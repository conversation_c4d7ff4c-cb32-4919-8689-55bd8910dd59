<?php

namespace Database\Factories\Qa;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Qa\Question>
 */
class QuestionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => 1,
            'title' => Str::random(10),
            'content' => Str::random(10),
            'anonymous' => 1,
            'answer_count' => 1,
            'status' => 1,
            'recommend_at' => Carbon::now()->toDateTimeString()
        ];
    }
}
