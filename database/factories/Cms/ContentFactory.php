<?php

namespace Database\Factories\Cms;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Cms\Content>
 */
class ContentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'category_id' => 6,
            'title' => "大宝猴的测试4",
            'type' => 4,
            'cover' => Str::random(10),
            'intro' => "大宝猴的测试4",
            'view_limit' => 0,
            'charge_credit' => 0,
            'charge_amount' => 0,
            'status_desc' => Str::random(10),
            'views' => 1,
            'source' => Str::random(10),
            'release_at' => Carbon::now()->toDateTimeString(),
            'recommend_at' => Carbon::now()->toDateTimeString()
        ];
    }
}
