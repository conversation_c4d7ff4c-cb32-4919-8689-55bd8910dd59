<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('experts', function (Blueprint $table) {
            $table->text('extra_text')->nullable()->comment("额外字段，用于存工作年限，擅长领域，安全服务方向")->after('remark');
        });
        // Blueprint 不支持 WITH PARSER ngram，所以这里必须用 DB::statement() 执行原生 SQL
        DB::statement("
        ALTER TABLE experts
            ADD FULLTEXT INDEX ft_all (
                name,
                residence,
                education,
                occupation,
                industry,
                safety_work_experience,
                course_scopes,
                typical_cases,
                serve_customers,
                teaching_styles,
                extra_text
              ) WITH PARSER ngram
        ");
  }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE experts DROP INDEX ft_all");
    }
};
