<?php

use App\Models\Cms\ContentCourseProgress;
use App\Models\Cms\ContentCourseSection;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 此表为 cms_conetnt_course_progresses 的⽗表，代表学员在指定⼀个课程中的整体状态，⽐如是否学完，已学有效学时，已学总时⻓
     */
    public function up(): void
    {
        Schema::table('cms_content_course_progresses', function (Blueprint $table) {
            $table->unsignedInteger('progs_status_id')->default(0)->comment('⽤⼾课程进度ID')->after('id');
            $table->unsignedInteger('valid_duration')->default(0)->after('duration')->comment('有效观看时⻓（秒）');
        });

        // 填充 valid_duration 值
        ContentCourseProgress::query()->chunkById(100, function ($tmpProgresses) {
            $grouped = $tmpProgresses->groupBy('section_id');

            foreach ($grouped as $sectionID => $progresses) {
                $section = ContentCourseSection::query()->with('video')->where('id', $sectionID)->first();
                $sectionDuration = $section->actual_duration;

                // 参考 orgCourseProgress 方法逻辑
                foreach ($progresses as $progress) {
                    if ($progress->finished) {
                        $progress->valid_duration = $sectionDuration;

                    } else if ($progress->duration > 0) {
                        $progress->valid_duration = $progress->duration >= $sectionDuration ? $sectionDuration - 1 : $progress->duration;

                    } else {
                        $progress->valid_duration = 0;
                    }
                    $progress->save();
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_content_course_progresses', function (Blueprint $table) {
            $table->dropColumn(['progs_status_id']);
            $table->dropColumn(['valid_duration']);
        });

        // Schema::table('cms_content_course_progresses', function (Blueprint $table) {
        //     $table->unsignedInteger('content_id')->default(0)->index('idx_content_id')->after('user_id')->comment('内容ID');
        //     $table->unsignedInteger('chapter_id')->default(0)->after('content_id')->comment('章ID');
        //     $table->unsignedInteger('org_id')->default(0)->comment('机构ID')->after('section_id');
        //     $table->unsignedInteger('enroll_id')->default(0)->comment('报名表ID')->after('org_id');

        //     $table->index('org_id', 'org_id');
        //     $table->index('enroll_id', 'enroll_id');
        // });
    }
};
