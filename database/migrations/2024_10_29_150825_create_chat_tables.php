<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_sessions', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->string('model', 50)->comment('模型');
            $table->string('title', 50)->comment('标题');
            $table->timestamps();

            $table->index(['user_id'], 'user_id');
        });

        Schema::create('chat_messages', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('session_id');
            $table->text('prompt')->comment('提示词');
            $table->text('completion')->comment('结论');
            $table->unsignedTinyInteger('like')->default(0)->comment('赞 0：无 1：赞 2：踩');
            $table->timestamps();

            $table->index(['user_id'], 'user_id');
            $table->index(['session_id'], 'session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_sessions');
        Schema::dropIfExists('chat_messages');
    }
};
