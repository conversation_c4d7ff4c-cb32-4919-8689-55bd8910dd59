<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('punish_topics', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 20)->comment('名称');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->comment('行政自测题库');
            $table->timestamps();
        });

        Schema::create('punish_subjects', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('topic_id');
            $table->string('intro', 320)->comment("违法行为");
            $table->string('statute', 1500)->comment("法律法规");
            $table->string('punishment', 1500)->comment("处罚依据");
            $table->string('remark')->default('')->comment("备注");
            $table->comment('行政自测题目');
            $table->timestamps();
        });
        Schema::create('punish_subject_options', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('subject_id');
            $table->string('name')->comment('适用条件');
            $table->string('level', 1)->comment('载量阶次');
            $table->string('standard', 1500)->comment('具体标准');
            $table->unsignedInteger('amount')->default(0)->comment('罚款金额');
            $table->comment('行政自测选项');
            $table->timestamps();
        });
        Schema::create('punish_tests', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('topic_id');
            $table->unsignedInteger('amount_tol');
            $table->comment('行政自测考试');
            $table->timestamps();
        });
        Schema::create('punish_test_subjects', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('topic_id');
            $table->unsignedInteger('test_id');
            $table->unsignedInteger('subject_id');
            $table->unsignedInteger('option_id');
            $table->comment('行政自测考试选项');
            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('punish_topics');
        Schema::dropIfExists('punish_subjects');
        Schema::dropIfExists('punish_subject_options');
        Schema::dropIfExists('punish_tests');
        Schema::dropIfExists('punish_test_subjects');
    }
};
