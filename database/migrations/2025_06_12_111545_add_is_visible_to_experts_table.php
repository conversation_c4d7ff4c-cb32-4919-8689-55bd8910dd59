<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('experts', function (Blueprint $table) {
            $table->tinyInteger('is_visible')->default(1)->after('status')->comment('显隐状态: 1-显示, 0-隐藏');
            $table->unsignedSmallInteger('sort')->default(0)->after('is_visible')->comment('排序-越大越靠前');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('experts', function (Blueprint $table) {
            $table->dropColumn('is_visible');
            $table->dropColumn('sort');
        });
    }
};