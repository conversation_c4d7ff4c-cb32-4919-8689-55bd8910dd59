<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE `org_exports` MODIFY COLUMN `type` enum(
            'hour_cert',
            'student_archive',
            'hour_record',
            'test_paper',
            'org_enrollment',
            'org_enrollment_form',
            'org_download_pack'
        ) COMMENT '导出类型'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE `org_exports` MODIFY COLUMN `type` enum(
            'hour_cert',
            'student_archive',
            'hour_record',
            'test_paper',
            'org_enrollment',
            'org_enrollment_form'
        ) COMMENT '导出类型'");
    }
};
