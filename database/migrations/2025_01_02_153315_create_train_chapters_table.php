<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('train_chapters', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('topic_id');
            $table->string('name', 30);
            $table->unsignedTinyInteger('example')->default(0)->comment('案例章 0 否，1 是，代表下面节都是案例');
            $table->timestamps();

            $table->index(['topic_id'], 'topic_id');
        });

        Schema::create('train_sections', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('topic_id');
            $table->unsignedInteger('chapter_id');
            $table->string('name', 30);
            $table->timestamps();

            $table->index(['chapter_id'], 'chapter_id');
        });

        Schema::create('train_examples', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('topic_id');
            $table->unsignedInteger('chapter_id');
            $table->unsignedInteger('section_id');
            $table->text('content');
            $table->timestamps();

            $table->index(['chapter_id'], 'chapter_id');
            $table->index(['section_id'], 'section_id');
        });

        Schema::table('train_subjects', function (Blueprint $table) {
            $table->unsignedInteger('chapter_id')->default(0)->after('topic_id');
            $table->unsignedInteger('section_id')->default(0)->after('chapter_id');
            $table->unsignedInteger('example_id')->default(0)->after('section_id');
            $table->text('answer')->nullable()->comment('问答题的答案')->after('judge_correct');

            $table->index(['chapter_id'], 'chapter_id');
            $table->index(['section_id'], 'section_id');
        });

        Schema::table('train_tests', function (Blueprint $table) {
            $table->unsignedInteger('chapter_id')->default(0)->after('topic_id');
            $table->unsignedInteger('subject_ignore_count')->default(0)->comment('代表不评分题数')->after('subject_correct_count');

            $table->index(['chapter_id'], 'chapter_id');
        });

        Schema::table('train_test_subjects', function (Blueprint $table) {
            $table->text('answer')->nullable()->comment('用户填写的答案')->after('correct');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('train_chapters');
        Schema::dropIfExists('train_sections');
        Schema::dropIfExists('train_examples');

        Schema::table('train_subjects', function (Blueprint $table) {
            $table->dropColumn(['chapter_id', 'section_id', 'example_id', 'answer']);
        });

        Schema::table('train_tests', function (Blueprint $table) {
            $table->dropColumn(['chapter_id', 'subject_ignore_count']);
        });

        Schema::table('train_test_subjects', function (Blueprint $table) {
            $table->dropColumn(['answer']);
        });
    }
};
