<?php

use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseProgress;
use App\Models\Cms\ContentCourseStatus;
use App\Services\Cms\ContentCourseProgressService;
use App\Services\Org\CourseService;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cms_content_course_status', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('org_id')->default(0)->comment('机构ID');
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('content_id');
            $table->unsignedInteger('enroll_id')->default(0)->comment('机构学习计划 ID');
            $table->unsignedInteger('duration')->default(0)->comment('学习总时⻓');
            $table->unsignedInteger('valid_duration')->default(0)->comment('学习有效时⻓');
            $table->unsignedTinyInteger('hour')->default(0)->comment('课程总已学有效学时');
            $table->unsignedTinyInteger('percent')->default(0)->comment('进度百分⽐,0-100');
            $table->tinyInteger('finished')->default(0)->comment('是否已学完');
            $table->timestamps();

            $table->index('user_id', 'user_id');
            $table->index('content_id', 'content_id');
            $table->index('enroll_id', 'enroll_id');

            $table->comment('⽤⼾课程进度');
        });

        // 先获取所有唯一的组合
        $uniqueCombinations = DB::table('cms_content_course_progresses')
            ->select('org_id', 'user_id', 'content_id', 'enroll_id')
            ->distinct()
            ->get();

        // 分批处理组合
        foreach ($uniqueCombinations->chunk(100) as $chunk) {
            foreach ($chunk as $combination) {
                // 获取该组合的所有进度记录
                $chapterSections = CourseService::getResourceTree($combination->org_id, $combination->content_id);
                if (empty($chapterSections)) {
                    continue;
                }

                $progresses = ContentCourseProgressService::getCourseProgress($combination->user_id, $combination->content_id, $combination->org_id, $combination->enroll_id);
                if ($progresses->isEmpty()) {
                    continue;
                }

                $validDuration = 0;
                $duration = 0;
                $finished = 1;

                // 过滤掉非可见章节记录
                foreach ($chapterSections as $chapter) {
                    foreach ($chapter['children'] as $section) {
                        /** @var ContentCourseProgress|null $progress */
                        $progress = $progresses->get($section['id']);

                        if (!$progress) {
                            continue;
                        }

                        $validDuration += $progress->valid_duration;
                        $duration += $progress->duration;
                        $finished = min($finished, $progress->finished);
                    }
                }

                $contentCourse = ContentCourse::query()->where('content_id', $combination->content_id)->first();
                list($courseHour, $courseDuration) = CourseService::getCourseHourAndDuration($contentCourse, $combination->org_id);
                list($studyHour, $studyPercent) = $contentCourse->studyHourForPercent($validDuration, $courseHour);

                /** 更新课程学习进度 */
                $progsStatus = ContentCourseStatus::query()->firstOrCreate([
                    'org_id' => $combination->org_id,
                    'user_id' => $combination->user_id,
                    'content_id' => $combination->content_id,
                    'enroll_id' => $combination->enroll_id,
                ], [
                    'finished' => $finished,
                    'duration' => $duration,
                    'valid_duration' => $validDuration,
                    'hour' => (int)$studyHour,
                    'percent' => $studyPercent,
                ]);

                // 首次创建, 更新下progress的关联id
                if ($progsStatus->wasRecentlyCreated) {
                    $ids = $progresses->pluck('id')->all();
                    ContentCourseProgress::whereIn('id', $ids)->update(['progs_status_id' => $progsStatus->id]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cms_content_course_status');
    }
};
