<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orgs', function (Blueprint $table) {
            $table->string('official_seal_image')
                ->default('')
                ->comment('公章图片')
                ->after('service_qrcode');
        });

        DB::statement("ALTER TABLE `org_exports` MODIFY COLUMN `type` enum(
            'hour_cert',
            'student_archive',
            'hour_record',
            'test_paper',
            'org_enrollment',
            'org_enrollment_form'
        ) COMMENT '导出类型'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orgs', function (Blueprint $table) {
            $table->dropColumn('official_seal_image');
        });

        DB::statement("ALTER TABLE `org_exports` MODIFY COLUMN `type` enum(
            'hour_cert',
            'student_archive',
            'hour_record',
            'test_paper',
            'org_enrollment'
        ) COMMENT '导出类型'");
    }
};
