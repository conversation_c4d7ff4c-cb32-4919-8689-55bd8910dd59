<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('org_classes', function (Blueprint $table) {
            $table->timestamp('actual_end_at')
                  ->nullable()
                  ->after('end_at')
                  ->comment('实际结束时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('org_classes', function (Blueprint $table) {
            $table->dropColumn('actual_end_at');
        });
    }
};