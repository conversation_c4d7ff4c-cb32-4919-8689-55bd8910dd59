<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_content_courses', function (Blueprint $table) {
            $table->unsignedInteger('hour_per_minutes')->comment('一课时分钟数')->after('topic_id')->default(60);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_content_courses', function (Blueprint $table) {
            $table->dropColumn(['hour_per_minutes']);
        });
    }
};
