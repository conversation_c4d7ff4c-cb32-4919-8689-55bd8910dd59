<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('org_enrollments', function (Blueprint $table) {
            $table->timestamp('exam_pass_at')->nullable()->after('exam_passed')->comment('考试通过时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('org_enrollments', function (Blueprint $table) {
            $table->dropColumn('exam_pass_at');
        });
    }
};