<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inspect_hidden_dangers', function (Blueprint $table) {
            $table->id();
            $table->string('question')->nullable()->comment('隐患描述');
            $table->string('suggestion')->nullable()->comment('隐患建议');
            $table->json('images')->nullable()->comment('隐患图片');
            $table->unsignedTinyInteger('is_public')->default(0)->comment('是否公开,0:否,1:是');
            $table->unsignedInteger('amazed')->default(0)->comment('顶赞数量');
            $table->unsignedInteger('no_amazed')->default(0)->comment('踩赞数量');
            $table->unsignedInteger('user_id')->index('idx_user_id')->default(0)->comment('发布者ID');
            $table->unsignedInteger('approver_id')->default(0)->comment('审批人ID');
            $table->unsignedTinyInteger('approver_status')->nullable()->comment('审批状态,0=暂不处理,1=已处理');
            $table->timestamp('approver_at')->nullable()->comment('审批时间');
            $table->softDeletes();
            $table->timestamps();
            $table->comment('隐患记录表');
        });

        Schema::create('inspect_clues', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('target_id')->default(0)->comment('来源ID');
            $table->unsignedInteger('target_type')->default(1)->comment('来源类型, 1隐患线索');
            $table->string('name', 20)->nullable()->comment('姓名');
            $table->string('phone', 11)->nullable()->comment('手机');
            $table->string('company', 50)->nullable()->comment('单位名称');
            $table->timestamps();
            $table->comment('线索表');
        });

        Schema::create('inspect_user_amazes', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->default(0)->comment('用户ID');
            $table->unsignedInteger('hidden_danger_id')->default(0)->comment('隐患记录ID');
            $table->unsignedTinyInteger('type')->default(1)->comment('顶踩类型 1 顶赞，2 踩赞');
            $table->timestamps();
            $table->comment('用户顶踩记录');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inspect_hidden_dangers');
        Schema::dropIfExists('inspect_clues');
        Schema::dropIfExists('inspect_user_amazes');
    }
};
