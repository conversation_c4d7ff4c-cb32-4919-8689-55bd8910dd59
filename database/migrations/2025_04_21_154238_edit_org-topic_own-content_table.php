<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_own_contents', function (Blueprint $table) {
            $table->unsignedInteger('org_id')->comment('机构id')->after('classify')->default(0);
            $table->unsignedInteger('enroll_id')->comment('学员id')->after('org_id')->default(0);
        });
        Schema::table('user_own_topics', function (Blueprint $table) {
            $table->unsignedInteger('org_id')->comment('机构id')->after('topic_id')->default(0);
            $table->unsignedInteger('enroll_id')->comment('学员id')->after('org_id')->default(0);
        });

        Schema::table('org_topics', function (Blueprint $table) {
            $table->decimal('price_original_30', 10)->unsigned()->after('price_sell')->default(0)->comment('原价30天');
            $table->decimal('price_sell_30', 10)->unsigned()->after('price_original_30')->default(0)->comment('售价30天');
            $table->decimal('price_original_60', 10)->unsigned()->after('price_sell_30')->default(0)->comment('原价30天');
            $table->decimal('price_sell_60', 10)->unsigned()->after('price_original_60')->default(0)->comment('售价60天');
        });

        \App\Models\Org\Topic::query()->chunkById(100, function ($orgTopics) {
            foreach ($orgTopics as $orgTopic) {
                $orgTopic->price_original_30 = $orgTopic->price_original;
                $orgTopic->price_sell_30 = $orgTopic->price_sell;

                $orgTopic->price_original_60 = $orgTopic->price_original_30 * 2;
                $orgTopic->price_sell_60 = $orgTopic->price_sell_30 * 2;
                $orgTopic->save();
            }
        });



        Schema::table('org_topics', function (Blueprint $table) {
            $table->dropColumn(['price_original', 'price_sell']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_own_contents', function (Blueprint $table) {
            $table->dropColumn(['org_id', 'enroll_id']);
        });

        Schema::table('user_own_topics', function (Blueprint $table) {
            $table->dropColumn(['org_id', 'enroll_id']);
        });

        Schema::table('org_topics', function (Blueprint $table) {
            $table->decimal('price_original', 10)->unsigned()->after('status')->default(0)->comment('原价');
            $table->decimal('price_sell', 10)->unsigned()->after('price_original')->default(0)->comment('售价');
        });

        Schema::table('org_topics', function (Blueprint $table) {
            $table->dropColumn(['price_original_30', 'price_sell_30', 'price_original_60', 'price_sell_60']);
        });
    }
};
