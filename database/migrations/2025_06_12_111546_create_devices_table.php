<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 设备表
        Schema::create('inspect_devices', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('org_id')->default(0)->comment('关联组织ID');
            $table->string('name', 100)->comment('设备名称');
            $table->smallInteger('inspection_item_count')->default(0)->comment('巡检项数量');
            $table->string('image_url')->nullable()->comment('图片地址');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamps();

            $table->comment('设备表');
        });

        // 设备巡检项表
        Schema::create('inspect_device_items', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('device_id')->index('idx_device_id')->comment('关联设备ID');
            $table->string('name', 100)->comment('巡检项名称');
            $table->timestamps();

            $table->comment('设备巡检项表');
        });

        // 任务表
        Schema::create('inspect_tasks', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->string('name', 100)->comment('任务名称');
            $table->unsignedInteger('device_count')->default(0)->comment('设备数量');
            $table->unsignedInteger('frequency')->comment('任务频率');
            $table->timestamps();

            $table->comment('任务表');
        });

        // 任务设备关联表
        Schema::create('inspect_task_devices', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('task_id')->index('idx_task_id')->comment('关联任务ID');
            $table->unsignedInteger('device_id')->index('idx_device_id')->comment('关联设备ID');
            $table->timestamps();

            $table->comment('任务设备关联表');
        });

        // 任务设备记录表
        Schema::create('inspect_task_devices_records', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('task_id')->index('idx_task_id')->comment('关联任务ID');
            $table->unsignedInteger('device_id')->comment('关联设备ID');
            $table->timestamp('checked_at')->comment('巡检时间:根据任务频率计算, 目前具体到天即可');
            $table->json('image_url')->nullable()->comment('现场图片'); // 这里存多图情况
            $table->unsignedTinyInteger('status')->default(0)->comment('状态:0=待巡检,1=已巡检,2=待审批,3=已审批');
            $table->text('question')->nullable()->comment('问题描述');
            $table->text('suggestion')->nullable()->comment('处理建议');
            $table->unsignedInteger('approver_id')->default(0)->comment('审批人ID');
            $table->unsignedTinyInteger('approver_status')->default(0)->comment('审批状态:0=暂不处理,1=已处理');
            $table->timestamp('approver_at')->nullable()->comment('审批时间');
            $table->timestamps();

            $table->comment('任务设备记录表');
        });

        // 巡检项记录表
        Schema::create('inspect_device_items_records', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('task_devices_record_id')->index('idx_task_devices_record_id')->comment('关联任务设备记录表ID');
            $table->unsignedInteger('item_id')->comment('关联巡检项ID');
            $table->unsignedTinyInteger('status')->default(0)->comment('检测状态:0=异常,1=正常');

            $table->comment('巡检项记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inspect_devices');
        Schema::dropIfExists('inspect_device_items');
        Schema::dropIfExists('inspect_tasks');
        Schema::dropIfExists('inspect_task_devices');
        Schema::dropIfExists('inspect_task_devices_records');
        Schema::dropIfExists('inspect_device_items_records');
    }
};