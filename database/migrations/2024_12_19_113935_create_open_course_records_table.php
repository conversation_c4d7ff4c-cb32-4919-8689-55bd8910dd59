<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('open_course_batches', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedSmallInteger('count')->comment('开通人数');
            $table->unsignedInteger('admin_id')->comment('操作人');
            $table->timestamp('created_at')->comment('创建时间');

            $table->index('admin_id', 'idx_admin_id');

            $table->comment('开通课程批次');
        });

        Schema::create('open_course_records', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('batch_id')->comment('批次ID');
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->json('course_ids')->comment('课程ID');
            $table->json('topic_ids')->comment('题库ID');
            $table->timestamp('created_at')->comment('创建时间');

            $table->index('user_id', 'idx_user_id');
            $table->index('batch_id', 'idx_batch_id');

            $table->comment('开通课程记录');
        });

        DB::statement('ALTER TABLE open_course_records ADD INDEX course_ids ((CAST(course_ids->"$[*]" AS UNSIGNED ARRAY)));');
        DB::statement('ALTER TABLE open_course_records ADD INDEX topic_ids ((CAST(topic_ids->"$[*]" AS UNSIGNED ARRAY)));');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('open_course_records');
        Schema::dropIfExists('open_course_batches');
    }
};
