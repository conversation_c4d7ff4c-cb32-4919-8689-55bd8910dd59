<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            ALTER TABLE `org_templates`
            MODIFY COLUMN `type`
            ENUM('hour_cert','student_archive','hour_record','org_enrollment_form')
            CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
            NOT NULL COMMENT '模板类别';
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("
            ALTER TABLE `org_templates`
            MODIFY COLUMN `type`
            ENUM('hour_cert','student_archive','hour_record')
            CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
            NOT NULL COMMENT '模板类别';
        ");
    }
};
