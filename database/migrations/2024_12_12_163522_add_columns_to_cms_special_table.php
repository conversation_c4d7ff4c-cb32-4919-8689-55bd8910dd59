<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_specials', function (Blueprint $table) {
            $table->unsignedInteger('admin_id')->default(0)->after('recommend_at');

            $table->index(['admin_id'], 'admin_id');
        });

        Schema::table('stat_daily_promoters', function (Blueprint $table) {
            $table->unsignedInteger('consume_credit_content')->default(0)->comment('内容消耗的积分数量')->after('consume_credit');
            $table->unsignedInteger('consume_credit_special')->default(0)->comment('专题消耗的积分数量')->after('consume_credit_content');
            $table->unsignedInteger('content_special')->default(0)->comment('专题数量')->after('content_download');
        });

        Schema::table('stat_daily_overviews', function (Blueprint $table) {
            $table->unsignedInteger('content_special')->default(0)->comment('专题数量')->after('content_download');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_specials', function (Blueprint $table) {
            $table->dropColumn(['admin_id']);
        });

        Schema::table('stat_daily_promoters', function (Blueprint $table) {
            $table->dropColumn(['consume_credit_content', 'consume_credit_special', 'content_special']);
        });

        Schema::table('stat_daily_overviews', function (Blueprint $table) {
            $table->dropColumn(['content_special']);
        });
    }
};
