<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cms_content_course_progress_buffer', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->unsignedInteger('org_id')->default(0)->comment('机构ID');
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->unsignedInteger('content_id')->comment('内容ID');
            $table->unsignedInteger('enroll_id')->default(0)->comment('报名表ID');
            $table->unsignedInteger('section_id')->comment('章节ID');
            $table->unsignedInteger('update_count')->default(0)->comment('更新次数');
            $table->timestamps();
            
            $table->index(['org_id', 'user_id', 'content_id', 'enroll_id'], 'idx_org_user_content_enroll');

            $table->comment('学习进度缓冲表');
        });
    }

    /**
     * 回滚迁移
     */
    public function down()
    {
        Schema::dropIfExists('cms_content_course_progress_buffer');
    }
};
