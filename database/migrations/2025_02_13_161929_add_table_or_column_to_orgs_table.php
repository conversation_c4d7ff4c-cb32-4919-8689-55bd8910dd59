<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 机构表 (orgs) 新增字段
        Schema::table('orgs', function (Blueprint $table) {
            $table->after('name', function (Blueprint $table) {
                $table->string('alias', 64)->default('')->comment('别名，简称');
                $table->string('domain', 64)->nullable()->unique('idx_domain_unique')->comment('URL中自定义ID');
                $table->string('logo')->default('')->comment('logo图路径');
                $table->enum('verified_status', ['pending', 'verified', 'rejected'])
                    ->default('pending')
                    ->index('idx_verified_status')
                    ->comment('认证状态');
                $table->unsignedInteger('total_students')->default(0)->comment('学员总数');
                $table->unsignedInteger('total_classes')->default(0)->comment('开班总数');
                $table->unsignedInteger('total_trained')->default(0)->comment('培训总数');
                $table->unsignedDecimal('balance', 10)->default(0.00)->comment('余额');
                $table->string('business_license')->default('')->comment('营业执照');
                $table->text('business_scope')->nullable()->comment('业务范围');
                $table->string('contact', 20)->default('')->comment('联系方式');
                $table->string('service_qrcode')->default('')->comment('客服二维码图片');
                $table->unsignedMediumInteger('area_code')->default(0)->comment('所在地区码');
                $table->string('area_text')->default('')->comment('所在地区名字');
                $table->timestamp('verified_at')->nullable()->comment('认证时间');
            });
        });

        // 机构余额记录 (org_balance_records)
        Schema::create('org_balance_records', function (Blueprint $table) {
            $table->comment('机构余额记录表');
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->enum('type', ['expense', 'income', 'refund'])->comment('消费类型');
            $table->decimal('origin_balance', 10)->comment('原始金额');
            $table->decimal('amount', 10)->comment('变动金额');
            $table->unsignedInteger('enroll_id')->default(0)->comment('学员ID');
            $table->tinyInteger('status')->default(1)->comment('1=正常，2=已退款');
            $table->unsignedInteger('ref_id')->default(0)->comment('关联记录 ID');
            $table->string('remark')->default('')->comment('描述');
            $table->timestamps();

            // 添加索引
            $table->index('org_id', 'idx_org_id');
            $table->index('type', 'idx_type');
            $table->index('enroll_id', 'idx_enroll_id');
        });

        // 机构学员 (org_enrollments）
        Schema::create('org_enrollments', function (Blueprint $table) {
            $table->comment('机构学员表');
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('所属机构ID');
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->string('name', 64)->comment('姓名');
            $table->string('phone', 20)->comment('手机号码');
            $table->string('id_card_front')->comment('身份证正面图');
            $table->string('id_card_back')->comment('身份证反面图');
            $table->char('id_card_number', 18)->comment('身份证号');
            $table->string('photo')->default('')->comment('照片');
            $table->unsignedInteger('class_id')->default(0)->comment('所在班级ID');

            // 课程相关
            $table->enum('type', ['course', 'topic'])->comment('学习类型'); // 课程/题库
            $table->unsignedInteger('resource_id')->default(0)->comment('资源ID');

            // 时间相关
            $table->timestamp('started_at')->nullable()->comment('开课时间');
            $table->timestamp('expired_at')->nullable()->comment('到期时间');

            // 扩展信息
            // type - 与报名表 fields type 一致
            // id - 与报名表 fields id 关联
            // name - 名称
            // value - 填写的值或选择的选项列表，如果是图片或者签名则是图片的路径
            $table->json('extra')->nullable()->comment('报名扩展信息');

            // 状态和进度
            $table->unsignedTinyInteger('status')->default(0)->comment('状态:0=待开课,1=学习中,2=已完成,3=已过期,4=未完成');
            $table->tinyInteger('latest')->default(0)->comment('是否最新个人信息 0 否，1 是');
            $table->unsignedInteger('learned_duration')->default(0)->comment('已学学时（秒数）');
            $table->tinyInteger('learn_finished')->default(0)->comment('是否已学完');
            $table->boolean('exam_taken')->default(false)->comment('是否已考试');
            $table->unsignedTinyInteger('exam_score')->default(0)->comment('考试最高成绩');
            $table->tinyInteger('exam_passed')->default(0)->comment('考试是否通过');
            $table->tinyInteger('exam_retake')->default(0)->comment('补考状态:0=不能补考,1=允许补考,2=已补考');
            $table->unsignedInteger('exam_latest_id')->default(0)->comment('考试记录ID');
            $table->unsignedTinyInteger('exam_count')->default(0)->comment('考试次数');
            $table->string('hour_cert', 255)->default('')->comment('学时证明图片');

            $table->timestamps();

            $table->index('org_id', 'idx_org_id');
            $table->index('user_id', 'idx_user_id');
            $table->index('class_id', 'idx_class_id');
            $table->index('status', 'idx_status');
            $table->index(['started_at', 'expired_at'], 'idx_start_expire');
            $table->index('phone', 'idx_phone');
            $table->index('id_card_number', 'idx_id_card_number');
        });

        // 机构班级 (org_classes)
        Schema::create('org_classes', function (Blueprint $table) {
            $table->comment('机构班级表');
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->string('name', 64)->comment('班级名称');
            $table->unsignedInteger('manager_id')->comment('子账号/老师ID');
            $table->enum('type', ['course', 'topic'])->comment('班级类型');
            $table->unsignedInteger('resource_id')->comment('课程或题库ID');
            $table->unsignedTinyInteger('status')->comment('状态');

            // 统计数据
            $table->integer('total_enrollments')->default(0)->comment('培训人数');
            $table->integer('total_course_finished')->default(0)->comment('学时完成人数');
            $table->integer('total_examined')->default(0)->comment('考试人数');
            $table->integer('total_passed')->default(0)->comment('及格人数');

            // 考试相关
            $table->boolean('exam_enabled')->default(false)->comment('是否考试');
            $table->boolean('exam_limit')->default(false)->comment('是否限制考试次数');
            $table->integer('exam_limit_count')->default(1)->comment('限考次数');
            $table->enum('exam_condition', ['anytime', 'after_course', 'fixed_time'])
                ->default('anytime')
                ->comment('考试条件');
            $table->enum('exam_mode', ['all', 'mobile_only', 'pc_only'])
                ->default('all')
                ->comment('考试方式');
            $table->timestamp('exam_at')->nullable()->comment('考试时间');

            // 人脸识别
            $table->boolean('face_capture_enabled')->default(false)->comment('是否开启人脸抓拍');
            $table->integer('face_capture_count')->default(0)->comment('人脸抓拍次数');

            // 模板相关
            $table->boolean('template_custom')->default(false)->comment('是否自定义下载模板');
            $table->unsignedInteger('template_hour_id')->default(0)->comment('学时证明模板ID');
            $table->unsignedInteger('template_archive_id')->default(0)->comment('一期一档模板ID');

            // 时间相关
            $table->timestamp('start_at')->nullable()->comment('开始时间');
            $table->timestamp('end_at')->nullable()->comment('结束时间');
            $table->timestamps();

            // 添加索引
            $table->index('org_id', 'idx_org_id');
            $table->index('manager_id', 'idx_manager_id');
            $table->index('status', 'idx_status');
            $table->index(['type', 'resource_id'], 'idx_type_res_id');
            $table->index(['start_at', 'end_at'], 'idx_start_end');
        });

        // 机构课程（org_courses）
        Schema::create('org_courses', function (Blueprint $table) {
            $table->comment('机构课程表');
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->unsignedInteger('course_id')->comment('课程ID');
            $table->unsignedTinyInteger('hour')->default(0)->comment('学时');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态:0=隐藏,1=显示');
            $table->decimal('price_original', 10)->default(0)->comment('原价');
            $table->decimal('price_sell', 10)->default(0)->comment('售价');
            $table->timestamps();

            $table->index('org_id', 'idx_org_id');
            $table->index('course_id', 'idx_course_id');
            $table->index('status', 'idx_status');
        });

        // 机构课程章节（org_course_subs）
        Schema::create('org_course_subs', function (Blueprint $table) {
            $table->comment('机构课程章节表');
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->unsignedInteger('course_id')->comment('课程ID');
            $table->enum('type', ['chapter', 'section'])->comment('类型:章或节');
            $table->unsignedInteger('resource_id')->comment('章或节ID');
            $table->timestamps();

            $table->index('org_id', 'idx_org_id');
            $table->index('course_id', 'idx_course_id');
            $table->index('resource_id', 'idx_resource_id');
            $table->index(['type', 'resource_id'], 'idx_type_resource');
        });

        // 机构题库 （org_topics）
        Schema::create('org_topics', function (Blueprint $table) {
            $table->comment('机构题库表');
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->unsignedInteger('topic_id')->comment('题库ID');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态:0=隐藏,1=显示');
            $table->decimal('price_original', 10)->default(0)->comment('原价');
            $table->decimal('price_sell', 10)->default(0)->comment('售价');
            $table->unsignedTinyInteger('exam_time')->default(60)->comment('考试时间');
            $table->unsignedTinyInteger('pass_score')->default(60)->comment('及格分数');
            $table->json('exam_config')->nullable()->comment('考试配置');
            $table->timestamps();

            $table->index('org_id', 'idx_org_id');
            $table->index('topic_id', 'idx_topic_id');
            $table->index('status', 'idx_status');
        });

        Schema::table('train_topics', function (Blueprint $table) {
            $table->unsignedTinyInteger('exam_time')->default(60)->comment('考试时间')->after('amount');
            $table->unsignedTinyInteger('pass_score')->default(60)->comment('及格分数')->after('exam_time');
            $table->json('exam_config')->nullable()->comment('考试配置')->after('pass_score');
        });

        // 机构模板（org_templates）
        Schema::create('org_templates', function (Blueprint $table) {
            $table->comment('机构模板表');
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->string('name', 128)->comment('模板名称');
            $table->enum('type', ['hour_cert', 'student_archive', 'hour_record'])->comment('模板类别');
            // 每个机构，每个类别只能有一个默认
            $table->boolean('is_default')->default(false)->comment('是否默认');
            $table->string('tpl_path')->comment('模板文件路径');
            $table->enum('format', ['word', 'excel', 'pdf'])->nullable()->comment('下载格式');
            $table->timestamps();

            $table->index('org_id', 'idx_org_id');
            $table->index('type', 'idx_type');
        });

        // 机构报名表单配置表（org_enrollment_forms)
        Schema::create('org_enrollment_forms', function (Blueprint $table) {
            $table->comment('机构报名表单配置表');
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->string('title')->comment('报名标题');
            // type - 类型
            //    text - 单行文件
            //    textarea - 多行文本
            //    radio - 单选
            //    checkbox - 多选
            //    select - 单选下拉框
            //    datetime - 日期时间
            //    region - 地区选择
            //    sign - 签名（承诺书等）
            //    photo - 形象照（picture 的别名，带有固定的 placeholder）
            //    pic - 单图上传
            //    multi-pic - 多图上传
            // name - 字段名
            // placeholder - 占位文字
            // desc - 描述，主要用于承诺书的内容，也可用于其它文字超出于 placeholder 之外的内容
            // id - 随机生成的字符串，（UUID 或 uniqid 等）用以和学员 extra 以及更新的关联
            // required - 是否必填
            // options - 单选或多选时的选项
            $table->json('fields')->comment('自定义字段配置');
            $table->timestamps();

            $table->index('org_id', 'idx_org_id');
        });

        // 学习抓拍记录（org_learn_captures）
        Schema::create('org_learn_captures', function (Blueprint $table) {
            $table->comment('学习抓拍记录表');
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->unsignedInteger('enroll_id')->comment('报名表ID');
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->unsignedInteger('course_id')->comment('课程ID');
            $table->unsignedInteger('section_id')->default(0)->comment('小节ID');
            $table->string('photo')->comment('照片文件路径');
            $table->timestamp('created_at')->nullable();

            // 添加带前缀的索引
            $table->index('org_id', 'idx_org_id');
            $table->index('enroll_id', 'idx_enroll_id');
            $table->index('user_id', 'idx_user_id');
            $table->index('course_id', 'idx_course_id');
        });

        // 导出任务表（org_exports）
        Schema::create('org_exports', function (Blueprint $table) {
            $table->comment('导出任务表');
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->enum('type', [
                'hour_cert',       // 学时证明
                'student_archive', // 一期一档
                'hour_record',     // 学习记录
                'test_paper',      // 考试试卷
                'org_enrollment',  // 机构报名
            ])->comment('导出类型');
            $table->string('desc')->default('')->comment('描述');
            $table->unsignedInteger('admin_id')->comment('操作人ID');
            $table->unsignedTinyInteger('status')->comment('状态:0=进行中,1=已完成,2=失败');
            $table->string('file')->default('')->comment('导出文件路径');
            $table->string('error')->default('')->comment('错误信息');
            $table->json('extra')->nullable()->comment('导出参数');
            $table->timestamps();

            $table->index('org_id', 'idx_org_id');
            $table->index('admin_id', 'idx_admin_id');
            $table->index('type', 'idx_type');
            $table->index('status', 'idx_status');
        });

        // 关联的题库（练习、考试）
        Schema::table('cms_content_courses', function (Blueprint $table) {
            $table->unsignedInteger('topic_id')->default(0)->comment('题库ID')->after('try_view_count');
        });

        Schema::create('stat_orgs', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('org_id');
            $table->date('date');
            $table->unsignedSmallInteger('enrollments')->default(0)->comment('新增学员数');
            $table->unsignedSmallInteger('classes')->default(0)->comment('新增班级数');
            $table->unsignedSmallInteger('exams')->default(0)->comment('考试次数');
            $table->unsignedSmallInteger('trained')->default(0)->comment('培训完成人数');
            $table->timestamps();

            $table->comment('机构每日统计表');

            $table->index('org_id', 'idx_org_id');
            $table->index('date', 'idx_date');
        });

        Schema::table('cms_content_course_progresses', function (Blueprint $table) {
            $table->unsignedInteger('org_id')->default(0)->comment('机构ID')->after('section_id');
            $table->unsignedInteger('enroll_id')->default(0)->comment('报名表ID')->after('org_id');
            $table->dropIndex('idx_chapter_id');
            $table->index('org_id', 'org_id');
            $table->index('enroll_id', 'enroll_id');
        });

        Schema::table('train_tests', function (Blueprint $table) {
            $table->unsignedInteger('org_id')->default(0)->comment('机构ID')->after('type');
            $table->unsignedInteger('enroll_id')->default(0)->comment('报名表ID')->after('org_id');
            $table->tinyInteger('passed')->default(0)->comment('是否已通过')->after('score');
            $table->index('org_id', 'org_id');
            $table->index('enroll_id', 'enroll_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('train_tests', function (Blueprint $table) {
            $table->dropColumn(['org_id', 'enroll_id', 'passed']);
        });

        Schema::table('cms_content_course_progresses', function (Blueprint $table) {
            $table->dropColumn(['org_id', 'enroll_id']);
            $table->index('chapter_id', 'idx_chapter_id');
        });

        Schema::dropIfExists('stat_orgs');

        Schema::table('orgs', function (Blueprint $table) {
            $table->dropColumn([
                'alias',
                'domain',
                'logo',
                'verified_status',
                'verified_at',
                'total_students',
                'total_classes',
                'total_trained',
                'balance',
                'business_license',
                'business_scope',
                'contact',
                'service_qrcode',
                'area_code',
                'area_text'
            ]);
        });

        Schema::dropIfExists('org_balance_records');

        Schema::dropIfExists('org_enrollments');

        Schema::dropIfExists('org_classes');

        Schema::dropIfExists('org_courses');

        Schema::dropIfExists('org_course_subs');

        Schema::dropIfExists('org_topics');

        Schema::dropIfExists('org_templates');

        Schema::dropIfExists('org_enrollment_forms');

        Schema::dropIfExists('org_learn_captures');

        Schema::dropIfExists('org_exports');

        Schema::table('cms_content_courses', function (Blueprint $table) {
            $table->dropColumn('topic_id');
        });

        Schema::table('train_topics', function (Blueprint $table) {
            $table->dropColumn(['exam_time', 'pass_score', 'exam_config']);
        });
    }
};
