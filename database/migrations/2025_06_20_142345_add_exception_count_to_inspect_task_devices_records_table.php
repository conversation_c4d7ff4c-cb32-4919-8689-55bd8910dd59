<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inspect_task_devices_records', function (Blueprint $table) {
            $table->integer('abnormal_count')->default(0)->after('status')->comment('异常巡检项数量');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inspect_task_devices_records', function (Blueprint $table) {
            $table->dropColumn('abnormal_count');
        });
    }
};