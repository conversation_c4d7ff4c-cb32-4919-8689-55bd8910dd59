<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('org_enrolls', function (Blueprint $table) {
            $table->boolean('is_invoiced')->default(false)->after('status')->comment('是否已开具发票：0=未开票，1=已开票');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('org_enrolls', function (Blueprint $table) {
            $table->dropColumn('is_invoiced');
        });
    }
};
