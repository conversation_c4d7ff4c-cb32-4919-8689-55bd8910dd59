<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_categories', function(Blueprint $table) {
            $table->unsignedTinyInteger('hot_position')->default(0)->comment('在热门分类中的位置')->after('sort')->index('hot_position');
        });

        Schema::table('cms_contents', function(Blueprint $table) {
            $table->unsignedInteger('admin_id')->default(0)->comment('创建或发布者')->index('admin_id')->after('recommend_at');
        });

        Schema::table('cms_content_course_sections', function(Blueprint $table) {
            $table->unsignedInteger('ref_video_id')->default(0)->comment('引用的视频ID')->after('chapter_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropColumns('cms_contents', 'admin_id');
        Schema::dropColumns('cms_categories', 'hot_position');
        Schema::dropColumns('cms_content_course_sections', 'ref_video_id');
    }
};
