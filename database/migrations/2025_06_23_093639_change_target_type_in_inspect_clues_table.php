<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inspect_clues', function (Blueprint $table) {
            $table->string('target_type')
                ->nullable()
                ->comment('来源类型，多态模型类名')
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inspect_clues', function (Blueprint $table) {
            $table->unsignedInteger('target_type')
                ->default(1)
                ->comment('来源类型, 1隐患线索')
                ->change();
        });
    }
};
