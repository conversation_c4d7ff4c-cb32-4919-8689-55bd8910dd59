<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ers_projects', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title', 50)->comment('标题');
            $table->unsignedSmallInteger('sort')->default(0)->comment('排序');
            $table->string('icon', 255)->default('')->comment('图标');
            $table->mediumText('intro')->nullable()->comment('介绍');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态 0 启用，1 停用');
            $table->unsignedTinyInteger('is_bind_category')->default(0)->comment('是否绑定类别');
            $table->unsignedInteger('flow_id')->comment('流程ID');
            $table->timestamps();

            $table->index('status', 'idx_status');

            $table->comment('项目');
        });

        Schema::create('ers_industries', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 50)->comment('名称');
            $table->unsignedTinyInteger('sort')->default(0)->comment('排序');
            $table->timestamps();

            $table->comment('行业类别');
        });

        Schema::create('ers_enterprise_categories', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('industry_id')->comment('所属行业')->index('industry_id');
            $table->string('name', 50)->comment('名称');
            $table->unsignedTinyInteger('sort')->default(0)->comment('排序');
            $table->timestamps();

            $table->comment('企业类别');
        });

        Schema::create('ers_flows', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 50)->comment('名称');
            $table->timestamps();

            $table->comment('流程');
        });

        Schema::create('ers_flow_steps', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('flow_id')->comment('流程ID');
            $table->string('name')->default('')->comment('模块在流程中的名称，不填则代表模块的默认名称');
            $table->string('module', 20)->comment('模块');
            $table->unsignedTinyInteger('step')->default(0)->comment('步骤，0开始');
            $table->timestamps();
            $table->softDeletes();

            $table->index('flow_id', 'idx_flow_id');

            $table->comment('流程步骤');
        });

        Schema::create('ers_form_project_forms', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('project_id')->comment('项目ID');
            $table->unsignedInteger('flow_id')->comment('流程ID');
            $table->unsignedInteger('step_id')->comment('步骤ID');
            $table->unsignedInteger('industry_id')->default(0)->comment('行业ID');
            $table->unsignedInteger('enterprise_id')->default(0)->comment('企业类别ID');
            $table->timestamps();

            $table->index('project_id', 'idx_project_id');
            $table->index('flow_id', 'idx_flow_id');
            $table->index('step_id', 'idx_step_id');

            $table->comment('项目表单');
        });

        Schema::create('ers_form_project_inputs', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('project_id')->comment('项目ID');
            $table->unsignedInteger('flow_id')->comment('流程ID');
            $table->unsignedInteger('step_id')->comment('步骤ID');
            $table->unsignedInteger('project_form_id')->comment('项目流程模块表单ID');
            $table->unsignedInteger('form_library_id')->default(0)->comment('引用基础组件库ID 0 代表非引用，非0代表引用，如果是引用，则项目内是只读状态，不能单独修改');
            $table->string('title', 50)->comment('标题');
            $table->enum('type', ['text', 'textarea', 'select', 'checkbox', 'image', 'file', 'group'])->comment('类型（不可更改）');
            $table->unsignedSmallInteger('sort')->default(0)->comment('排序');
            $table->unsignedTinyInteger('is_required')->default(0)->comment('是否必填');
            $table->string('desc', 255)->default('')->comment('描述');
            $table->json('options')->comment('限制或选项');
            $table->timestamps();
            $table->softDeletes();

            $table->index('project_id', 'idx_project_id');
            $table->index('flow_id', 'idx_flow_id');
            $table->index('step_id', 'idx_step_id');
            $table->index('project_form_id', 'idx_project_form_id');
            $table->index('form_library_id', 'idx_form_library_id');

            $table->comment('项目表单项');
        });

        Schema::create('ers_form_order_forms', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('order_id')->comment('工单ID');
            $table->unsignedInteger('project_form_id')->comment('项目表单ID');
            $table->unsignedInteger('order_step_id')->comment('工单步骤ID');
            $table->timestamps();

            $table->index('order_id', 'idx_order_id');
            $table->index('project_form_id', 'idx_project_form_id');
            $table->index('order_step_id', 'idx_order_step_id');

            $table->comment('工单表单');
        });

        Schema::create('ers_form_order_data', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('order_id')->comment('工单ID');
            $table->unsignedInteger('project_input_id')->comment('项目表单项ID');
            $table->unsignedInteger('order_form_id')->comment('工单表单ID');
            $table->json('data')->nullable()->comment('表单值');
            $table->tinyInteger('status')->default(0)->comment('字段状态：0=未提交，1=待审核，2=已通过，3=未通过');
            $table->string('reject_reason', 255)->default('')->comment('未通过理由');
            $table->timestamps();

            $table->index('order_id', 'idx_order_id');
            $table->index('project_input_id', 'idx_project_input_id');
            $table->index('order_form_id', 'idx_order_form_id');

            $table->comment('工单表单项数据');
        });

        Schema::create('ers_form_libraries', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title', 50)->comment('标题');
            $table->enum('type', ['text', 'textarea', 'select', 'checkbox', 'image', 'file', 'group'])->comment('类型（不可更改）');
            $table->unsignedInteger('is_required')->default(0)->comment('是否必填');
            $table->string('desc', 255)->default('')->comment('描述');
            $table->json('options')->comment('限制或选项');
            $table->timestamps();
            $table->softDeletes();

            $table->comment('基础组件库');
        });

        Schema::create('ers_payment_order_payments', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('order_id')->comment('工单ID');
            $table->unsignedInteger('order_step_id')->comment('工单步骤ID');
            $table->enum('type', ['normal', 'advance', 'final'])->default('normal')->comment('支付类型');
            $table->decimal('total_amount', 10)->comment('总金额');
            $table->decimal('pay_amount', 10)->comment('付款金额');
            $table->unsignedInteger('pay_order_id')->default(0)->comment('支付订单 ID');
            $table->timestamps();

            $table->index('order_id', 'idx_order_id');
            $table->index('order_step_id', 'idx_order_step_id');

            $table->comment('工单付款数据');
        });

        Schema::create('ers_solution_project_previews', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('project_id')->comment('项目ID');
            $table->unsignedInteger('flow_id')->comment('流程ID');
            $table->unsignedInteger('step_id')->comment('步骤ID');
            $table->string('allow_upload_types')->default('*')->comment('允许上传的方案文档类型');
            $table->timestamps();
            $table->softDeletes();

            $table->index('project_id', 'idx_project_id');
            $table->index('flow_id', 'idx_flow_id');
            $table->index('step_id', 'idx_step_id');

            $table->comment('预览方案项目配置');
        });

        Schema::create('ers_solution_order_previews', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('order_id')->comment('工单ID');
            $table->unsignedInteger('order_step_id')->comment('工单步骤ID');
            $table->json('files')->comment('文件');
            $table->timestamps();

            $table->index('order_id', 'idx_order_id');
            $table->index('order_step_id', 'idx_order_step_id');

            $table->comment('预览方案工单数据');
        });

        Schema::create('ers_solution_order_downloads', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('order_id')->comment('工单ID');
            $table->unsignedInteger('order_step_id')->comment('工单步骤ID');
            $table->string('email', 50)->comment('邮箱地址');
            $table->timestamps();

            $table->index('order_id', 'idx_order_id');
            $table->index('order_step_id', 'idx_order_step_id');

            $table->comment('发送方案工单数据');
        });

        Schema::create('ers_service_orders', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->unsignedInteger('project_id')->comment('项目ID');
            $table->unsignedInteger('flow_id')->comment('流程ID');
            $table->unsignedInteger('industry_id')->default(0)->comment('行业ID');
            $table->unsignedInteger('enterprise_id')->default(0)->comment('企业类别ID');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态 0 草稿，1 用户待办，2 后台待办，3 已完结');
            $table->unsignedInteger('admin_id')->default(0)->comment('对接管理员ID');
            $table->timestamp('finished_at')->nullable()->comment('完成时间');
            $table->timestamps();
            $table->softDeletes();

            $table->index('user_id', 'idx_user_id');
            $table->index('project_id', 'idx_project_id');
            $table->index('flow_id', 'idx_flow_id');
            $table->index('status', 'idx_status');
            $table->index('admin_id', 'idx_admin_id');

            $table->comment('服务工单');
        });

        Schema::create('ers_service_order_steps', function(Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('order_id')->comment('工单ID');
            $table->unsignedInteger('flow_id')->comment('流程ID');
            $table->unsignedInteger('step_id')->comment('步骤ID');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态 0 草稿，1 用户待办，2 后台待办，3 已完结');
            $table->string('module', 20)->comment('模块');
            $table->unsignedInteger('data_id')->default(0)->comment('关联工单模块数据ID');
            $table->timestamp('last_admin_handled_at')->nullable()->comment('后台最后处理时间');
            $table->timestamp('last_user_handled_at')->nullable()->comment('用户最后处理时间');
            $table->tinyInteger('finished_by')->nullable()->comment('由谁完成，0用户，1后台');
            $table->timestamps();

            $table->index('order_id', 'idx_order_id');
            $table->index('step_id', 'idx_step_id');
            $table->index('status', 'idx_status');

            $table->comment('工单流程状态');
        });

        Schema::create('ers_service_order_records', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('order_id')->comment('工单ID');
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->unsignedInteger('admin_id')->comment('管理员ID');
            $table->unsignedInteger('step_id')->comment('到达步骤ID');
            $table->string('desc', 255)->default('')->comment('描述');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态 0 代办，1 已办');
            $table->timestamps();
            $table->softDeletes();

            $table->index('order_id', 'idx_order_id');
            $table->index('user_id', 'idx_user_id');
            $table->index('admin_id', 'idx_admin_id');
            $table->index('step_id', 'idx_step_id');
            $table->index('status', 'idx_status');

            $table->comment('工单/扭转记录');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ers_projects');
        Schema::dropIfExists('ers_industries');
        Schema::dropIfExists('ers_enterprise_categories');
        Schema::dropIfExists('ers_flows');
        Schema::dropIfExists('ers_flow_steps');
        Schema::dropIfExists('ers_form_project_forms');
        Schema::dropIfExists('ers_form_project_inputs');
        Schema::dropIfExists('ers_form_order_forms');
        Schema::dropIfExists('ers_form_order_data');
        Schema::dropIfExists('ers_form_libraries');
        Schema::dropIfExists('ers_payment_order_payments');
        Schema::dropIfExists('ers_solution_project_previews');
        Schema::dropIfExists('ers_solution_order_previews');
        Schema::dropIfExists('ers_solution_order_downloads');
        Schema::dropIfExists('ers_service_orders');
        Schema::dropIfExists('ers_service_order_steps');
        Schema::dropIfExists('ers_service_order_records');
    }
};
