<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stat_daily_promoters', function (Blueprint $table) {
            $table->unsignedDecimal('platform_material_amount', 10)->default(0)->comment('平台资料支付金额')->after('payment_course_user');
        });
        Schema::table('stat_daily_overviews', function (Blueprint $table) {
            $table->unsignedDecimal('platform_material_amount', 10)->default(0)->comment('平台资料支付金额')->after('payment_course_user');
            if (!Schema::hasColumn('stat_daily_overviews', 'payment_topic_user')) {
                $table->unsignedInteger('payment_topic_user')->default(0)->comment('支付题库用户数');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stat_daily_promoters', function (Blueprint $table) {
            $table->dropColumn('platform_material_amount');
        });
        Schema::table('stat_daily_overviews', function (Blueprint $table) {
            $table->dropColumn('platform_material_amount');
            $table->dropColumn('payment_topic_user');
        });
    }
};
