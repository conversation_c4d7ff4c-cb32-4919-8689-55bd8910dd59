<?php

use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePackList;
use App\Models\Org\Course;
use App\Models\Org\CoursePack;
use App\Services\Cms\ContentCourseService;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 课程包下的课程都通过 org_courses 表给到机构
     */
    public function up(): void
    {
        $tableName = 'org_course_packs';
        Schema::table($tableName, function (Blueprint $table) use ($tableName) {
            if (!Schema::hasColumn($tableName, 'hour')) {
                $table->unsignedTinyInteger('hour')->default(0)->after('status')->comment('学时');
            }

            if (!Schema::hasColumn($tableName, 'duration')) {
                $table->unsignedSmallInteger('duration')->default(0)->after('hour')->comment('课程视频时长（单位：秒）');
            }
        });

        // 1. 将org_course_packs的课程添加到org_courses, 过滤已存在的课程
        // 2. 使用org_courses的hour和duration更新org_course_packs的hour和duration

        CoursePack::query()->select(['org_id', 'course_pack_id', 'hour', 'duration'])->chunk(100, function ($coursePacks) { 
            $grouped = $coursePacks->groupBy('org_id');

            // 处理同一机构的课程包
            foreach ($grouped as $orgId => $coursePacks) {

                // 处理每个课程包的课程
                foreach ($coursePacks as $coursePack) {
                    // 课程包所含的课程
                    $courseIds = ContentCoursePackList::query()
                        ->where('content_id', $coursePack->course_pack_id)
                        ->pluck('course_id')
                        ->toArray();

                    $inOrgCourseIds = [];

                    // 机构已存在的课程
                    $inOrgCourseIds = Course::query()
                        ->where('org_id', $orgId)
                        ->whereIn('course_id', $courseIds)
                        ->pluck('course_id')
                        ->toArray();

                    $notInOrgCourseIds = array_diff($courseIds, $inOrgCourseIds);

                    // 添加课程
                    if (!empty($notInOrgCourseIds)) {
                        $contentCourses = ContentCourse::query()
                            ->select(['hour_per_minutes', 'content_id'])
                            ->whereIn('content_id', $notInOrgCourseIds)
                            ->get();
                        
                        $courseDurations = ContentCourseService::getTotalDurations($courseIds);

                        foreach ($contentCourses as $contentCourse) {
                            $hour = $contentCourse->studyHour($courseDurations[$contentCourse->content_id] ?? 0, false);
                            $price = $contentCourse->content?->charge_amount ?? 0;

                            $model = new Course();
                            $model->org_id = $orgId;
                            $model->course_id = $contentCourse->content_id;
                            $model->price_original = $price;
                            $model->price_sell = $price;
                            $model->hour = $hour ?? 0;
                            $model->status = Course::STATUS_HIDDEN;
                            $model->save();
                        }
                    }
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('org_course_packs', function (Blueprint $table) {
            $table->dropColumn('hour');
            $table->dropColumn('duration');
        });
    }
};
