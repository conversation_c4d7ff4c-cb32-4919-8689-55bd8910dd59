<?php

use App\Models\Org\EnrollmentForm;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('org_students', function (Blueprint $table) {
            $table->string('work_unit')
                  ->nullable()
                  ->after('phone')
                  ->comment('工作单位');
        });

        DB::table('org_students')
            ->whereNotNull('extra')
            ->orderBy('id')
            ->chunk(100, function ($students) {
                foreach ($students as $student) {
                    try {
                        $extra = json_decode($student->extra, true);
                        if (!is_array($extra)) continue;
                        
                        foreach ($extra as &$item) {
                            if (isset($item['name']) && in_array($item['name'], ['工作单位', '公司']) && isset($item['value'])) {
                                $item['type'] = EnrollmentForm::TYPE_WORK_UNIT;
                                DB::table('org_students')
                                    ->where('id', $student->id)
                                    ->update([
                                        'work_unit' => $item['value'],
                                        'extra' => json_encode($extra)
                                    ]);
                                break;
                            }
                        }
                    } catch (\Exception $e) {
                        // 跳过解析错误的记录
                        continue;
                    }
                }
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('org_students', function (Blueprint $table) {
            $table->dropColumn('work_unit');
        });
    }
};