<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orgs', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 32)->comment('机构名称');
            $table->timestamps();

            $table->index('name', 'idx_name');

            $table->comment('组织机构');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->unsignedInteger('org_id')->default(0)->comment('机构ID')->after('uuid');
            $table->timestamp('join_org_at')->nullable()->comment('加入机构时间')->after('last_active_at');

            $table->index('org_id', 'idx_org_id');
        });

        Schema::table('open_course_records', function (Blueprint $table) {
            $table->unsignedInteger('org_id')->default(0)->comment('机构ID')->after('user_id');

            $table->index('org_id', 'idx_org_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orgs');

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['org_id', 'join_org_at']);
        });

        Schema::table('open_course_records', function (Blueprint $table) {
            $table->dropColumn(['org_id']);
        });
    }
};
