<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cms_content_course_packs', function (Blueprint $table) {
            $table->increments('content_id');
            $table->timestamps();
            $table->comment('课程包');
        });

        Schema::create('cms_content_course_pack_lists', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('content_id')->index('content_id');
            $table->unsignedInteger('course_id')->comment('关联课程的ID')->index('course_id');
            $table->unsignedMediumInteger('order')->default(0)->comment('排序');
            $table->timestamps();
            $table->comment('课程包课程列表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('cms_content_course_pack_lists');
        Schema::drop('cms_content_course_packs');
    }
};
