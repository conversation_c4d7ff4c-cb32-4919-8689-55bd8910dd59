<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cms_content_course_docs', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('content_id')->comment('所属内容ID');
            $table->string('filename', 64)->comment('文件名');
            $table->string('filepath', 255)->comment('文件路径');
            $table->unsignedTinyInteger('sort')->default(0)->comment('排序');
            $table->unsignedInteger('download_count')->default(0)->comment('下载统计');
            $table->timestamps();

            $table->index('content_id', 'idx_content_id');

            $table->comment('课程资料');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cms_content_course_docs');
    }
};
