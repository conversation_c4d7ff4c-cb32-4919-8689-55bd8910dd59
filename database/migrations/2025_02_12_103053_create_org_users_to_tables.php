<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('org_admins', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('org_id');
            $table->string('username', 21)->comment('用户名');
            $table->string('password', 128)->comment('密码');
            $table->string('real_name', 30)->default('')->comment('姓名');
            $table->string('phone', 20)->default('')->comment('手机号');
            $table->string('email', 32)->default('')->comment('邮箱');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态 0 正常，1 禁用');
            $table->unsignedTinyInteger('is_main')->default(0)->comment('是否主账号');
            $table->timestamp('last_logged_at')->nullable()->comment('最近登录时间');
            $table->string('last_logged_ip', 15)->default('')->comment('最近登录IP');
            $table->timestamp('last_active_at')->nullable()->comment('最近活跃时间');
            $table->string('last_active_ip', 15)->default('')->comment('最近活跃IP');
            $table->timestamps();

            $table->index(['org_id'], 'org_id');
            $table->unique(['username'], 'username');

            $table->comment('机构管理员表');
        });

        Schema::create('org_roles', function (Blueprint $table) {
            $table->smallIncrements('id');
            $table->string('name', 20)->comment('角色名称');
            $table->string('code', 30)->comment('角色编码');
            $table->string('desc')->default('')->comment('角色描述');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态 0:禁用 1:启用');
            $table->timestamps();

            $table->unique(['code'], 'code');
            $table->comment('权限表');
        });

        Schema::create('org_admin_roles', function (Blueprint $table) {
            $table->smallIncrements('id');
            $table->unsignedSmallInteger('admin_id');
            $table->unsignedSmallInteger('role_id');
            $table->timestamp('created_at')->nullable();

            $table->index(['admin_id'], 'admin_id');
            $table->index(['role_id'], 'role_id');

            $table->comment('用户权限表');
        });

        Schema::create('org_admin_operates', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('admin_id')->comment('管理员ID');
            $table->string('remark',255)->comment('备注');
            $table->text('context');
            $table->timestamp('created_at')->nullable();

            $table->index(['admin_id'], 'admin_id');

            $table->comment('操作记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('org_admins');
        Schema::dropIfExists('org_roles');
        Schema::dropIfExists('org_admin_roles');
        Schema::dropIfExists('org_admin_operates');
    }
};
