<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inspect_devices', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->after('org_id')->index()->comment('用户ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inspect_devices', function (Blueprint $table) {
            $table->dropColumn('user_id');
        });
    }
};