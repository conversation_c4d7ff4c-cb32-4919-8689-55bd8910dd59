<?php

namespace App\Core\Enums;

use App\Models\Cms\Content;
use App\Models\Cms\Special;
use App\Models\Ers\PaymentOrderPayment;
use App\Models\Ers\ServiceOrder;
use App\Models\Expert;
use App\Models\Invitation;
use App\Models\Order\Order;
use App\Models\Org;
use App\Models\Org\EnrollConfig;
use App\Models\Org\Enrollment;
use App\Models\Qa\Answer;
use App\Models\Qa\Question;
use App\Models\Train\Subject;
use App\Models\Train\Topic;
use App\Models\User\UserCreditLog;
use Illuminate\Database\Eloquent\Model;
use App\Models\Org\Export;

/**
 * 业务类型
 */
enum BusinessType: string
{
    case Category = 'category';
    case Content = 'content';
    case CmsMaterial = 'cms.material';
    case CmsCourse = 'cms.course';
    case CmsCoursePack = 'cms.course_pack';
    case CmsNews = 'cms.news';
    case CmsCourseDoc = 'cms.course_doc';

    case Special = 'special';

    case Question = 'question';
    case Answer = 'answer';

    case Topic = 'topic';
    case Subject = 'subject';
    case TrainExample = 'train.example';

    case Invitation = 'invitation';

    /** 订单类型，一般用来代表充值 */
    case Order = 'order';

    case Credit = 'credit';

    /** 报名类型，一般用来代表机构报名 */
    case Enroll = 'enroll';

    case Booth = 'booth';

    case Activity = 'activity';

    case User = 'user';

    case ErsOrder = 'ers.order';
    case ErsPayment = 'ers.order_payment'; //这个模块是要硬编码进来的
    case ErsProject = 'ers.project';

    case Expert = 'expert';

    case Org = 'org';

    case Enrollment = 'enrollment';

    case LearnCapture = 'learn_capture';

    case Export = 'export';

    case OrgTemplate = 'org.template';
    case OrgStudent = 'org.student';

    /**
     * 获取业务对应的模型类名
     *
     * 在被相应模型多态关联时需要在此地配置
     *
     * @return class-string<Model>|null
     */
    public function modelClass()
    {
        return match($this) {
            self::Content, self::CmsMaterial, self::CmsCourse, self::CmsNews, self::CmsCourseDoc, self::CmsCoursePack => Content::class,
            self::Special => Special::class,
            self::Question => Question::class,
            self::Answer => Answer::class,
            self::Topic => Topic::class,
            self::Subject => Subject::class,
            self::Invitation => Invitation::class,
            self::Order => Order::class,
            self::Credit => UserCreditLog::class,
            self::ErsOrder => ServiceOrder::class,
            self::ErsPayment => PaymentOrderPayment::class,
            self::Expert => Expert::class,
            self::Org => Org::class,
            self::Enrollment => Enrollment::class,
            self::Export => Export::class,
            self::Enroll => EnrollConfig::class,
            default => null
        };
    }

    /**
     * 获取业务对应的中文名称
     *
     * @return string|null
     */
    public function modelLabel()
    {
        return match($this) {
            self::Content, self::CmsMaterial, self::CmsCourse, self::CmsNews, self::CmsCourseDoc, self::CmsCoursePack => '内容',
            self::Special => '专题',
            self::Question => '安全动态',
            self::Answer => '评论',
            self::Topic => '题库',
            self::Subject => '题目',
            self::Invitation => '邀请',
            self::Order => '订单',
            self::Credit => '积分',
            self::Activity => '活动',
            self::ErsOrder => '需求工单',
            self::ErsPayment => '需求付款',
            self::Expert => '专家',
            self::Export => '导出',
            self::Enroll => '机构报名',
            default => $this->name
        };
    }

}
