<?php

namespace App\Libs\AsyncTasks;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\AsyncTask as AsyncTaskModel;
use App\Models\Cms\Content;
use App\Models\Cms\ContentVideo;
use App\Services\Cms\ContentService;
use App\Services\Common\AttachmentService;

/**
 * 单视频内容视频转码发布任务
 * 获取视频信息、处理转码、截图完成后发布单视频内容
 */
class ContentVideoTranscodeTask extends QiniuVideoTranscodeTask
{

    /**
     * @param int $contentId video 类型内容 ID
     * @param string $targetFormat 要转码的目标格式
     * @param bool $addWatermark 是否添加水印
     */
    public function __construct(public readonly int $contentId, string $targetFormat='mp4', bool $addWatermark=false)
    {
        $video = $this->contentVideo();

        if (!$video) {
            throw new ServiceException("内容 $contentId 不存在或不是视频类型内容。");
        }

        $extend = $video->extend ?: [];

        if (!empty($extend['hd'])) {
            throw new ServiceException("内容 $this->contentId 已经转码完成，无法继续转码。");
        }

        parent::__construct($video->filepath, $targetFormat, $addWatermark );
    }

    protected function contentVideo(): ?ContentVideo
    {
        return ContentVideo::query()
            ->find($this->contentId);
    }

    /**
     * @inheritDoc
     */
    public function callback(AsyncTaskModel $task, $data)
    {
        $info = $this->transcodedVideoInfo($data);
        $contentVideo = $this->contentVideo();
        $extend = $contentVideo->extend ?: [];

        //保存视频并存储到 extend 中，这个流程可以根据自己的需要自行实现
        $extend = $this->fillExtend($info, $extend, BusinessType::Content, $contentVideo->content_id);

        $contentVideo->extend = $extend;
        $contentVideo->duration = $info['duration'];
        $contentVideo->save();

        //发布内容
        ContentService::release($contentVideo->content_id);

        return $info;
    }

}
