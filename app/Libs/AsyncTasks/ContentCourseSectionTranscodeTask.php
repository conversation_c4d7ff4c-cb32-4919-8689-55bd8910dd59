<?php

namespace App\Libs\AsyncTasks;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\AsyncTask as AsyncTaskModel;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourseSection;
use App\Services\Cms\ContentService;
use App\Services\Common\AttachmentService;

/**
 * 章节课程小节视频转码发布任务
 * 获取视频信息、处理转码、截图完成后如果该内容的所有小节都已完成，则发布内容
 */
class ContentCourseSectionTranscodeTask extends QiniuVideoTranscodeTask
{

    /**
     * @param int $sectionId 章节课程小节 ID
     * @param string $targetFormat 要转码的目标格式
     * @param bool $addWatermark 是否添加水印
     */
    public function __construct(public readonly int $sectionId, string $targetFormat='mp4', bool $addWatermark=false)
    {
        $video = $this->courseSection();

        if (!$video) {
            throw new ServiceException("小节 $this->sectionId 不存在。");
        }

        $extend = $video->extend ?: [];

        if (!empty($extend['hd'])) {
            throw new ServiceException("小节 $this->sectionId 已经转码完成，无法继续转码。");
        }

        parent::__construct($video->filepath, $targetFormat, $addWatermark);
    }

    protected function courseSection(): ?ContentCourseSection
    {
        return ContentCourseSection::query()
            ->where('id', $this->sectionId)
            ->first();
    }

	/**
	 * @inheritDoc
	 */
	public function callback(AsyncTaskModel $task, $data)
    {
        $info = $this->transcodedVideoInfo($data);
        $courseSection = $this->courseSection();
        $extend = $courseSection->extend ?: [];

        //保存视频并存储到 extend 中，这个流程可以根据自己的需要自行实现
        $extend = $this->fillExtend($info, $extend, BusinessType::Content, $courseSection->content_id);

        $courseSection->extend = $extend;
        $courseSection->duration = $info['duration'];
        $courseSection->status = ContentCourseSection::STATUS_SHOW;
        $courseSection->save();

        //发布内容，检查该小节下内容是否已全部完成，是则发布
        if ($courseSection->content->status == Content::STATUS_PROCESSING) {
            $stillNotConvert = ContentCourseSection::query()
                ->where('content_id', $courseSection->content_id)
                ->whereIn('status', [ContentCourseSection::STATUS_PADDING, ContentCourseSection::STATUS_PROCESSING])
                ->exists();

            if (!$stillNotConvert) {
                ContentService::release($courseSection->content_id);
            }
        }

        return $info;
	}
}
