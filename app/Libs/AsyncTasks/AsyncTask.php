<?php

namespace App\Libs\AsyncTasks;

use App\Models\AsyncTask as AsyncTaskModel;

interface AsyncTask
{

    /**
     * 启动任务
     *
     * @return string 返回该任务的外部 ID
     */
    public function start(): string;

    /**
     * 查询外部任务执行结果
     *
     * @param AsyncTaskModel $task
     * @return mixed 检查成功返回检查到的任务数据，进行中返回 false，失败抛出异常
     */
    public function check(AsyncTaskModel $task);

    /**
     * 三方任务处理成功后的回调处理
     *
     * @param AsyncTaskModel $task
     * @param mixed $data 第三方任务成功返回的数据
     * @return mixed 本地处理结果
     */
    public function callback(AsyncTaskModel $task, $data);

    /**
     * 获取任务类型
     */
    public function getTaskType(): AsyncTaskType;

}
