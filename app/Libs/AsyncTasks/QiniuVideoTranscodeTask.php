<?php

namespace App\Libs\AsyncTasks;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\AsyncTask as AsyncTaskModel;
use App\Services\Common\AttachmentService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Overtrue\Flysystem\Qiniu\QiniuAdapter;
use Qiniu\Config;
use Qiniu\Http\Client;
use Qiniu\Http\Error;
use Qiniu\Processing\PersistentFop;
use function Qiniu\base64_urlSafeEncode;

/**
 * 七牛视频转码服务
 *
 * 对指定的业务模型进行视频转码
 * 转码、视频截图做封面、获取视频时长三个任务
 */
abstract class QiniuVideoTranscodeTask implements AsyncTask
{

    const DISK_NAME = 'qiniu-priv';

    /**
     * 记录进任务中的视频信息
     */
    protected array $avInfoCache;

    /**
     * @param string $path 源视频路径
     * @param string $targetFormat 要转换至的目标格式
     * @param bool $addWatermark 是否添加水印
     */
    public function __construct(public readonly string $path, public readonly string $targetFormat='mp4', public readonly bool $addWatermark=false)
    {}

	/**
	 * @inheritDoc
	 */
	public function getTaskType(): AsyncTaskType
    {
		return AsyncTaskType::QiniuVideoTranscode;
	}

    public function getAdapter(): QiniuAdapter
    {
        return Storage::disk(self::DISK_NAME)->getAdapter();
    }

    public function getPersistent()
    {
        $adapter = $this->getAdapter();
        $auth = $adapter->getAuthManager();

        //发送处理请求
        $config = new Config();
        $config->useHTTPS = true;
        return new PersistentFop($auth, $config);
    }

	/**
	 * @inheritDoc
	 */
	public function start(): string
    {
		$adapter = $this->getAdapter();
        $bucket = $adapter->getBucket();

        $avInfo = $this->avInfo();

        //如果文件格式不同则强制转换
        $force = strcasecmp(pathinfo($this->path, PATHINFO_EXTENSION), $this->targetFormat) != 0;

        $fops = [];

        //视频转码
        $transcode = $this->transcodeFops($avInfo, $force);
        $transcodeSaveAs = $bucket.':tmp/hd-'.uniqid().'.'.$this->targetFormat; //存储位置

        //转码同时加水印
        if ($this->addWatermark) {
            $transcode = $this->withWatermarkFops($transcode, $avInfo);
        }

        if ($transcode) {
            $transcode .= '|saveas/'. base64_urlSafeEncode($transcodeSaveAs);
            $fops[] = $transcode;
        }

        //视频截图
        $screenshot = $this->screenshotFops($avInfo);
        $screenshotSaveAs = $transcodeSaveAs.'-vframe.jpg';
        $screenshot .= '|saveas/'. base64_urlSafeEncode($screenshotSaveAs);
        $fops[] = $screenshot;

        //发送处理请求
        $persistent = $this->getPersistent();

        $callbackUrl = app()->isLocal() ? 'http://requestbin.cn/15n960i1' : route('qiniu_transcode_callback');

        /** @var Error|null $err */
        [$persistentId, $err] = $persistent->execute($bucket, $this->path, $fops, null, $callbackUrl);

        Log::info("七牛视频转码开始 {$this->path} -> {$transcodeSaveAs}，截图至 {$screenshotSaveAs}.", ['fops'=>$fops]);

        if ($err) {
            throw new ServiceException("发起七牛视频转码失败：{$err->message()}");
        }

        return $persistentId;
	}

	/**
	 * @inheritDoc
	 */
	public function check(AsyncTaskModel $task)
    {
        $persistent  = $this->getPersistent();

        //返回数据格式见 https://developer.qiniu.com/dora/1294/persistent-processing-status-query-prefop
        /** @var Error|null $err */
        [$ret, $err] = $persistent->status($task->task_id);

        if ($err) {
            throw new ServiceException($err->message());
        }

        return self::taskStatusCheck($ret) ? $ret : false;
	}

    /**
     * 检查任务结果并反馈状态和数据
     *
     * 因为七牛的任务查询和回调数据结构是相同的，此方法共享在 check() 查询和回调的检查中。
     *
     * @param array $ret
     * @return bool 是否已完成
     * @throws ServiceException
     */
    public static function taskStatusCheck($ret)
    {
        switch ($ret['code']) {
            case 0:
                return true;

            case 1:
            case 2:
                return false;

            case 3:
                $errors = [];
                foreach ($ret['items'] as $item) {
                    if ($item['code'] == 3 && isset($item['error'])) {
                        $errors[] = $item['error'];
                    }
                }

                if ($errors) {
                    $error = $ret['desc'].': '.join('; ', $errors).'.';
                } else {
                    $error = $ret['desc'];
                }

                throw new ServiceException($error);

            default:
                throw new ServiceException('任务状态异常 '.$ret['code']);
        }
    }

    /**
     * 获取视频文件的流媒体信息
     *
     * @return array
     * @throws ServiceException
     */
    private function avInfo()
    {
        if (isset($this->avInfoCache)) {
            return $this->avInfoCache;
        }

        $cacheKey = 'qiniu-avinfo:'.$this->path;

        $avInfo = Cache::get($cacheKey);

        if (!$avInfo) {
            /** @var QiniuAdapter $adapter */
            $avInfoUrl = Storage::disk(self::DISK_NAME)->getAdapter()->privateDownloadUrl($this->path.'?avinfo');
            $avInfo = Client::get($avInfoUrl)->json();

            if (!is_array($avInfo)) {
                throw new ServiceException('获取视频文件信息失败');
            }

            if (!empty($avInfo['error'])) {
                throw new ServiceException('获取音视频信息失败: '.$avInfo['error']);
            }

            foreach ($avInfo['streams'] as $i => $row) {
                //如果相同的 codec_type 出现两次，则保留 codec_time_base 不是 0/1 的那个（0/1 的极有可能不是正常的音视频流，如 codec_name 是 png 的假视频流）
                if (!isset($avInfo['streams'][$row['codec_type']]) || (isset($row['codec_time_base']) && $row['codec_time_base'] != '0/1')) {
                    $avInfo['streams'][$row['codec_type']] = $row;
                } else {
                    //其它 codec_type 重复的部分追加个序号继续保留
                    $avInfo['streams'][$row['codec_type'].'_'.$i] = $row;
                }
                unset($avInfo['streams'][$i]);
            }

            Cache::put($cacheKey, $avInfo, 86400*5);
        }

        $this->avInfoCache = $avInfo;

        return $avInfo;
    }

    /**
     * 对比并返回
     *
     * @param array $avInfo
     * @param bool $force
     * @return string
     */
    protected function transcodeFops($avInfo, $force=false)
    {
        //转换目录最大参数
        $target = [
            'width' => 1280, //分辨率
            'height' => 720,
            'ab' => '64k', //音频码率
            'ar' => 44100, //音频采样率
            'aprofile' => 'lc', //音频配置方案
            'acodec' => 'libfdk_aac', //音频编码方案
            'vb' => 1250*1000, //视频码率1250k
            'vprofile' => 'main', //视频配置方案
            'vcodec' => 'libx264', //视频编码方案
            'fps' => 30 //FPS
        ];

        $fopsMain = ''; //必要参数,当必要参数具备时一定转换编码
        $fopsAddon = '/ab/'.$target['ab']; //非必要参数,只有必要参数具备,非必要参数才会作为附属添加进去

        //视频信息
        if (isset($avInfo['streams']['video'])) {
            $video = $avInfo['streams']['video'];

            //FPS 对比
            //Todo: 目前FPS的测算方法都不是很正确，不管是 r_frame_rate 还是 avg_frame_rate 多种情况下结果不正确,还需要继续研究
            //因为预算不是很正确，所以在 FPS 在于目标值并超过 10 以上时才转换
            if ($video['avg_frame_rate'] != '0/0') {
                $frate = explode('/', $video['avg_frame_rate']);
                if ($frate[0] / $frate[1] > $target['fps'] + 10) {
                    $fopsMain .= '/r/'.$target['fps'];
                }
            }

            //视频采样率对比,七牛文档指出如果给出的码率大于原码率,则会使用原码率转换，但某些视频仍然会出现码率变大,所以此处加上判断
            $bitRate = $video['bit_rate'] ?? $avInfo['format']['bit_rate'] ?? 0;
            if ($bitRate) {
                $fopsAddon .= '/vb/' . ($bitRate > $target['vb'] ? $target['vb'] : $bitRate);
            }

            //编码对比
            if ($video['codec_name'] != 'h264') {
                $fopsMain .= '/vcodec/' . $target['vcodec'];
            }

            //视频配置方案对比
            if (strtolower($video['profile']) == 'high') {
                $fopsMain .= '/h264Profile/'.$target['vprofile'];
            }

            //分辨率对比,以原比例限定在指定分辨率以内
            if ($force) {
                $fopsMain .= '/s/' . ($video['width'] > $target['width'] ? $target['width'] : $video['width'])
                    . 'x' . ($video['height'] > $target['height'] ? $target['height'] : $video['height']) . '/autoscale/1';
            } elseif ($video['width'] > $target['width'] || $video['height'] > $target['height']) {
                $fopsMain .= '/s/'.$target['width'].'x'.$target['height'].'/autoscale/1';
            }

            //保留元信息
            $fopsAddon .= '/stripmeta/0';
        }

        //音频信息
        if (isset($avInfo['streams']['audio'])) {
            $audio = $avInfo['streams']['audio'];

            //if (strtolower($audio['profile']) != 'lc') {
            //	$fopsMain .= '/audioProfile/lc';
            //}

            //音频采样率对比
            if ($audio['sample_rate'] > $target['ar']) {
                $fopsAddon .= '/ar/'.$target['ar'];
            }

            if ($audio['codec_name'] != 'aac') {
                $fopsMain .= '/acodec/'.$target['acodec'];
            }
        }

        //检查封装格式
        $brand = $avInfo['format']['tags']['major_brand'] ?? '';
        if ($brand && $brand != 'isom' && !str_contains($fopsMain, '/vcodec/')) {
            $fopsMain .= '/vcodec/'.$target['vcodec'];
        }

        $fops = '';

        //如果视频不是目标格式,或有必要参数,则进行转码
        if ($force || $fopsMain || !str_contains($avInfo['format']['format_name'], $this->targetFormat)) {
            $fops = 'avthumb/'.$this->targetFormat.$fopsMain.$fopsAddon;
        }

        return $fops;
    }

    protected function withWatermarkFops($transcodeFops, $avInfo=null)
    {
        if (!$transcodeFops) {
            $transcodeFops = 'avthumb/'.$this->targetFormat;
        }

        if ($avInfo) {
            $video = $avInfo['streams']['video'];
            //判断视频是横向还是坚向，横向则根据横边等比缩放，坚向根据竖边
            //wmScaleType 值为 0 代表根据原视频短边进行缩放，值为 1 代表根据原视频长边进行缩放（这点七牛文档标注并不清晰，要搞清楚之间的关系，而且七牛的比例跟原图是有些差异的）
            if (isset($video['display_aspect_ratio']))  {
                [$rw, $rh] = explode(':', $video['display_aspect_ratio']);
            } else {
                $rw = $video['width'];
                $rh = $video['height'];
            }
            //横屏长边0.18,竖屏短边0.3
            [$scale, $scaleType] = $rw > $rh ? ['0.18', 1] : ['0.27', 0];
        } else {
            [$scale, $scaleType] = ['0.2', 1];
        }

        //在转码逻辑中加水印逻辑
        //$transcodeFops .= '/wmImage/<Encodedkodocheme>';
        return $transcodeFops
            .'/wmImage/'.base64_urlSafeEncode('kodo://heguibao-pub/assets/watermark/logo_white_2.png')
            ."/wmScale/$scale/wmScaleType/$scaleType/wmConstant/1/wmGravity/NorthEast";

        //文本水印
        //.'/wmText/'.base64_urlSafeEncode('安管合规').'/wmFont/'.base64_urlSafeEncode('Source Han Sans SC')
        //.'/wmFontColor/'.base64_urlSafeEncode('#FFFFFF').'/wmFontSize/25/wmFontStyle/'.base64_urlSafeEncode('bold')
        //.'/wmAlpha/0.7/wmGravityText/NorthWest';
    }

    protected function screenshotFops($avInfo)
    {

        $duration = $avInfo['streams']['video']['duration'] ?? $avInfo['streams']['audio']['duration'] ?? $avInfo['format']['duration'];
        $offset = match (true) {
            $duration > 300 => 30,
            $duration > 30 => 10,
            default => ceil($duration / 5)
        };
        return "vframe/jpg/offset/$offset|imageView2/0/w/720/h/720/format/jpg";
    }

    /**
     * 从任务数据中获取转码后的视频和截图信息
     *
     * 信息中包含视频和截图在目标存储中临时路径，文件 Hash，以及视频原始文件（转码前的源文件）的宽高等。
     * 如果返回的 video 为 null 代表原视频可以直接使用没有进行转码。
     *
     * @param array $data 任务数据
     * @return array{duration: int, source_width: int, source_height: int, video?: array{key: string, tmp: string, hash: string}, screenshot: {key: string, tmp: string, hash: string}}
     */
    protected function transcodedVideoInfo($data)
    {
        $avInfo = $this->avInfo();

        $avThumb = null; //转码可能存在符合条件不转的情况，所以 $avThumb 要考虑无
        $vframe = [];

        foreach ($data['items'] as $item) {
            if (str_starts_with($item['cmd'], 'avthumb/')) {
                $avThumb = $item;
            } elseif (str_starts_with($item['cmd'], 'vframe/')) {
                $vframe = $item;
            }
        }

        $streams = $avInfo['streams'];

        return [
            'duration' => $streams['video']['duration'] ?? $streams['audio']['duration'] ?? $avInfo['format']['duration'],
            'original_width' => $streams['video']['width'] ?? $streams['video']['coded_width'] ?? 0,
            'original_height' => $streams['video']['height'] ?? $streams['video']['coded_height'] ?? 0,
            'video' => $avThumb ? [
                'key' => $avThumb['key'],
                'tmp' => self::DISK_NAME.':'.$avThumb['key'],
                'hash' => $avThumb['hash']
            ] : null,
            'screenshot' => [
                'key' => $vframe['key'],
                'tmp' => self::DISK_NAME.':'.$vframe['key'],
                'hash' => $vframe['hash'],
            ]
        ];
    }

    /**
     * 辅助保存转码的视频并填充到 extend 中
     *
     * @param array $info 转码后的视频信息
     * @param array $extend 存储表的 extend 数据
     * @param BusinessType $businessType 业务类型
     * @param int $businessId 业务 ID
     */
    protected function fillExtend($info, $extend, BusinessType $businessType, $businessId)
    {
        $hd = $info['video'];

        //有转码时存储转码视频
        if ($hd) {
            $hdFile = AttachmentService::store($hd['tmp'], 'hd', $businessType, $businessId);
            $hdFile->width = $info['original_width'];
            $hdFile->height = $info['original_height'];
            $hdFile->save();

            $extend['hd'] = $hdFile->path;
            $extend['hd_file_id'] = $hdFile->id;
            $extend['same_as_original'] = false;
        } else {
            $extend['hd'] = null;
            $extend['hd_file_id'] = 0;
            $extend['same_as_original'] = true;
        }

        $extend['original_width'] = $info['original_width'];
        $extend['original_height'] = $info['original_width'];
        $extend['duration'] = $info['duration'];

        //存储视频截图
        $screenshotFile = AttachmentService::store($info['screenshot']['tmp'], 'hd_vframe', $businessType, $businessId);
        $extend['screenshot'] = $screenshotFile->path;
        $extend['screenshot_file_id'] = $screenshotFile->id;

        return $extend;
    }

}
