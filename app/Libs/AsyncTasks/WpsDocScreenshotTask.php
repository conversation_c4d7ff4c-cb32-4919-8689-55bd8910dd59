<?php

namespace App\Libs\AsyncTasks;

use App\Exceptions\ServiceException;
use App\Libs\Wps\Converter;
use App\Models\AsyncTask as AsyncTaskModel;
use App\Services\Common\AttachmentService;

/**
 * Wps 文档截图服务
 * 对文档进行截图，并获得截取的图片临时文件
 */
class WpsDocScreenshotTask implements AsyncTask
{

    /**
     * 文档截图
     *
     * @param string $docFileUrl 用于截图的源 Word, Excel, PPT, PDF 的文件地址
     * @param string $filename 文档文件的原始文件名
     * @param int $pages 要截取的最大页数
     */
    public function __construct(public readonly string $docFileUrl, public readonly string $filename, public readonly int $pages=10)
    {}

	public function start(): string
    {
        $converter = app(Converter::class);

        $data = $converter->toJpg($this->docFileUrl, $this->filename, [
            'ranges' => $this->pages > 1 ? '1-'.$this->pages : '1'
        ]);

        return $data['task_id'];
	}

	public function check(AsyncTaskModel $task)
    {
        $converter = app(Converter::class);

        $data = $converter->getTask($task->task_id);

        return match ($data['status']) {
            'processing' => false,
            'success' => $data,
            'failure' => throw new ServiceException($data['message']),
            default => throw new ServiceException('转换任务异常')
        };
	}

	public function callback(AsyncTaskModel $task, $data)
    {
        $images = [];

        //将转换成功的图片全部存储到临时文件区
		foreach ($data['result']['images'] as $image) {
            $ret = AttachmentService::download($image['url'], upload: true);
            unset($ret['local_path']);
            $images[] = $ret;
        }

        return $images;
	}

	public function getTaskType(): AsyncTaskType
    {
		return AsyncTaskType::WpsDocToJpg;
	}

}
