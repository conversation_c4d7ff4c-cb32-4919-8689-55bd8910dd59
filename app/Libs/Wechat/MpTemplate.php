<?php

namespace App\Libs\Wechat;

class MpTemplate
{
    /** @var string 模板类型 */
    public const SERVICE_ORDER = 'service_order';

    /**
     * 数据样本
     *
     * @param array $data 发送数据
     * @param string $type 模板类型
     *
     * @return string
     */
    public static function messageSample(array $data, string $type): string
    {
        return match ($type) {
            self::SERVICE_ORDER => "工单编号: {$data['character_string1']['value']}\n工单状态: {$data['phrase7']['value']}\n服务进度: {$data['phrase8']['value']}\n备注：{$data['thing3']['value']}\n处理时间：{$data['date2']['value']}"
        };
    }
}
