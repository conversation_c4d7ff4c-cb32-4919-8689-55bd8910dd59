<?php
namespace App\Libs\Meitu;

use App\Exceptions\ServiceException;
use GuzzleHttp\Exception\GuzzleException;

class AIGCP
{
    private Signer $signer;

    public function __construct()
    {
        $this->signer = new Signer(
            config('meitu.app_key'),
            config('meitu.secret_id'),
        );
    }

    /**
     * 智能抠图
     *
     * @param string $imageUrl 图片地址
     * @return array
     */
    public function aiCut(string $imageUrl)
    {
        $modelType = 0;
        $url = config('meitu.url.ai_cut');
        $body = [
            'sync_timeout' => 120,
            'task_type' => 'mtlab',
            'init_images' => [['url' => $imageUrl]],
            'task' => 'v1/sod',
            'params' => json_encode([
                'parameter' => [
                    'rsp_media_type' => 'url', // “url”：抠图结果以短期有效的url返回
                    //'nMask' => false, // 是否返回mask图，True只返回mask图，False返回结果图
                    // ⚠️：model_type 传 0 ，返回值 Kind 就是写死的 0 ，无法判断是否是人还是物
                    //'model_type' => $modelType, // 选择要使用的抠图模型，传0：表示使用人像抠图；传1：表示使用商品抠图；传2：表示使用图形抠图。若不传，模型内部会自动判断选择使用哪个模型
                    //'blackwhite' => true // 是否返回黑白图，True只返回黑白mask图，False返回四通道Mask图，默认为False
                ]
            ])
        ];

        $response = $this->request($url, 'POST', body: $body);

        if (isset($response['code']) && $response['code'] > 0) {
            throw new ServiceException('图片处理失败：' . $this->signer->getCodeError($response['code']));
        }

        if (isset($response['data']['status']) && $response['data']['status'] !== 10) {
            // 任务处理状态：9=超时，2=失败，10=成功
            $err = [
                9 => '超时',
                2 => '失败'
            ];

            throw new ServiceException('图片处理失败：' . $err[$response['data']['status']] ?? '未知错误');
        }

        if (isset($response['data']['result']['parameters']['Kind']) && $response['data']['result']['parameters']['Kind'] !== $modelType) {
            throw new ServiceException('图片处理失败，请上传正确的人像照片');
        }

        if (!isset($response['data']['result']['urls']) || !is_array($response['data']['result']['urls']) || count($response['data']['result']['urls']) < 1) {
            throw new ServiceException('图片处理失败，返回结果不正确');
        }

        return ['url' => $response['data']['result']['urls'][0]];
    }

    /**
     * 请求接口
     *
     * @param string $url
     * @param string $method
     * @param array $headers
     * @param array $body
     * @param bool $isJson
     * @return array
     */
    protected function request(string $url, string $method, array $headers = [], array $body = [], bool $isJson = true): array
    {
        try {
            return $this->signer->request($url, $method, $headers, $body, $isJson);
        } catch (GuzzleException $e) {
            throw new ServiceException($e->getCode() . ':'. $e->getMessage());
        }
    }
}
