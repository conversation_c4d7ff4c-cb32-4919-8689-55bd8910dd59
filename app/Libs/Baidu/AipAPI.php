<?php

namespace App\Libs\Baidu;

use App\Exceptions\ServiceException;
use App\Services\HttpClientService;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AipAPI
{
    protected string $APIKey;
    protected string $secretKey;
    protected array $config;

    public function __construct(protected HttpClientService $httpClient)
    {
        $this->config = config('baidu');
        $this->APIKey = $this->config['api_key'];
        $this->secretKey = $this->config['secret_key'];
    }

    /**
     * 获取access_token
     *
     * @return string
     * @throws GuzzleException
     */
    private function getAccessToken(): string
    {
        $cacheKey = 'baidu:aip:accessToken';

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $uri = $this->config['url']['token'] . "?grant_type=client_credentials&client_id=$this->APIKey&client_secret=$this->secretKey";
        // 设置自定义请求头
        $accessToken = $this->httpClient->post($uri);

        Cache::put($cacheKey, $accessToken['access_token'], $accessToken['expires_in']);

        return $accessToken['access_token'];
    }

    /**
     * 获取身份证信息
     *
     * @param string $imageUrl 图片访问地址
     * @param string $idCardSide 身份证正反面 front/back
     * @param string $detectCard 是否检测身份证进行裁剪
     * @return array
     * @throws GuzzleException
     */
    public function getIdCardInfo(string $imageUrl, string $idCardSide, string $detectCard = 'false'): array
    {
        $uri = $this->config['url']['id_card'] . "?access_token={$this->getAccessToken()}";
        $headers = ['Content-Type' => 'application/x-www-form-urlencoded'];
        $data = [
            'url' => $imageUrl,
            'id_card_side' => $idCardSide,
            'detect_card' => $detectCard,
            'detect_ps' => 'true',
            'detect_quality' => 'true'
        ];

        try {
            $res = $this->httpClient->setHeaders($headers)->post($uri, $data, false);
            Log::info("身份证信息", $res);

            if (isset($res['error_code'])) {
                throw new ServiceException("身份证类型错误, {$res['error_msg']}");
            }

            $errMsg = $this->getErrorMsg($res);
            if (!empty($errMsg)) {
                throw new ServiceException($this->getErrorMsg($res));
            }

            return $res;
        } catch (\Throwable $e) {
            Log::error("身份证识别失败:" . $e);
            throw new ServiceException($e->getMessage());
        }
    }

    /**
     * 人脸对比
     *
     * @param string $photo1Url
     * @param string $photo2Url
     * @param string $type
     * @return array
     * @throws GuzzleException
     */
    public function faceMatchV3(string $photo1Url, string $photo2Url, string $type = 'LIVE'): array
    {
        $url = $this->config['url']['face_match_v3'] . "?access_token={$this->getAccessToken()}";
        $data = [
            [
                'image' => $this->getBase64FromUrl($photo1Url),
                'image_type' => 'BASE64',
                'face_type' => $type
            ],
            [
                'image' => $this->getBase64FromUrl($photo2Url),
                'image_type' => 'BASE64',
                'face_type' => 'LIVE',
                'spoofing_control' => 'NORMAL'
            ]
        ];

        try {
            $res = $this->httpClient->post($url, $data);

            if (isset($res['error_code']) && $res['error_code'] > 0) {
                Log::error("身份照片对比出错：{$res['error_code']}：{$res['error_msg']}", compact('photo1Url', 'photo2Url'));
                throw new ServiceException("身份照片对比失败，请上传清晰且符合要求的真人照片，确保图片无遮挡、无模糊、无反光。");
            }

            if (!isset($res['result']['score'])) {
                throw new ServiceException("身份照片对比请求出错");
            }

            return ['score' => $res['result']['score'], 'pass' => $res['result']['score'] > 70];
        } catch (\Throwable $e) {
            Log::error("人脸对比失败:" . $e);
            throw new ServiceException($e->getMessage());
        }
    }

    public function faceMatchV4(string $photo1Url, string $photo2Url, string $type = 'LIVE'): array
    {
        $url = $this->config['url']['face_match_v4'] . "?access_token={$this->getAccessToken()}";
        $data = [
            'image' => $this->getBase64FromUrl($photo1Url),
            'image_type' => 'BASE64',
            'face_type' => $type,
            'register_image' => $this->getBase64FromUrl($photo2Url),
            'register_image_type' => 'BASE64',
            'register_face_type' => 'LIVE',
            'register_spoofing_control' => 'NORMAL'
        ];

        try {
            $res = $this->httpClient->post($url, $data);

            if (isset($res['error_code']) && $res['error_code'] > 0) {
                Log::error("身份照片对比出错：{$res['error_code']}：{$res['error_msg']}", compact('photo1Url', 'photo2Url'));
                throw new ServiceException("身份照片对比失败，请上传清晰且符合要求的真人照片，确保图片无遮挡、无模糊、无反光。");
            }

            if (!isset($res['result']['score'])) {
                throw new ServiceException("身份照片对比请求出错");
            }

            return ['score' => $res['result']['score'], 'pass' => $res['result']['score'] > 70];
        } catch (\Throwable $e) {
            Log::error("人脸对比失败:" . $e);
            throw new ServiceException($e->getMessage());
        }
    }

    /**
     * 图片识别
     *
     * @param string $imageUrl
     * @return array
     * @throws GuzzleException
     */
    function imageUnderstanding(string $imageUrl)
    {
        $token = $this->getAccessToken();

        $url = $this->config['url']['image_understanding_req'] . "?access_token={$token}";
        $headers = ['Content-Type' => 'application/json'];
        
        $data = [
            'url' => $imageUrl,
            'question' => "对图片中的设备进行识别并返回巡检清单，每个清单内容简短一点，严格按以下格式输出：设备名称：[识别出的设备名称]巡检项目：1. [核心检查项1] 2. [核心检查项2] 3.[核心检查项3]"
        ];
        
        try {
            $res = $this->httpClient->setHeaders($headers)->post($url, $data);

            if (isset($res['error_code'])) {
                Log::error('图片识别失败', ['error_code' => $res['error_code'], 'error' => $res['error_msg']]);
                throw new ServiceException("图片识别错误, {$res['error_msg']}");
            }

            $getResultUrl = $this->config['url']['image_understanding_resp'] . "?access_token={$token}";
            
            $maxRetries = 10;  // 重试阈值
            $retryDelay = 2;   // 间隔秒数
            $retryCount = 0;   // 重试次数
            
            do {
                sleep($retryDelay);
                $res = $this->httpClient->setHeaders($headers)->post($getResultUrl, ['task_id' => $res['result']['task_id']]);
                Log::info("图片识别轮询结果", ['time' => \date('Y-m-d H:i:s'), 'retry' => $retryCount, 'response' => $res]);
                
                if ($res['result']['ret_code'] == 0) {
                    return $this->parseInspectionResult($res['result']['description']);
                }
                
                $retryCount++;

            } while ($retryCount < $maxRetries && $res['result']['ret_code'] != 0);
            
            Log::error('图片理解轮询失败', [
                'retry_count' => $retryCount,
                'last_response' => $res,
                'task_id' => $res['result']['task_id']
            ]);

            throw new ServiceException('图片理解处理超时，请稍后再试');

        } catch (\Throwable $e) {
            Log::error('图片识别请求失败', [$e]);
            throw new ServiceException($e->getMessage());
        }
    }


    /**
     * 身份证正面信息
     *
     * @param string $imageUrl
     * @return array
     * @throws GuzzleException
     */
    public function idCardFront(string $imageUrl): array
    {
        $res = $this->getIdCardInfo($imageUrl, 'front');

        return [
            'name' => $res['words_result']['姓名']['words'],
            'id_card_number' => $res['words_result']['公民身份号码']['words'],
            'birthday' => $res['words_result']['出生']['words'],
            'sex' => $res['words_result']['性别']['words'],
            'ethnicity' => $res['words_result']['民族']['words']
        ];
    }

    /**
     * 身份证反面信息
     *
     * @param string $imageUrl
     * @return array
     * @throws GuzzleException
     */
    public function idCardBack(string $imageUrl): array
    {
        $res = $this->getIdCardInfo($imageUrl, 'back');

        $info = [
            'expire_date' => $res['words_result']['失效日期']['words'],
            'issue_authority' => $res['words_result']['签发机关']['words'],
            'issue_date' => $res['words_result']['签发日期']['words']
        ];

        if (Carbon::parse($info['expire_date'])->lt(now())) {
            Log::error('身份证已过期', $info);
            throw new ServiceException('身份证已过期');
        }

        return $info;
    }

    /**
     * 身份证识别错误消息
     *
     * @param array $res 身份证返回信息
     * @return string
     */
    public function getErrorMsg(array $res): string
    {
        if (isset($res['error_code'])) {
            return "{$res['error_code']}：{$res['error_msg']}";
        }

        if (isset($res['risk_type']) && $res['risk_type'] != 'normal') {
            return "非身份证原件";
        }

        if (isset($res['card_ps']) && $res['card_ps'] != 0) {
            return "身份证PS或无效";
        }

        if (isset($res['card_quality'])) {
            if (isset($res['card_quality']['IsComplete']) && $res['card_quality']['IsComplete'] == 0) {
                return '身份证边框/四角不完整';
            } elseif (isset($res['card_quality']['IsNoCover']) && $res['card_quality']['IsNoCover'] == 0) {
                return '头像、关键字段有遮挡/马赛克';
            }
        }

        if (isset($res['image_status']) && $res['image_status'] != 'normal') {
            return match ($res['image_status']) {
                'reversed_side' => '身份证正反面颠倒',
                'non_idcard' => '上传的图片中不包含身份证',
                'blurred' => '身份证模糊',
                'other_type_card' => '其他类型证照',
                'over_exposure' => '身份证关键字段反光或过曝',
                'over_dark' => '身份证欠曝（亮度过低）',
                'unknown' => '未知状态'
            };
        }

        if (isset($res['idcard_number_type']) && $res['idcard_number_type'] != 1) {
            return match ($res['idcard_number_type']) {
                -1 => '身份证正面所有字段全为空',
                0 => '身份证证号不合法',
                2 => '身份证证号和性别、出生信息都不一致',
                3 => '身份证证号和出生信息不一致',
                4 => '身份证证号和性别信息不一致'
            };
        }

        return '';
    }

    /**
     * 根据URL获取图片并转换为Base64编码
     *
     * @param string $imageUrl 图片URL
     * @return string|null Base64编码的图片或null（如果失败）
     * @throws \Exception
     */
    /**
     * 解析巡检结果文本为结构化数组
     *
     * @param string $description API返回的描述文本
     * @return array 解析后的结构化数组
     */
    protected function parseInspectionResult(string $description): array
    {
        $result = ['设备名称' => '', '巡检项目' => []];
        
        // 提取设备名称
        if (preg_match('/设备名称：(.+?)\n/', $description, $matches)) {
            $result['设备名称'] = trim($matches[1]);
        }
        
        // 提取巡检项目
        if (preg_match_all('/\d+\.\s*(.+?)(?=\n|$)/', $description, $matches)) {
            $result['巡检项目'] = $matches[1];
        }
        
        return $result;
    }

    protected function getBase64FromUrl(string $imageUrl): ?string
    {
        try {
            // 使用Laravel的HTTP客户端获取图片内容
            $response = Http::timeout(30)->get($imageUrl);

            if ($response->successful()) {
                // 获取图片内容
                $imageContent = $response->body();

                // 转换为Base64并返回
                return base64_encode($imageContent);
            }

            throw new ServiceException('获取图片失败');
        } catch (\Exception $e) {
            Log::error('处理图片时发生错误', [
                'url' => $imageUrl,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}