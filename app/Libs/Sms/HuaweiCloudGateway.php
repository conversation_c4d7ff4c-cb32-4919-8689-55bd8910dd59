<?php

namespace App\Libs\Sms;

use Overtrue\EasySms\Contracts\MessageInterface;
use Overtrue\EasySms\Contracts\PhoneNumberInterface;
use Overtrue\EasySms\Support\Config;
use Overtrue\EasySms\Traits\HasHttpRequest;

/**
 * EasySms 华为云适配器
 */
class HuaweiCloudGateway extends \Overtrue\EasySms\Gateways\Gateway
{

    use HasHttpRequest;

    const BASIC_DATE_FORMAT = 'Ymd\THis\Z';

    protected $apiUrl = 'https://smsapi.cn-south-1.myhuaweicloud.com/sms/batchSendSms/v1';

	/**
	 * @inheritDoc
	 */
	public function send(PhoneNumberInterface $to, MessageInterface $message, Config $config)
    {
        $templateId = $message->getTemplate();
        //签名逻辑
        $data = [
            'from' => in_array($templateId, $config->get('notice_template_ids')) ? $config->get('sender_notice') : $config->get('sender'),
            'to' => (string)$to,
            'templateId' => $message->getTemplate(),
            'templateParas' => json_encode($message->getData()),
            'signature' => $config->get('sign')
        ];

        $body = http_build_query($data);

        $headers = $this->signature($config, payload: $body);
        $headers['Content-Type'] = 'application/x-www-form-urlencoded';

        return $this->request('POST', $this->apiUrl, [
            'headers' => $headers,
            'body' => $body
        ]);
    }

    protected function signature(Config $config, $method='POST', $payload='')
    {
        //格式为ISO8601规范的UTC时间格式
        $date = gmdate(self::BASIC_DATE_FORMAT);

        $headers = ['X-Sdk-Date' => $date];

        //URI
        $canonicalURI = parse_url($this->apiUrl, PHP_URL_PATH);
        if (!str_ends_with($canonicalURI, '/')) {
            $canonicalURI .= '/';
        }

        //Query String
        $canonicalQueryString = '';

        //Headers
        $sortedKeys = array_keys($headers);
        sort($sortedKeys);

        $canonicalHeaders = '';

        foreach ($sortedKeys as $k) {
            $canonicalHeaders .= strtolower($k).':'.trim($headers[$k])."\n";
        }

        $signedHeadersStr = join(';', array_map('strtolower', $sortedKeys));

        //Sign String
        $bodyHash = hash('sha256', $payload);
        $canonicalRequest = "$method\n$canonicalURI\n$canonicalQueryString\n$canonicalHeaders\n$signedHeadersStr\n$bodyHash";
        $requestHash = hash('sha256', $canonicalRequest);
        $stringToSign = "SDK-HMAC-SHA256\n$date\n$requestHash";

        //Sign
        $signature = hash_hmac('sha256', $stringToSign, $config->get('key_secret'));

        $headers['Authorization'] = "SDK-HMAC-SHA256 Access={$config->get('app_key')}, SignedHeaders=$signedHeadersStr, Signature=$signature";

        return $headers;
    }

}
