<?php

namespace App\Libs\Wps;

use GuzzleHttp\Client;

/**
 * Wps Office 格式转换器
 */
class Converter
{

    private $baseUrl = 'https://solution.wps.cn';

    private array $config;

    /**
     * @param Client|null $client 可以传入定制的 Guzzle Http Client
     */
    public function __construct(private ?Client $client=null)
    {
        $this->config = config('services.wps_converter');

        if (!$this->client) {
            $this->client = app(Client::class);
        }
    }

    /**
     * 文档转换成 jpg
     *
     * @param string $url 文档下载地址
     * @param string $filename 文档名称，包含扩展名，例如：文字文稿.docx
     * @param array $optional 其它可选参数，参考 https://solution.wps.cn/docs/convert/to-jpg.html#body
     */
    public function toJpg($url, $filename, $optional=[])
    {
        $data = compact('url', 'filename');
        $optional && $data = array_merge($data, $optional);
        return $this->request('POST', '/api/developer/v1/office/convert/to/jpg', data: $data);
    }

    /**
     * 格式转换结果查询
     * @param string $taskId 格式转换任务 id
     * @return array
     */
    public function getTask($taskId)
    {
        return $this->request('GET', '/api/developer/v1/tasks/'.$taskId);
    }

    /**
     * 发起鉴权请求
     *
     * @param string $method
     * @param string $uri
     * @param array $query URL 查询
     * @param array $data POST、PUT 请求的数据体
     * @return array
     * @throws ConverterExceptoin
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function request($method, $uri, $query=[], $data=[])
    {
        if ($query) {
            $uri .= (str_contains($uri, '?') ? '?' : '').http_build_query($query);
        }

        //请求鉴权
        $date = gmdate('D, d M Y H:i:s') . ' GMT';
        $contentType = 'application/json';
        $body = $data ? json_encode($data) : '';
        $md5 = $method == 'GET' ? md5($uri) : md5($body);
        $auth = 'WPS-2:'.$this->config['app_id'].':'.sha1($this->config['app_secret'].$md5.$contentType.$date);

        $options = [
            'headers' => [
                'Date' => $date,
                'Content-Md5' => $md5,
                'Content-Type' => $contentType,
                'Authorization' => $auth
            ],
            'http_errors' => false
        ];

        if ($body !== null) {
            $options['body'] = $body;
        }

        $response = $this->client->request($method, $this->baseUrl.$uri, $options);

        $res = json_decode((string)$response->getBody(), true);

        if ($res['code'] === 0) {
            return $res['data'];
        } else {
            throw new ConverterExceptoin($res['hint'] ?: $res['message'], $res['code']);
        }
    }

}
