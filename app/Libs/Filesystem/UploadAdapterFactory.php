<?php

namespace App\Libs\Filesystem;

use Illuminate\Support\Facades\Storage;
use League\Flysystem\Local\LocalFilesystemAdapter;
use Overtrue\Flysystem\Qiniu\QiniuAdapter;

class UploadAdapterFactory
{

    public static function create($diskName=null): UploadAdapter
    {
        $disk = Storage::disk($diskName);
        $adapter = $disk->getAdapter();

        return match (true) {
            $adapter instanceof LocalFilesystemAdapter => new LocalUploadAdapter($disk),
            $adapter instanceof QiniuAdapter => new QiniuUploadAdapter($disk),
            default => throw new \RuntimeException("Unsupported disk type '$diskName' adapter to get upload form.")
        };
    }

}
