<?php

namespace App\Libs\Filesystem;

use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;
use Qiniu\Etag;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class LocalUploadAdapter implements UploadAdapter
{

    public function __construct(private ?FilesystemAdapter $disk=null)
    {}

    public function form($prefix, $allowMimeTypes = [], $maxSizeKB = 0, $diskName = null): array
    {
        //本地文件系统上传预设
        $token = json_encode([
            'm' => $allowMimeTypes,
            'k' => $maxSizeKB,
            'd' => $diskName,
            'p' => $prefix,
            't' => time() + 3600 //截止有效时间戳
        ]);

        return [
            'method' => 'POST',
            'url' => route('upload'),
            'name' => 'file',
            'form_params' => [
                'token' => Crypt::encryptString($token)
            ]
        ];
    }

    /**
     * 上传到本地文件
     *
     * 与 UploadAdapter 中定义的稍有不同的是，本地文件适配器的 response 方法直接处理上传请求，没有中间环节
     *
     * @inheritDoc
     */
    public function response(Request $request, $type): array
    {
        $params = $request->validate([
            'token' => 'required|string'
        ]);

        try {
            $token = Crypt::decryptString($params['token']);
            [
                'm' => $allowMimeTypes,
                'k' => $maxSizeKB,
                'd' => $diskName,
                'p' => $prefix,
                't' => $expires
            ] = json_decode($token, true);
        } catch (DecryptException $e) {
             throw new BadRequestHttpException('请求错误。');
        } catch (\Throwable $e) {
            throw new BadRequestHttpException('上传 Token 错误。');
        }

        [
            'm' => $allowMimeTypes,
            'k' => $maxSizeKB,
            'd' => $diskName,
            'p' => $prefix,
            't' => $expires
        ] = json_decode($token, true);

        if ($expires < time()) {
            throw new BadRequestHttpException('上传 Token 已失效。');
        }

        $fileRule = 'file|required';

        if ($allowMimeTypes) {
            $fileRule .= '|mimetypes:'.join(',', $allowMimeTypes);

            //如果上传的是图片文件，则强行校验图片的宽高，以排除不合法的图片和小到无法使用的图片
            if (str_contains($fileRule, 'image/')) {
                $fileRule .= '|dimensions:min_width=10,min_height=10';
            }
        }

        if ($maxSizeKB > 0) {
            //$fileRule .= '|max:1';
        }

        $params = $request->validate([
            'file' => $fileRule
        ], [
            'file' => [
                'required' => '您没有上传任何文件。',
                'mimetypes' => '您上传的文件格式有误。',
                'dimensions' => '您上传的图片文件异常。'
            ]
        ]);

        /** @var \Illuminate\Http\UploadedFile $file */
        $file = $params['file'];

        //生成路径
        $filename = $prefix.'-'.uniqid();

        if ($extension = $file->guessExtension()) {
            $filename .= '.'.$extension;
        }

        $disk = $this->disk ?: Storage::disk($diskName);

        //存储文件
        if (!$disk->putFileAs('tmp', $file, $filename)) {
            throw new BadRequestHttpException('文件上传失败');
        }

        return [
            'url' => $disk->url('tmp/'.$filename),
            'filename' => $file->getClientOriginalName(),
            'key' => $diskName.':tmp/'.$filename,
            'size' => $file->getSize(),
            'mime' => $file->getMimeType()
        ];
    }

    /**
     * @inheritDoc
     */
    public function info($key): array
    {
        $realPath = $this->disk->path($key);

        [$hash, $error] = Etag::sum($realPath);

        if ($error) {
            throw new BadRequestHttpException('文件校验失败。');
        }

        $mime = $this->disk->mimeType($key);

        $info = [
            'hash' => $hash,
            'mime' => $mime
        ];

        if (str_starts_with($mime, 'image/')) {
            [$info['width'], $info['height']] = getimagesize($realPath);
        }

        return $info;
    }

    public function getDisk(): FilesystemAdapter
    {
        return $this->disk;
    }

}
