<?php

namespace App\Libs\Logger;

use Illuminate\Support\Facades\Redis;
use Monolog\Formatter\FormatterInterface;
use Monolog\Formatter\LineFormatter;
use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Level;
use Monolog\LogRecord;

/**
 * 将日志以 Loki 格式推送到 Redis，然后由 Redis Loki Pusher 发送至 Loki
 *
 * 必须至少配置一个 labels，另外配置的 Redis 连接要考虑使用的 db 以及前缀问题，如不符合请考虑另建一个连接，指定 database 以及添加 options 配置数组并配置 prefix 为空字符串。
 *
 * 有 monolog 和 custom 两种方式创建:
 *
 * 'loki' => [
 *     'driver' => 'monolog',
 *     'handler' => LokiRedisHandler::class,
 *     'with' => [
 *         'connection' => 'loki',
 *         'labels' => [
 *             'project' => 'super-topic-server',
 *             'filename' => 'laravel.log'
 *         ],
 *     ],
 *     'formatter' => 'default'
 * ],
 *
 * 'loki_custom' => [
 *     'driver' => 'custom',
 *     'via' => function($config) {
 *         $handler = new LokiRedisHandler(
 *             labels: [
 *                 'project' => 'super-topic-server',
 *                 'filename' => 'laravel.log'
 *             ],
 *             connection: 'loki'
 *         );
 *         $channel = app()->bound('env') ? app()->environment() : 'production';
 *         return new Logger($channel, [$handler]);
 *     }
 * ],
 */
class LokiRedisHandler extends AbstractProcessingHandler
{

    private $redis;

    /**
     * @param int|string|Level|LogLevel::* $level The minimum logging level at which this handler will be triggered
     * @param bool $bubble Whether the messages that are handled can bubble up the stack or not
     * @param array  $labels Static labels send to loki
     * @param string|null $connection Redis connection name
     *
     * @phpstan-param value-of<Level::VALUES>|value-of<Level::NAMES>|Level|LogLevel::* $level
     */
    public function __construct(int|string|Level $level = Level::Debug, bool $bubble = true, protected array $labels=[], string $connection=null)
    {
        parent::__construct($level, $bubble);
        $this->redis = Redis::connection($connection);
    }

    protected function write(LogRecord $record): void
    {
        $labels = array_merge([
            'level' => $record->level->name,
            // Laravel 的 channel 不具备太多的自定义性，基本是一样的，没有添加到 labels 的必要
            // 'channel' => $record['channel']
        ], $this->labels);

        $ts = sprintf('%d000', $record->datetime->format('Uu'));

        $data = [
            'streams' => [
                [
                    'stream' => $labels,
                    'values' => [
                        [$ts, $record->formatted]
                    ]
                ]
            ]
        ];

        $this->redisPush($data);
    }

    private function redisPush($data)
    {
        $this->redis->lpush('loki_push_queue', json_encode($data));
    }

    protected function getDefaultFormatter(): FormatterInterface
    {
        return new LineFormatter(
            '%channel%.%level_name%: %message% %context% %extra%',
            null,
            true
        );
    }

}
