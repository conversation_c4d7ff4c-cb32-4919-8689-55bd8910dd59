<?php

namespace App\Libs\Ai;

use Exception;
use App\Services\HttpClientService;
use Illuminate\Support\Facades\Log;

class TongYi
{
    protected string $APIKey;
    protected string $APIUrl;
    protected string $Model;
    protected array $config;

    public function __construct(protected HttpClientService $httpClient)
    {
        $this->config = config('tongyi');
        $this->APIKey = $this->config['api_key'];
        $this->APIUrl = $this->config['api_url'];
        $this->Model = $this->config['model'];
    }
    function getContext($imagePath) {
        $response = $this->callQwenAPI($imagePath);

        // 解析响应
        $result = json_decode($response, true);
        Log::info('API响应解析', ['result' => $result]); // 记录API响应

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('API响应解析失败: ' . json_last_error_msg());
        }
        
        return $this->extractDeviceName($result);
    }

    // 从API响应中提取设备名称和巡检项
    function extractDeviceName($api_response) {
        if (!isset($api_response['output']['choices'][0]['message']['content'][0]['text'])) {
            return ['设备名称' => '无法识别设备', '巡检项目' => []];
        }
        
        $text = $api_response['output']['choices'][0]['message']['content'][0]['text'];
        $result = ['设备名称' => '', '巡检项目' => []];
        
        $lines = explode("\n", $text);
        foreach ($lines as $line) {
            if (strpos($line, '设备名称：') === 0) {
                $deviceName = trim(str_replace('设备名称：', '', $line));
                
                if ($deviceName === '无') {
                    $result = ['设备名称' => '无法识别设备', '巡检项目' => []];
                    break;
                }

                $result['设备名称'] = $deviceName;
            } elseif (preg_match('/^\d+\.\s*(.+)/', $line, $matches)) {
                $result['巡检项目'][] = $matches[1];
            }
        }
        
        return $result;
    }

    function callQwenAPI($image_url) {
        try {
            // 直接使用URL
            $image_content = [
                [
                    'image' => $image_url
                ]
            ];
            
            $headers = [
                'Content-Type: application/json',
                'Authorization: ' . $this->APIKey
            ];

            $data = [
                'model' => $this->Model,
                'input' => [
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => [
                                ...$image_content,
                                [
                                    'text' => '这张图片中的设备是什么？并列出设备的巡检项目，可以简短一点。请按以下格式回答：
    设备名称：[名称]
    巡检项目：
    1. [检查项1]
    2. [检查项2]
    ...
    请严格按此格式返回，不要额外解释。'
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            $requestData = json_encode($data, JSON_UNESCAPED_SLASHES);
            Log::info('API请求数据', ['request' => $requestData]);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->APIUrl);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            // 临时解决方案：禁用SSL验证（仅用于测试）
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            if ($response === false) {
                throw new Exception('API请求失败: ' . curl_error($ch));
            }
            
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            if ($http_code != 200) {
                throw new Exception("API返回错误状态码: $http_code");
            }
            
            curl_close($ch);

            Log::info('API响应', ['response' => $response]);
            return $response;
        } catch (Exception $e) {
            Log::error('callQwenAPI错误', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}