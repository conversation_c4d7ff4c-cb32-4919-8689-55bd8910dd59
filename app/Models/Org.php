<?php

namespace App\Models;

use App\Core\HashIdAttribute;
use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Models\Org\Admin\Admin;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * 组织机构
 *
 * @property int $id
 * @property string $name 机构名称
 * @property string $alias 别名简称
 * @property string $domain URL中自定义ID
 * @property string $logo logo图路径
 * @property string $verified_status 认证状态
 * @property int $total_students 学员总数
 * @property int $need_photo 是否需要证件照
 * @property int $total_classes 开班总数
 * @property int $total_trained 培训总数
 * @property float $balance 余额
 * @property string $business_license 营业执照
 * @property string|null $business_scope 业务范围
 * @property string $contact 联系方式
 * @property string $service_qrcode 客服二维码图片
 * @property string $official_seal_image 机构公章图片
 * @property int $enable_enroll 机构是否启用报名系统
 * @property string $enroll_bg_img 机构报名系统背景图
 * @property int $area_code 所在地区码
 * @property string $area_text 所在地区名字
 * @property \Carbon\Carbon|null $verified_at 认证时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @property Admin $mainAdmin
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 * @mixin \Eloquent
 */
class Org extends Model
{
    use HasFactory;
    use HashIdAttribute;

    protected $table = 'orgs';

    /** @var string 待认证 */
    const VERIFIED_STATUS_PENDING = 'pending';
    /** @var string 待认证 */
    const VERIFIED_STATUS_VERIFIED = 'verified';
    /** @var string 待认证 */
    const VERIFIED_STATUS_REJECTED = 'rejected';

    protected $fillable = [
        'name',
        'alias',
        'domain',
        'need_photo',
        'logo',
        'verified_status',
        'total_students',
        'total_classes',
        'total_trained',
        'balance',
        'business_license',
        'business_scope',
        'contact',
        'service_qrcode',
        'official_seal_image',
        'area_code',
        'area_text',
        'enable_enroll',
        'enroll_bg_img',
        'merchant_config',
        // 用于merchant_config字段
        'merchant_id',
        'merchant_name',
        'merchant_enable',
        'invoice_enabled',
    ];

    protected $casts = [
        'total_students' => 'int',
        'total_classes' => 'int',
        'total_trained' => 'int',
        'balance' => 'float',
        'area_code' => 'int',
        'verified_at' => 'datetime',
        'merchant_config' => 'array'
    ];

    protected $hidden = ['id', 'created_at', 'updated_at'];

    protected $appends = ['sid', 'logo_url', 'business_license_url', 'service_qrcode_url', 'official_seal_image_url', 'enroll_bg_img_url'];

    protected static function boot()
    {
        parent::boot();

        // 监听saving事件，在保存前处理商户配置字段
        static::saving(function ($org) {
            $org->handleMerchantFields();
        });
    }

    public static function addHashIdSalt()
    {
        return 'Organization';
    }

    public function scopePublicFields($query)
    {
        return $query->select(['id', 'name', 'alias', 'need_photo', 'logo', 'verified_status', 'service_qrcode', 'enable_enroll', 'enroll_bg_img', 'merchant_config']);
    }

    /**
     * 主管理员
     */
    public function mainAdmin(): HasOne
    {
        return $this->hasOne(Admin::class, 'org_id', 'id')->where('is_main', 1);
    }

    public function areaText(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                return $value ? json_decode($value, true) : [];
            },
            set: function ($value) {
                return json_encode($value, JSON_UNESCAPED_UNICODE);
            }
        );
    }

    public function getLogoUrlAttribute(): string
    {
        return $this->logo ? AttachmentService::url(Storage::disk(), $this->logo) : '';
    }

    public function getBusinessLicenseUrlAttribute(): string
    {
        return $this->business_license ? AttachmentService::url(Storage::disk(), $this->business_license) : '';
    }

    public function getServiceQrcodeUrlAttribute(): string
    {
        return $this->service_qrcode ? AttachmentService::url(Storage::disk(), $this->service_qrcode) : '';
    }

    public function getOfficialSealImageUrlAttribute(): string
    {
        return $this->official_seal_image ? AttachmentService::url(Storage::disk(), $this->official_seal_image) : '';
    }
    public function getEnrollBgImgUrlAttribute(): string
    {
        return $this->enroll_bg_img ? AttachmentService::url(Storage::disk(), $this->enroll_bg_img) : '';
    }

    /**
     * 处理商户配置字段
     * 将单独的merchant_id, merchant_name, merchant_enable字段转换为merchant_config JSON
     */
    protected function handleMerchantFields(): void
    {
        $hasMerchantFields = isset($this->attributes['merchant_id']) ||
                           isset($this->attributes['merchant_name']) ||
                           isset($this->attributes['merchant_enable']) ||
                           isset($this->attributes['invoice_enabled']);

        if ($hasMerchantFields) {
            // 获取现有的merchant_config或初始化为空数组
            $merchantConfig = $this->merchant_config ?? [];

            // 处理merchant_id
            if (isset($this->attributes['merchant_id'])) {
                $merchantConfig['mch_id'] = $this->attributes['merchant_id'];
                unset($this->attributes['merchant_id']);
            }

            // 处理merchant_name
            if (isset($this->attributes['merchant_name'])) {
                $merchantConfig['mch_name'] = $this->attributes['merchant_name'];
                unset($this->attributes['merchant_name']);
            }

            // 处理merchant_enable
            if (isset($this->attributes['merchant_enable'])) {
                $merchantConfig['enabled'] = $this->attributes['merchant_enable'];
                unset($this->attributes['merchant_enable']);
            }

            // 处理invoice_enabled
            if (isset($this->attributes['invoice_enabled'])) {
                $merchantConfig['invoice_enabled'] = $this->attributes['invoice_enabled'];
                unset($this->attributes['invoice_enabled']);
            }

            // 设置merchant_config
            $this->attributes['merchant_config'] = json_encode($merchantConfig);
        }
    }
}
