<?php

namespace App\Models;

use App\Models\Inspect\HiddenDanger;
use App\Models\Inspect\UserAmaze;
use App\Models\Org\Enrollment;
use App\Models\Org\Student;
use App\Models\User\UserBalanceRecord;
use App\Services\Common\AttachmentService;
use App\Traits\HasApiTokens;
use Hidehalo\Nanoid\Client;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as AuthUser;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Storage;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 * @property int $id
 * @property string $uuid UUID
 * @property int $org_id 机构ID
 * @property string $nickname 昵称
 * @property string $avatar 头像
 * @property string $phone 手机号
 * @property string $password 密码
 * @property int $credit 积分
 * @property string $balance 用户余额
 * @property int $status 状态
 * @property \Carbon\Carbon $last_logged_at 最近登录时间
 * @property string $last_logged_ip 最近登录IP
 * @property \Carbon\Carbon $last_active_at 最近活跃时间
 * @property \Carbon\Carbon $join_org_at 加入组织时间
 * @property string $last_active_ip 最近活跃IP
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read Org $org
 */
class User extends AuthUser implements JWTSubject
{
    use HasApiTokens, HasFactory, Notifiable;

    const STATUS_FORBID = 0; //禁用（禁止登录）
    const STATUS_NORMAL = 1; //正常
    const STATUS_MUTE = 2; //禁言
    const STATUS_DESTROYING = 3;
    const STATUS_DELETED = 4;

    protected $table = 'users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = ['id', 'uuid', 'org_id', 'nickname', 'avatar', 'phone', 'password', 'credit', 'balance', 'status', 'last_logged_at', 'last_logged_ip', 'last_active_at', 'join_org_at', 'last_active_ip', 'created_at', 'updated_at'];


    protected $casts = ['id' => 'integer', 'org_id' => 'integer', 'credit' => 'integer', 'status' => 'integer', 'last_logged_at' => 'datetime', 'last_active_at' => 'datetime', 'join_org_at' => 'datetime', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = ['id', 'password', 'remember_token', 'last_logged_at', 'last_logged_ip', 'last_active_at', 'last_active_ip', 'created_at', 'updated_at'];

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public function scopePublicFields($query)
    {
        return $query->select(['id', 'uuid', 'nickname', 'avatar', 'phone', 'status', 'created_at']);
    }

    /**
     * 头像转换为访问地址
     */
    public function avatar(): Attribute
    {
        $disk = Storage::disk();
        return Attribute::make(
            get: function ($value) use ($disk) {
                if ($value) {
                    return AttachmentService::url($disk, $value);
                } else {
                    return config('app.url') . '/static/images/avatar/default.jpg';
                }
            },
        );
    }

    public function org()
    {
        return $this->belongsTo(Org::class, 'org_id');
    }

    public function orgUser()
    {
        return $this->belongsTo(Enrollment::class, 'id', 'user_id');
    }

    public function students()
    {
        return $this->hasMany(Student::class, 'user_id');
    }

    // 用户发布的隐患
    public function hiddenDangers()
    {
        return $this->hasMany(HiddenDanger::class);
    }

    // 用户顶踩记录
    public function amazes()
    {
        return $this->hasMany(UserAmaze::class);
    }

    public function getBalanceShowAttribute()
    {
        return UserBalanceRecord::query()->where('user_id', $this->id)->exists();
    }

    /**
     * 通过手机号查找用户
     *
     * @param string $phone
     * @return static|null
     */
    public static function getUserByPhone($phone)
    {
        return User::query()->where('phone', $phone)->first();
    }

    /**
     * 获取用户积分
     *
     * @param int $userId
     * @return int|null
     */
    public static function getUserCredit(int $userId): int|null
    {
        $credit = self::query()->where('id', $userId)->value('credit');
        if (!$credit) {
            return null;
        }

        return $credit;
    }

    /**
     * 创建用户
     *
     * @param string $phone 手机号
     * @return User
     */
    public static function create(string $phone): User
    {
        $user = new User();
        $user->phone = $phone;
        $user->uuid = (new Client())->generateId();
        $user->nickname = '用户' . mt_rand(1000, 9999);
        $user->save();

        return $user;
    }
}
