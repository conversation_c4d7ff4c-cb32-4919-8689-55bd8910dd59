<?php

namespace App\Models\Crawler;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


/**
 * 知识星球爬虫数据
 *
 * @property int $id
 * @property int $topic_id
 * @property int $file_id
 * @property string $name 文件名
 * @property string $create_time 该内容的创建时间，也是翻页的基础
 * @property int $content_id 保存的内容 ID
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 */
class CrawlerZsxq extends Model
{
    use HasFactory;

    protected $table = 'crawler_zsxq';

    public static function add($topicId, $fileId, $name, $createTime, $contentId=0): self
    {
        $record = new self();
        $record->topic_id = $topicId;
        $record->file_id = $fileId;
        $record->name = $name;
        $record->create_time = $createTime;
        $record->content_id = $contentId;
        $record->save();

        return $record;
    }

}
