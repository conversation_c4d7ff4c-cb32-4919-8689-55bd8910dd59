<?php

namespace App\Models\Attachment;

use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property string $disk 存储空间名称
 * @property string $path 存储路径
 * @property string $filename 文件名
 * @property string $mime 文件类型
 * @property string $etag 文件哈希，采用七牛 QETag 算法
 * @property int $filesize 文件大小
 * @property int $width 图片宽度
 * @property int $height 图片高度
 * @property \Carbon\Carbon $created_at
 * @property-read string $url 文件的访问地址
 */
class AttachmentFile extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'attachment_files';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'disk', 'path', 'filename', 'mime', 'etag', 'filesize', 'width', 'height', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'filesize' => 'integer', 'width' => 'integer', 'height' => 'integer', 'created_at' => 'datetime'];

    const UPDATED_AT = null;

	public function delete()
	{
		//删除关联的文件
		$fs = Storage::disk($this->disk);

		if ($fs->fileExists($this->path)) {
			$fs->delete($this->path);
		}

		return parent::delete();
	}

    public function relations()
    {
        return $this->hasMany(AttachmentRelation::class, 'file_id', 'id');
    }

    public function getUrlAttribute()
    {
        return AttachmentService::url(Storage::disk($this->disk), $this->path);
    }

}
