<?php

namespace App\Models\Train;

use App\Core\Enums\BusinessType;
use App\Models\Attachment\AttachmentRelation;
use App\Models\User\UserFavorite;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use App\Core\HashIdAttribute;

/**
 * @property int $id
 * @property int $topic_id 题库ID
 * @property int $chapter_id
 * @property int $section_id
 * @property int $example_id
 * @property string $intro 描述
 * @property int $type 题型  1 单选，2 多选，3 判断 4 问答
 * @property int $judge_correct 判断题对错  1 错，2 对
 * @property string $answer 问答题的答案
 * @property string $analysis 解析
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property string $deleted_at
 * @property SubjectOption[]|Collection $options
 */
class Subject extends Model
{
    use HashIdAttribute;

    /**
     * The table associated with the model.
     */
    protected $table = 'train_subjects';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'topic_id', 'chapter_id', 'section_id', 'example_id', 'intro', 'type', 'judge_correct', 'answer', 'analysis', 'created_at', 'updated_at', 'deleted_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'topic_id' => 'integer', 'chapter_id' => 'integer', 'section_id' => 'integer', 'example_id' => 'integer', 'type' => 'integer', 'judge_correct' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];


    protected $hidden = [
        'id',
    ];
    protected $appends = ['sid'];


    /** @var int 单选 */
    const TYPE_SC = 1;
    /** @var int 多选 */
    const TYPE_MC = 2;
    /** @var int 判断 */
    const TYPE_TF = 3;
    /** @var int 问答 */
    const TYPE_QA = 4;
    protected static function addHashIdSalt()
    {
        return "TRAIN-subjects";
    }

    public function topic()
    {
        return $this->belongsTo(Topic::class, 'topic_id');
    }

    public function chapter()
    {
        return $this->belongsTo(Chapter::class, 'chapter_id');
    }

    public function section()
    {
        return $this->belongsTo(Section::class, 'section_id');
    }

    public function example()
    {
        return $this->belongsTo(Example::class, 'example_id');
    }

    public function options()
    {
        return $this->hasMany(SubjectOption::class,'subject_id', 'id');
    }

    public function option()
    {
        return $this->hasMany(SubjectOption::class,'subject_id', 'id');
    }

    public function testOption()
    {
        return $this->hasOne(TestSubject::class,'subject_id', 'id');
    }

    public function favorite()
    {
        return $this->hasOne(UserFavorite::class, 'business_id', 'id')->where('business_type', BusinessType::Subject);
    }

    public function attachments()
    {
        return $this->hasMany(AttachmentRelation::class, 'target_id', 'id')->where('target_type', BusinessType::Subject);
    }

    public function scopePublicFields($query)
    {
        $query->select(['id', 'topic_id', 'intro', 'type', 'judge_correct']);
    }
}
