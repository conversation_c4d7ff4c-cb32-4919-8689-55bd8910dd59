<?php

namespace App\Models\Train;

use App\Core\HashIdAttribute;
use App\Core\OrderableInterface;
use App\Models\Order\Order;
use App\Models\Org\Enrollment;
use App\Models\User\UserOwnTopic;
use App\Services\Org\EnrollCourseService;
use App\Services\Org\EnrollmentService;
use App\Services\Org\StudentService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $course_category_id 关联讲解内容分类（默认为0）
 * @property int $course_content_id 关联讲解内容（默认为0）
 * @property string $name 名称
 * @property string $amount 金额
 * @property int $exam_time 考试时间
 * @property int $pass_score 及格分数
 * @property array $exam_config 考试配置
 * @property string $next_exam_at 下次考试时间
 * @property int $sort 排序
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property Subject[] $subjects
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 */
class Topic extends Model implements OrderableInterface
{
    use HashIdAttribute;

    protected $hidden = [
        'id',
    ];
    /**
     * The table associated with the model.
     */
    protected $table = 'train_topics';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'course_category_id', 'course_content_id', 'name', 'amount', 'exam_time', 'pass_score', 'exam_config', 'next_exam_at', 'sort', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'course_category_id' => 'integer',
        'course_content_id' => 'integer',
        'exam_time' => 'integer',
        'pass_score' => 'integer',
        'exam_config' => 'array',
        'sort' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $appends = ['sid'];

    public static function examConfig(){
        $jsonString = '{"judge": {"count": 0, "score": 0}, "single_choice": {"count": 60, "score": 1}, "multiple_choice": {"count": 20, "score": 2}}';
        $examConfig = json_decode($jsonString, true);

        return $examConfig;
    }

    /**
     * 题库价格
     * @return array[]
     */
    public static function amount(){
        return [
            'month' => [
                'bays' => 30,
                'amount' => config('topic_amount.topic_mount_amount'),
                'type' => 'month'
            ],
            'year' => [
                'bays' => 365,
                'amount' => config('topic_amount.topic_year_amount'),
                'type' => 'year'
            ]
        ];

    }

    public function subjects()
    {
        return $this->hasMany(Subject::class, 'topic_id');
    }

    protected static function addHashIdSalt()
    {
        return "Train-Topic";
    }

    public function orderDelivery(Order $order): bool
    {
        $expiredAt = $order->extend ? now()->addDays($order->extend['valid_days']) : null;
        $orgId = $order->extend['org_id'] ?? 0;

        $userOwnTopic = UserOwnTopic::query()
            ->where('user_id', $order->user_id)
            ->where('topic_id', $order->business_id)
            ->where('expired_at','>', now()->toDateTimeString())
            ->orderByDesc('expired_at');

        if (isset($order->extend['org_id'])) {
            $userOwnTopic->where('org_id', $order->extend['org_id']);
        }

        $userOwnTopic = $userOwnTopic->first();

        if ($userOwnTopic && $userOwnTopic->expired_at && $order->extend['valid_days']){
            $expiredAt = Carbon::parse($userOwnTopic->expired_at)->addDays($order->extend['valid_days']);
        }

        $enrollmentID = 0;
        if ($orgId) {
            $student = StudentService::check($orgId, $order->user_id, '', $order->user->phone, '');
            $enrollment = EnrollmentService::createEnroll($orgId, $order->user_id, $student->id, Enrollment::TYPE_TOPIC, $order->business_id);
            $enrollmentID = $enrollment->id;
        }
        UserOwnTopic::add($order->user_id, $order->business_id, $expiredAt, $orgId, $enrollmentID);

        return true;
    }

    public function orderName(): string
    {
        return $this->name;
    }

    public function scopePublicFields($query)
    {
        $query->select(['id', 'course_category_id', 'name', 'amount', 'exam_time', 'pass_score', 'exam_config']);
    }

    public function subjectCount()
    {
        $this->subject_count = $this->subjects()->count();
    }
}
