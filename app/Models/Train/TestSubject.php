<?php

namespace App\Models\Train;

use App\Core\HashIdAttribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $topic_id 题库ID
 * @property int $test_id 测试ID
 * @property int $subject_id 题目ID
 * @property string $option_id 选项ID（判断题默认为0，多选题多个ID逗号相连）
 * @property int $correct 是否正确 0 否，1 是
 * @property string $answer 用户填写的答案
 * @property int $wrong_removed 是否移出错题记录 0 否，1 是
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read Subject $subject
 */
class TestSubject extends Model
{
    use HashIdAttribute;

    protected $hidden = [
        'id',
        'user_id',
        'topic_id',
        'subject_id',
    ];
    /**
     * The table associated with the model.
     */
    protected $table = 'train_test_subjects';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'topic_id', 'test_id', 'subject_id', 'option_id', 'correct', 'answer', 'wrong_removed', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'topic_id' => 'integer', 'test_id' => 'integer', 'subject_id' => 'integer', 'correct' => 'integer', 'wrong_removed' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];


    /** @var int 正确 */
    const  CORRECT_RIG = 1;

    /** @var int 错误 */
    const  CORRECT_ERR = 2;

    /** @var int 已经移除错误记录 */
    const  WRONG_REMOVED = 1;

    /** @var int 没有移除错误记录 */
    const  WRONG_REMOVED_NOT = 0;

    protected static function addHashIdSalt()
    {
        return "TRAIN-Test-Subject";
    }

    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class, 'subject_id', 'id');
    }
}
