<?php

namespace App\Models\Train;

use App\Core\HashIdAttribute;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $topic_id 题库ID
 * @property int $chapter_id
 * @property int $current_subject_id 当前题目ID
 * @property int $type 题型  0 顺序，1 模拟（随机抽取题目），2 专项训练-单选，3 专项训练-多选
 * @property int $org_id 机构ID
 * @property int $enroll_id 报名表ID
 * @property int $status 状态  0 进行中，1 已结束
 * @property string $end_at 考试结束时间
 * @property int $subject_count 总题数（当时的总题数）
 * @property int $subject_completed_count 已做题数
 * @property int $subject_correct_count 正确题数
 * @property int $subject_ignore_count 代表不评分题数
 * @property string $score 成绩
 * @property bool $passed 是否通过
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read Topic $topic
 * @property-read Collection<TestSubject> $subjects
 */
class Test extends Model
{

    use HashIdAttribute;

    protected $hidden = [
        'id', 'user_id', 'topic_id', 'chapter_id', 'current_subject_id',
    ];
    /**
     * The table associated with the model.
     */
    protected $table = 'train_tests';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'topic_id', 'chapter_id', 'current_subject_id', 'type', 'org_id', 'enroll_id', 'status', 'end_at', 'subject_count', 'subject_completed_count', 'subject_correct_count', 'subject_ignore_count', 'score', 'passed', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'topic_id' => 'integer', 'chapter_id' => 'integer', 'current_subject_id' => 'integer', 'type' => 'integer', 'org_id' => 'integer', 'enroll_id' => 'integer', 'status' => 'integer', 'subject_count' => 'integer', 'subject_completed_count' => 'integer', 'subject_correct_count' => 'integer', 'subject_ignore_count' => 'integer', 'passed' => 'boolean', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $appends = ['sid', 'test_time', 'test_level'];

    /** @var int 进行中 */
    const STATUS_TO = 0;

    /** @var int 结束 */
    const STATUS_STOP = 1;


    /** @var int 顺序练习 */
    const TYPE_SEQ = 1;

    /** @var int 专项单选训练 */
    const TYPE_SINGLE = 2;
    /** @var int 模拟考试 */
    const TYPE_EMU = 3;

    /** @var int 错误练习 */
    const TYPE_ERR = 4;

    /** @var int 收藏练习 */
    const TYPE_COLL = 5;

    /** @var int 专项多个训练 */
    const TYPE_MULTI = 6;

    /** @var int 专项判断训练 */
    const TYPE_JUDGE = 7;

    /** @var int 章节训练 */
    const TYPE_CHAPTER = 8;

    /** @var int 考试 */
    const TYPE_EXAM = 9;


    /** @var int 没有移除错误记录 */
    const  WRONG_REMOVED_NOT = 0;
    /** @var int 已经移除错误记录 */
    const  WRONG_REMOVED = 1;

    /** @var int 回答正确 */
    const  CORRECT_RIGHT = 1;

    /** @var int 回答错误 */
    const  CORRECT_ERR = 2;

    /** @var int 考试限时（单位：分） */
    public static int $limitTime = 60;

    /** @var int 考试通过 */
    const PASSED = 1;

    /** @var int 考试没通过 */
    const PASSED_NOT = 0;
    protected static function addHashIdSalt()
    {
        return "TRAIN-Test";
    }

    public function getTestTimeAttribute(): string
    {
        if ($this->end_at) {

            $starTime = Carbon::parse($this->created_at);
            $endTime = Carbon::parse($this->end_at);
            $diff = $starTime->diff($endTime);

            return sprintf("%02d:%02d", $diff->i, $diff->s);
        } else {

            return "00:00";
        }
    }

    public function getTestLevelAttribute(): string
    {
        return $this->passed ? '及格' : '不及格';
    }

    public function topic(): BelongsTo
    {
        return $this->belongsTo(Topic::class);
    }

    public function subjects(): HasMany
    {
        return $this->hasMany(TestSubject::class, 'test_id', 'id');
    }

}
