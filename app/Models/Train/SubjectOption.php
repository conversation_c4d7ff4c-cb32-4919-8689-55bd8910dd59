<?php

namespace App\Models\Train;

use App\Core\HashIdAttribute;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $subject_id 题目ID
 * @property int $sn 序号（同时用来排序）
 * @property string $name 选项名称
 * @property int $is_correct 是否正确答案  0 否，1 是
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class SubjectOption extends Model
{
    use HashIdAttribute;

    /**
     * The table associated with the model.
     */
    protected $table = 'train_subject_options';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'subject_id', 'sn', 'name', 'is_correct', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'subject_id' => 'integer', 'sn' => 'integer', 'is_correct' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $appends = ['sid'];

    protected $hidden = [
        'id',
    ];
    /** @var int 正确答案 */
    const CORRECT = 1;
    /** @var int 错误答案 */
    const CORRECT_NOT = 2;

    protected static function addHashIdSalt()
    {
        return "TRAIN-Subjects-Option";
    }
}
