<?php

namespace App\Models;

use App\Core\Enums\BusinessType;
use App\Libs\AsyncTasks\AsyncTaskType;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $type 任务类型 0 七牛视频转码，1 WPS开放平台文档转图片
 * @property string $task_id 开放平台任务ID
 * @property BusinessType $business_type 业务类型
 * @property int $business_id 业务ID
 * @property string $task 序列化的任务实例
 * @property string $callback_data 任务回调数据
 * @property string $result 任务的本地处理结果
 * @property int $status 状态 0 进行中，1 已完成，2 失败
 * @property string $description 任务状态描述
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class AsyncTask extends Model
{

    const STATUS_PROCESSING = 0;
    const STATUS_FINISHED = 1;
    const STATUS_FAILED = 2;

    /**
     * The table associated with the model.
     */
    protected $table = 'async_tasks';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'type', 'task_id', 'business_type', 'business_id', 'task', 'callback_data', 'result', 'status', 'description', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'type' => AsyncTaskType::class, 'business_type' => BusinessType::class, 'business_id' => 'integer', 'status' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * 当给 description 字段赋值时，自动截断到 255 个字符
     */
    protected function description(): Attribute
    {
        return Attribute::make(
            set: fn(string $value) => mb_substr($value, 0, 255)
        );
    }

}
