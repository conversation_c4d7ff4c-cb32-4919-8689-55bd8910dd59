<?php

namespace App\Models\Qa;

use App\Core\Enums\BusinessType;
use App\Core\HashIdAttribute;
use App\Models\Attachment\AttachmentFile;
use App\Models\Attachment\AttachmentRelation;
use App\Models\User;
use App\Services\Common\AttachmentService;
use App\Services\Qa\QuestionService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;
use Laravel\Scout\Searchable;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property string $title 标题
 * @property string $content 内容
 * @property array|null $images 图片
 * @property int $anonymous 是否匿名  0 否，1 是
 * @property int $answer_count 回答数量
 * @property int $views 浏览数
 * @property int $views_add 固定增加的浏览数
 * @property int $status 状态  0 待审核，1 正常，2 已拒绝
 * @property string $recommend_at 推荐时间（默认NULL代表不推荐，推荐按时间倒排）
 * @property string $reply_at 最新回复时间
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read User $user
 * @property-read Answer[]|Collection $answers
 * @property-read Tag[]|Collection $tags
 * @property-read TagRelation[]|Collection $tag_relations
 */
class Question extends Model
{
    use HashIdAttribute, SoftDeletes;

    use HasFactory, Searchable;

    /**
     * The table associated with the model.
     */
    protected $table = 'qa_questions';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'title', 'content', 'images', 'anonymous', 'answer_count', 'views', 'views_add', 'status', 'recommend_at', 'reply_at', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'images' => 'array', 'anonymous' => 'integer', 'answer_count' => 'integer', 'views' => 'integer', 'views_add' => 'integer', 'status' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'user_id', 'deleted_at', 'views_add'];

    protected $appends = ['sid'];

    /** @var int 等待审核 */
    const STATUS_PENDING = 0;

    /** @var int 通过审核 */
    const STATUS_NORMAL = 1;

    /** @var int 拒绝 */
    const STATUS_REJECTED = 2;


    protected static function addHashIdSalt()
    {
        return "QA-Question";
    }

    public function scopePublicFields($query)
    {
        return $query->select(['id', 'user_id', 'title', 'content', 'images', 'anonymous', 'answer_count', 'views', 'created_at']);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 显示叠加的计数
     */
    public function views(): Attribute
    {
        $add = $this->views_add ?? 0;
        //这里的 set 是为了不对实际修改 views 时造成影响
        return Attribute::make(
            get: fn($value, array $attrs) => $value + $add,
            set: fn($value, array $attrs) => $value - $add
        );
    }

    /**
     * 获取模型的可索引的数据。
     *
     * @return array|null
     */
    public function toSearchableArray(): array|null
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'answer_count' => $this->answer_count,
            'views' => $this->views + $this->views_add,
            'status' => $this->status,
            'nickname' => $this->user?->nickname ?? '',
            'title' => $this->title ?? '',
            'content' => $this->content ?? '',
            'created_at' => Carbon::parse($this->created_at)->toDateTimeString(),
            'updated_at' => Carbon::parse($this->reply_at)->toDateTimeString(),
            'tag_ids' => $this->tagRelations?->pluck('tag_id')->toArray() ?: []
        ];
    }

    public function answers()
    {
        return $this->hasMany(Answer::class);
    }

    //收藏作用域查询
    public function scopeFavorite($query, $data = [])
    {
        return $query->select(['id', 'title', 'answer_count']);
    }

    public function tags()
    {
        return $this->hasManyThrough(Tag::class, TagRelation::class, 'question_id', 'id', 'id', 'tag_id');
    }

    public function tagRelations()
    {
        return $this->hasMany(TagRelation::class, 'question_id');
    }

    protected function imageUrls(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (!$this->images) {
                    return [];
                }

                $images = [];
                $disk = Storage::disk(config('heguibao.storage.pub'));

                foreach ($this->images as $image) {
                    $images[] = AttachmentService::url($disk, $image['path']);
                }

                return $images;
            }
        );
    }

    public function scopeWithLikeState(Builder $query, $userId)
    {
        return $query->withExists(['attitude as is_like' => function ($query) use ($userId) {
            $query->where('user_id', $userId)->where('attitude', User\UserAttitude::ATTITUDE_LIKE);
        }]);
    }

    /**
     * 点赞
     * @return HasOne
     */
    public function attitude()
    {
        return $this->hasOne(User\UserAttitude::class, 'business_id', 'id')
            ->where("business_type", BusinessType::Question);

    }

    /**
     * 点赞
     * @return HasMany
     */
    public function attitudes()
    {
        return $this->hasMany(User\UserAttitude::class, 'business_id', 'id')
            ->where("business_type", BusinessType::Question);
    }

}
