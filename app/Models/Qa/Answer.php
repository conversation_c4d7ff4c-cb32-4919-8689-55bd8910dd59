<?php

namespace App\Models\Qa;

use App\Core\AttitudeInterface;
use App\Core\HashIdAttribute;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $question_id 问题ID
 * @property string $content 内容
 * @property int $anonymous 匿名回答
 * @property int $like_count 赞数量
 * @property int $dislike_count 踩数量
 * @property int $status 状态  0 待审核，1 正常，2 已拒绝
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @mixin \Eloquent
 */
class Answer extends Model implements AttitudeInterface
{
    use HashIdAttribute, SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'qa_answers';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'question_id', 'content', 'anonymous', 'like_count', 'dislike_count', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'question_id' => 'integer', 'anonymous' => 'integer','like_count' => 'integer', 'dislike_count' => 'integer', 'status' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $appends = ['sid'];

    protected $hidden = ['id', 'question_id', 'user_id'];

    /** @var int 等待审核 */
    const STATUS_PR = 0;

    /** @var int 通过审核 */
    const STATUS_NORMAL = 1;

    /** @var int 拒绝 */
    const STATUS_RJ = 2;

    /** @var int 删除 */
    const STATUS_DE = 3;

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function question()
    {
        return $this->hasOne(Question::class, 'id', 'question_id');
    }
    public function attitude()
    {
        return $this->hasMany(User\UserAttitude::class,'business_id', 'id');
    }

    protected static function addHashIdSalt()
    {
        return "QA-Answer";
    }

    public function getAttitudeKeys(): array
    {
        return ['like_count', 'dislike_count'];
    }

    public function scopePublicFields($query)
    {
        $query->select(['id', 'user_id', 'question_id', 'anonymous', 'like_count', 'dislike_count']);
    }
}
