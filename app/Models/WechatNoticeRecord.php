<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * WechatNoticeRecord
 *
 * @property int $id
 * @property int $user_id 用户ID
 * @property string $open_id openID
 * @property string $template_id 模板ID
 * @property int $status 状态 1 成功，2 失败
 * @property string $type 消息通知类型
 * @property string $content 发送内容
 * @property string $error_message 错误消息
 * @property \Illuminate\Support\Carbon $created_at
 * @mixin \Eloquent
 */
class WechatNoticeRecord extends Model
{
    use HasFactory;

    protected $table = 'wechat_notice_records';

    protected $fillable = ['id', 'user_id', 'open_id', 'template_id', 'status', 'content', 'error_message', 'created_at'];

    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'status' => 'integer', 'created' => 'datetime'];

    const UPDATED_AT = null;

    /** @var int 成功状态 */
    const STATUS_SUCCESS = 1;

    /** @var int 失败状态 */
    const STATUS_FAIL = 2;

    /** @var string 资料修改 */
    const TYPE_MATERIAL_MODIFY = 'material_modify';

    /** @var string 付款 */
    const TYPE_PAYMENT = 'payment';

    /** @var string 方案确认 */
    const TYPE_SOLUTION_CONFIRM = 'solution_confirm';

    /** @var string 提交邮箱 */
    const TYPE_SUBMIT_EMAIL = 'submit_email';

    /**
     * 获取消息类型文案
     *
     * @return string[]
     */
    public static function getTypeText(): array
    {
        return [
            self::TYPE_MATERIAL_MODIFY => '资料修改',
            self::TYPE_PAYMENT => '付款',
            self::TYPE_SOLUTION_CONFIRM => '方案确认',
            self::TYPE_SUBMIT_EMAIL => '提交邮箱'
        ];
    }

    /**
     * 新增微信模板消息发送记录
     *
     * @param int $userId 用户ID
     * @param string $openId openID
     * @param string $templateId 模板ID
     * @param int $status 状态
     * @param string $type 消息类型
     * @param string $content 发送内容
     * @param string $errorMessage 错误消息
     * @return WechatNoticeRecord
     */
    public static function add(int $userId, string $openId, string $templateId, int $status, string $type,
                               string $content, string $errorMessage): WechatNoticeRecord
    {
        $record = new self();
        $record->user_id = $userId;
        $record->open_id = $openId;
        $record->template_id = $templateId;
        $record->status = $status;
        $record->type = $type;
        $record->content = $content;
        $record->error_message = $errorMessage;
        $record->save();

        return $record;
    }
}
