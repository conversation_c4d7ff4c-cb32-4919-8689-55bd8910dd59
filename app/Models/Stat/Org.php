<?php

namespace App\Models\Stat;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 机构统计
 *
 * @property int $id
 * @property int $org_id 机构ID
 * @property string $date 日期
 * @property int $enrollments 新增学员数
 * @property int $classes 新增班级数
 * @property int $exams 当日考试人数
 * @property int $trained 当日培训结束人数
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 */
class Org extends Model
{
    use HasFactory;

    protected $table = 'stat_orgs';

    protected $fillable = [
        'id',
        'org_id',
        'date',
        'enrollments',
        'classes',
        'exams',
        'trained',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'enrollments' => 'integer',
        'classes' => 'integer',
        'exams' => 'integer',
        'trained' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $attributes = [
        'enrollments' => 0,
        'classes' => 0,
        'exams' => 0,
        'trained' => 0,
    ];

    public function org()
    {
        return $this->belongsTo(\App\Models\Org::class, 'org_id');
    }
}
