<?php

namespace App\Models\Stat;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $date 日期
 * @property int $user_register 注册用户数
 * @property int $user_active 活跃用户数
 * @property int $payment_order 支付订单数量
 * @property string $payment_amount 支付金额
 * @property int $payment_user 支付用户数
 * @property int $payment_credit_order 支付积分订单数量
 * @property string $payment_credit_amount 支付积分金额
 * @property int $payment_credit_user 支付积分用户数
 * @property int $payment_course_order 支付课程订单数量
 * @property string $payment_course_amount 支付课程金额
 * @property int $payment_course_user 支付课程用户数
 * @property int $payment_topic_order 支付题库订单数量
 * @property string $payment_topic_amount 支付题库金额
 * @property string $platform_material_amount 支付资料金额
 * @property int $payment_material_user 资料付款人数
 * @property int $payment_topic_user 支付题库用户数
 * @property int $consume_credit 消费积分
 * @property int $content 内容总数量
 * @property int $content_material 资料数量
 * @property int $content_course 课程数量
 * @property int $content_news 资讯数量
 * @property int $content_view 浏览总数（资料、资讯、课程总浏览数）
 * @property int $content_download 资料下载数
 * @property int $content_special 专题数量
 * @property int $platform_content 平台内容总数量
 * @property int $platform_content_material 平台资料总数量
 * @property int $platform_content_course 平台课程总数量
 * @property int $platform_content_news 平台资讯总数量
 * @property int $practise 做题次数
 * @property int $question 提问数
 * @property int $answer 回答数
 * @property int $search 搜索次数
 * @property int $collect 收藏数
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class DailyOverview extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'stat_daily_overviews';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'id', 'date', 'user_register', 'user_active', 'payment_order', 'payment_amount', 'payment_user', 'payment_credit_order', 'payment_credit_amount',
        'payment_credit_user', 'payment_course_order', 'payment_course_amount', 'payment_course_user', 'payment_topic_order', 'payment_topic_amount',
        'payment_topic_user', 'platform_material_amount', 'payment_material_user', 'consume_credit', 'content', 'content_material', 'content_course',
        'content_news', 'content_view', 'content_download', 'content_special', 'platform_content', 'platform_content_material',
        'platform_content_course', 'platform_content_news', 'practise', 'question', 'answer', 'search', 'collect', 'created_at', 'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'user_register' => 'integer',
        'user_active' => 'integer',
        'payment_order' => 'integer',
        'payment_user' => 'integer',
        'payment_credit_order' => 'integer',
        'payment_credit_user' => 'integer',
        'payment_course_order' => 'integer',
        'payment_course_user' => 'integer',
        'payment_topic_order' => 'integer',
        'payment_topic_user' => 'integer',
        'consume_credit' => 'integer',
        'content' => 'integer',
        'content_material' => 'integer',
        'content_course' => 'integer',
        'content_news' => 'integer',
        'content_view' => 'integer',
        'content_download' => 'integer',
        'content_special' => 'integer',
        'platform_content' => 'integer',
        'platform_content_material' => 'integer',
        'platform_content_course' => 'integer',
        'platform_content_news' => 'integer',
        'practise' => 'integer',
        'question' => 'integer',
        'answer' => 'integer',
        'search' => 'integer',
        'collect' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $attributes = [
        'user_register' => 0,
        'user_active' => 0,
        'payment_order' => 0,
        'payment_amount' => '0.00',
        'payment_user' => 0,
        'payment_credit_order' => 0,
        'payment_credit_amount' => '0.00',
        'payment_credit_user' => 0,
        'payment_course_order' => 0,
        'payment_course_amount' => '0.00',
        'payment_course_user' => 0,
        'payment_topic_order' => 0,
        'payment_topic_amount' => '0.00',
        'payment_topic_user' => 0,
        'platform_material_amount' => '0.00',
        'payment_material_user' => 0,
        'consume_credit' => 0,
        'content' => 0,
        'content_material' => 0,
        'content_course' => 0,
        'content_news' => 0,
        'content_view' => 0,
        'content_download' => 0,
        'content_special' => 0,
        'platform_content' => 0,
        'platform_content_material' => 0,
        'platform_content_course' => 0,
        'platform_content_news' => 0,
        'practise' => 0,
        'question' => 0,
        'answer' => 0,
        'search' => 0,
        'collect' => 0,
    ];
}
