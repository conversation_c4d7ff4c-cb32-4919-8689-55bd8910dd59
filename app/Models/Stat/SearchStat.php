<?php

namespace App\Models\Stat;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $date 日期
 * @property string $keyword 关键词
 * @property int $search_count 搜索次数
 * @mixin \Eloquent
 */
class SearchStat extends Model
{
    use HasFactory;

    protected $table = 'search_stats';
    public $timestamps = false;

    protected $casts = [
        'id' => 'integer',
        'date' => 'date',
        'keyword' => 'string',
        'search_count' => 'integer'
    ];

    protected $fillable = [
        'id',
        'date',
        'keyword',
        'search_count'
    ];
}
