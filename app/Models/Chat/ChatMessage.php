<?php

namespace App\Models\Chat;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

/**
 * 问答会话
 *
 * @property int $id
 * @property int $user_id
 * @property int $session_id
 * @property string $prompt 提示词
 * @property string $completion 结论
 * @property int $like 赞操作 0：无 1：赞 2：踩
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 */
class ChatMessage extends Model
{
    protected $table = 'chat_messages';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'session_id', 'prompt', 'completion', 'like', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'session_id' => 'integer', 'like' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['user_id'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
