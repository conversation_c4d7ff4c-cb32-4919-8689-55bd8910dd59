<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property int $type 0 幻灯，1 金刚区
 * @property string $name 名称
 * @property string $image 图片
 * @property int $sort 排序
 * @property string $url 目标地址
 * @property int $enable 是否启用 0 否，1 是
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class SettingBooth extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'setting_booths';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'type', 'name', 'image', 'sort', 'url', 'enable', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'type' => 'integer', 'sort' => 'integer', 'enable' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $appends = ['image_url'];

    /** @var int 启用 */
    const ENABLE = 1;

    /** @var int 不启用 */
    const ENABLE_NOT = 0;

    /** @var int 幻灯片 */
    const TYPE_SL = 0;

    /** @var int 金刚区 */
    const TYPE_KK = 1;

    protected function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => Storage::disk()->url($attributes['image'])
        );
    }

}
