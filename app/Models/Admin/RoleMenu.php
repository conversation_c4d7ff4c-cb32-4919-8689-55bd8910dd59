<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $role_id
 * @property int $menu_id
 * @property int $child
 * @property \Carbon\Carbon $created_at
 */
class RoleMenu extends Model
{
    const UPDATED_AT = null;

    /**
     * The table associated with the model.
     */
    protected $table = 'admin_role_menus';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'role_id', 'menu_id', 'child', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'role_id' => 'integer', 'menu_id' => 'integer', 'child' => 'integer', 'created_at' => 'datetime'];

    public function menu()
    {
        return $this->hasOne(Menu::class, 'id', 'menu_id');
    }
}
