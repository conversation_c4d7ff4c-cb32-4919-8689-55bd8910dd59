<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $admin_id 管理员ID
 * @property string $remark 备注
 * @property string $context
 * @property \Carbon\Carbon $created_at
 */
class AdminOperate extends Model
{
    const UPDATED_AT = null;

    /**
     * The table associated with the model.
     */
    protected $table = 'admin_operates';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'admin_id', 'remark', 'context', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'admin_id' => 'integer', 'created_at' => 'datetime'];
}
