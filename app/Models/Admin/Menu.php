<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $parent_id
 * @property int $type 类型 0.菜单 1.事件
 * @property string $name 菜单名称
 * @property string $icon 菜单图标
 * @property string $method 请求方式 GET POST PUT DELETE
 * @property string $route_name 路由名称
 * @property string $route_path 路由地址
 * @property string $component 页面组件
 * @property int $sort
 * @property int $status 状态 0:禁用 1:启用
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Menu extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'admin_menus';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'parent_id', 'type', 'name', 'icon', 'method', 'route_name', 'route_path', 'component', 'sort', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'parent_id' => 'integer', 'type' => 'integer', 'sort' => 'integer', 'status' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    const TYPE_MENU = 0;

    const TYPE_API = 1;
}
