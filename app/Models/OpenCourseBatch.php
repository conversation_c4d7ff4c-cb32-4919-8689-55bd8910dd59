<?php

namespace App\Models;

use App\Models\Admin\Admin;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 开课批次
 *
 * @property int $id
 * @property int $count 开通人数
 * @property int $admin_id 操作人
 * @property \Illuminate\Support\Carbon $created_at 创建时间
 * @mixin \Eloquent
 */
class OpenCourseBatch extends Model
{
    use HasFactory;

    const UPDATED_AT = null;

    protected $table = 'open_course_batches';

    protected $fillable = ['id', 'count', 'admin_id', 'created_at'];

    protected $casts = ['count' => 'integer', 'admin_id' => 'integer', 'created_at' => 'datetime'];

    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }
}
