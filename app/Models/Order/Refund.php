<?php

namespace App\Models\Order;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $order_id 订单ID
 * @property int $payment_id 支付ID
 * @property string $refund_no 退款唯一编号
 * @property string $refund_amount 退款金额
 * @property int $status 状态  0 退款中，1 已退款
 * @property string $remark 退款理由
 * @property string $finished_at 退款完成时间
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Refund extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'order_refunds';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'order_id', 'payment_id', 'refund_no', 'refund_amount', 'status', 'remark', 'finished_at', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'order_id' => 'integer', 'payment_id' => 'integer', 'status' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
}
