<?php

namespace App\Models\Order;

use App\Core\Enums\BusinessType;
use App\Models\Org\Enroll;
use App\Models\Org\EnrollOperateRecord;
use App\Models\User;
use App\Services\Org\EnrollOperateRecordService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqids\Sqids;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property string $order_no 订单编号
 * @property string $total_amount 订单金额
 * @property string $payment_amount 实际已支付金额（可能涉及到优惠或者测试，实际支付金额会有差异）
 * @property string $title 订单标题
 * @property int $status 状态  0 待支付，1 已付款，2 已发起退款，3 退款处理中，4，已退款
 * @property BusinessType $business_type 业务类型
 * @property int $business_id 业务ID
 * @property string $payment_method 支付方式，payment 直接支付，balance 余额支付
 * @property int $payment_id 支付ID
 * @property array $extend 扩展信息
 * @property \Carbon\Carbon|null $payment_at 支付时间
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read User $user
 * @property-read string $wechat_pay_mode 微信支付模式
 * @property-read int $org_id 机构ID
 * @property-read int $sub_mch_id 机构商户ID
 */
class Order extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'orders';
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'order_no', 'total_amount', 'payment_amount', 'title', 'status', 'business_type', 'business_id', 'extend', 'payment_method', 'payment_id', 'payment_at', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'status' => 'integer', 'business_type'=>BusinessType::class, 'business_id' => 'integer', 'extend'=>'array', 'payment_method'=>'string', 'payment_id' => 'integer', 'payment_at'=>'datetime', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'user_id', 'business_id', 'payment_id', 'deleted_at'];

    /** @var int 待支付 */
    const STATUS_UNPAID = 0;

    /** @var int 已支付 */
    const STATUS_PAID = 1;

    /** @var int 已发起退款 */
    const STATUS_REF_PEN = 2;

    /** @var int 退款处理中 */
    const STATUS_REF_HAVE = 3;

    /** @var int 已退款 */
    const STATUS_REF_DONE = 4;

    /** @var string 普通即时支付 */
    const PAY_METHOD_PAY = 'payment';

    /** @var string 余额支付 */
    const PAY_METHOD_BALANCE = 'balance';

    protected static function booted(): void
    {
        static::saved(function ($model) {
            // 机构报名流转记录
            if ($model->business_type === BusinessType::Enroll && $model->status === self::STATUS_PAID) {
                $enroll = Enroll::query()
                    ->where('org_id', $model->org_id)
                    ->where('user_id', $model->user_id)
                    ->where('enroll_config_id', $model->business_id)
                    ->first();
                app(EnrollOperateRecordService::class)->create(
                    $enroll->id,
                    EnrollOperateRecord::TYPE_PAYMENT,
                    0,
                    '用户支付报名费用，订单金额：' . $model->total_amount . '，实付金额：' . $model->payment_amount
                );
            }
        });
    }

    public function resource()
    {
        return $this->morphTo('resource', 'business_type', 'business_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function payment()
    {
        return $this->hasOne(Payment::class)->where('status', '!=', Payment::STATUS_UNPAID);
    }

    public function scopePublicFields($query)
    {
        $query->select(['id', 'user_id', 'order_no', 'total_amount', 'payment_amount', 'title', 'status', 'business_type', 'business_id']);
    }

    /**
     * 微信支付模式，下单时存入extend扩展字段里，默认为 normal普通商户
     * 机构报名，目前使用 service 服务商模式进行支付
     * @return Attribute
     */
    protected function wechatPayMode(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->extend['wechat_pay_mode'] ?? 'normal',
        );
    }

    /**
     * 获取订单里面的机构ID
     * @return Attribute
     */
    protected function orgId(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->extend['org_id'] ?? 0,
        );
    }

    /**
     * 获取订单里面的机构商户id
     * @return Attribute
     */
    protected function subMchId(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->extend['sub_mch_id'] ? (new Sqids(minLength: 6))->decode($this->extend['sub_mch_id'])[0] : 0,
        );
    }
}
