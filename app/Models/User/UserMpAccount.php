<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * UserMpAccount
 *
 * @property int $id
 * @property int $user_id 用户ID
 * @property string $platform 平台
 * @property string $open_id openID
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 * @mixin \Eloquent
 */
class UserMpAccount extends Model
{
    use HasFactory;

    protected $table = 'user_mp_accounts';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'platform', 'open_id', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'user_id'];

    const PLATFORM_WECHAT_MP = 'wechat.mp';

    public function scopePublicFields($query)
    {
        return $query->select(['id', 'user_id', 'platform', 'open_id']);
    }
}
