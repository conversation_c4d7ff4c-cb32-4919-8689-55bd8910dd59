<?php

namespace App\Models\User;

use App\Core\Enums\BusinessType;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property BusinessType $business_type 业务类型
 * @property int $business_id 业务ID
 * @property \Carbon\Carbon $created_at
 */
class UserFavorite extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'user_favorites';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'business_type', 'business_id', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'business_type'=>BusinessType::class, 'business_id' => 'integer', 'created_at' => 'datetime'];

    protected $hidden = ['business_id'];

    public function setUpdatedAt($value)
    {
        return $this;
    }

    public function resource()
    {
        return $this->morphTo('resource', 'business_type', 'business_id');
    }

}
