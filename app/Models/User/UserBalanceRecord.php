<?php

namespace App\Models\User;

use App\Core\Enums\BusinessType;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 用户余额记录
 *
 * @property int $id
 * @property int $user_id
 * @property string $type 类型，income=收入，expenses=支出
 * @property string $origin_balance 原始余额
 * @property string $amount 消费金额
 * @property string $business_type 业务类型
 * @property int $business_id 业务ID
 * @property string $remark 描述
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 */
class UserBalanceRecord extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'user_balance_records';

    /** @var string 收入 */
    const TYPE_RECHARGE = 'income';

    /** @var string 支出 */
    const TYPE_CONSUME = 'expenses';

    protected $hidden = ['id', 'user_id', 'business_id'];

    protected $casts = ['business_type'=>BusinessType::class];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
