<?php

namespace App\Models\User;

use App\Models\Cms\Category;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseProgress;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 *
 *
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $content_id 资料ID
 * @property string $classify 归类 material 资料，course 课程，news 资讯
 * @property \Carbon\Carbon $expired_at
 * @property \Carbon\Carbon $created_at
 * @property string $deleted_at
 * @property-read User $user
 * @property-read Content $content
 * @property int $org_id 机构id
 * @property int $enroll_id 学员id
 * @property-read mixed $is_expiration
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ContentCourseProgress> $progresses
 * @property-read int|null $progresses_count
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent whereClassify($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent whereContentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent whereEnrollId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent whereExpiredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent whereOrgId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnContent withoutTrashed()
 * @mixin \Eloquent
 */
class UserOwnContent extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'user_own_contents';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'content_id', 'classify', 'expired_at', 'created_at', 'deleted_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'content_id' => 'integer', 'created_at' => 'datetime'];

    protected $hidden = ['id', 'content_id'];

    const UPDATED_AT = null;
    const CLASSIFY_COURSE = 'course';

    /** @var int 机构课程课程包默认有效期 */
    const ORG_EXPIRED_MOUTH_DEFAULT = 9;

    public function content()
    {
        return $this->belongsTo(Content::class, 'content_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 判断是否过期
     */
    protected function isExpiration(): Attribute
    {
        return new Attribute(
            get: function() {
                if ($this->expired_at) {
                   $dataNow = Carbon::parse($this->expired_at);
                   if (now()->gt($dataNow)) {
                       return true;
                   }
                }
                return false;
            }
        );
    }

    /**
     * 学习内容章节进度
     * @return void
     */
    public function progresses()
    {
        return $this->hasMany(ContentCourseProgress::class,'content_id', 'content_id');
    }

}
