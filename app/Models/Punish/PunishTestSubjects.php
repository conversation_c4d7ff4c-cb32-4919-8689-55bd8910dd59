<?php

namespace App\Models\Punish;

use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property int $user_id
 * @property int $topic_id
 * @property int $test_id
 * @property int $subject_id
 * @property int $option_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects query()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects whereOptionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects whereSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects whereTestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects whereTopicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTestSubjects whereUserId($value)
 * @mixin \Eloquent
 */
class PunishTestSubjects  extends Model
{
    protected $table = 'punish_test_subjects';

    public function option()
    {
        return $this->hasOne(PunishSubjectOption::class, 'id', 'option_id');
    }

    public function subject()
    {
        return $this->hasOne(PunishSubject::class, 'id', 'subject_id');

    }
}
