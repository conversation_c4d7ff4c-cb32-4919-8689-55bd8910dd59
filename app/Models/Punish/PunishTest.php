<?php

namespace App\Models\Punish;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property int $user_id
 * @property int $topic_id
 * @property int $amount_tol
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTest query()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTest whereAmountTol($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTest whereTopicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTest whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTest whereUserId($value)
 * @mixin \Eloquent
 */
class PunishTest  extends Model
{
    protected $table = 'punish_tests';

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function topic()
    {
        return $this->belongsTo(PunishTopic::class, 'topic_id', 'id');
    }
}
