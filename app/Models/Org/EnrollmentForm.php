<?php
namespace App\Models\Org;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $org_id 机构ID
 * @property string $title 报名标题
 *
 * 自定义字段配置
 * // type - 类型
 * //    text - 单行文件
 * //    textarea - 多行文本
 * //    radio - 单选
 * //    checkbox - 多选
 * //    select - 单选下拉框
 * //    datetime - 日期时间
 * //    region - 地区选择
 * //    sign - 签名（承诺书等）
 * //    photo - 形象照（picture 的别名，带有固定的 placeholder）
 * //    pic - 单图上传
 * //    multi-pic - 多图上传
 * //    work_unit - 工作单位
 * // name - 字段名
 * // placeholder - 占位文字
 * // desc - 描述，主要用于承诺书的内容，也可用于其它文字超出于 placeholder 之外的内容
 * // id - 随机生成的字符串，（UUID 或 uniqid 等）用以和学员 extra 以及更新的关联
 * // required - 是否必填
 * // options - 单选或多选时的选项
 *
 * @property array $fields 自定义字段配置
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 */
class EnrollmentForm extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'org_enrollment_forms';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'org_id',
        'title',
        'fields',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'fields' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    const TYPE_TEXT = 'text';
    const TYPE_TEXTAREA = 'textarea';
    const TYPE_RADIO = 'radio';
    const TYPE_SELECT = 'select';
    const TYPE_CHECKBOX = 'checkbox';
    const TYPE_REGION = 'region';
    const TYPE_SIGN = 'sign';
    const TYPE_PHOTO = 'photo';
    const TYPE_PIC  = 'pic';
    const TYPE_FILE = 'file';
    const TYPE_DATE = 'date';
    const TYPE_WORK_UNIT = 'work_unit';
    const TYPE_CASCADE = 'cascade';

    public function scopePublicFields($query)
    {
        $query->select(['id', 'org_id', 'title', 'fields']);
    }

    public static function fileTypes(): array
    {
        return [self::TYPE_PIC, self::TYPE_FILE, self::TYPE_PHOTO];
    }
}
