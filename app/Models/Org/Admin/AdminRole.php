<?php

namespace App\Models\Org\Admin;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $admin_id
 * @property int $role_id
 * @property \Carbon\Carbon $created_at
 * @property Role $role
 */
class AdminRole extends Model
{
    const UPDATED_AT = null;

    /**
     * The table associated with the model.
     */
    protected $table = 'org_admin_roles';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'admin_id', 'role_id', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'admin_id' => 'integer', 'role_id' => 'integer', 'created_at' => 'datetime'];

    public function role()
    {
        return $this->hasOne(Role::class, 'id', 'role_id');
    }

    public function admin()
    {
        return $this->hasOne(Admin::class, 'id', 'admin_id');
    }
}
