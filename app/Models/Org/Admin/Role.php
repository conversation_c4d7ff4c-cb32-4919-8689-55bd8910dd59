<?php

namespace App\Models\Org\Admin;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $name 角色名称
 * @property string $code 角色标识
 * @property string $desc 角色描述
 * @property int $status 状态 0:禁用 1:启用
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Role extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'org_roles';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'name', 'code', 'desc', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'status' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    const CODE_ADMIN = 'R_ADMIN';           // 管理员
    const CODE_AUDITOR = 'R_AUDITOR';       // 财务
    const CODE_TEACHER = 'R_TEACHER';       // 老师
}
