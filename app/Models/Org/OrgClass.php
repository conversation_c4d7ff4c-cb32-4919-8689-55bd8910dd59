<?php
namespace App\Models\Org;

use App\Core\HashIdAttribute;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePack;
use App\Models\Org\Admin\Admin;
use App\Models\Train\Topic as TrainTopic;
use App\Services\Org\OrgClassService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $org_id 机构ID
 * @property string $name 班级名称
 * @property int $manager_id 子账号/老师ID
 * @property string $type 班级类型
 * @property int $resource_id 课程或题库ID
 * @property int $status 状态
 * @property int $total_enrollments 培训人数
 * @property int $total_course_finished 学时完成人数
 * @property int $total_examined 考试人数
 * @property int $total_passed 及格人数
 * @property int $exam_enabled 是否考试
 * @property int $exam_limit 是否限制考试次数
 * @property int $exam_limit_count 限考次数
 * @property string $exam_condition 考试条件
 * @property string $exam_mode 考试方式
 * @property Carbon $exam_at 考试时间
 * @property int $face_capture_enabled 是否开启人脸抓拍
 * @property int $face_capture_count 人脸抓拍次数
 * @property int $template_custom 是否自定义下载模板
 * @property int $template_hour_id 学时证明模板ID
 * @property int $template_archive_id 一期一档模板ID
 * @property Carbon $start_at 开始时间
 * @property Carbon $end_at 结束时间
 * @property Carbon $actual_end_at 实际结束时间
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property ContentCourse|TrainTopic $resource 资源
 * @property-read Admin $manager 老师
 */
class OrgClass extends Model
{
    use HashIdAttribute;

    /**
     * The table associated with the model.
     */
    protected  $table = 'org_classes';

    /** @var string 课程类型 */
    public const TYPE_COURSE = 'course';
    /** @var string 题库类型 */
    public const TYPE_TOPIC = 'topic';
    public const TYPE_COURSE_PACK = 'course_pack';


    /** @var string 随时考试 */
    public const EXAM_CONDITION_ANYTIME = 'anytime';
    /** @var string 课程结束后考试 */
    public const EXAM_CONDITION_AFTER_COURSE = 'after_course';
    /** @var string 固定时间考试 */
    public const EXAM_CONDITION_FIXED_TIME = 'fixed_time';

    /** @var string 全部设备可考试 */
    public const EXAM_MODE_ALL = 'all';
    /** @var string 仅移动端考试 */
    public const EXAM_MODE_MOBILE_ONLY = 'mobile_only';
    /** @var string 仅PC端考试 */
    public const EXAM_MODE_PC_ONLY = 'pc_only';

    /** @var int 未开始 */
    public const STATUS_DEFAULT = 0;

    /** @var int 进行中 */
    public const STATUS_STARTING = 1;

    /** @var int 已结束 */
    public const STATUS_FINISHED = 2;

    /** @var array|string[] 关联资源 */
    public static array $resource = [
        self::TYPE_COURSE => ContentCourse::class,
        self::TYPE_TOPIC => TrainTopic::class,
        self::TYPE_COURSE_PACK => ContentCoursePack::class,
    ];


    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'id',
        'org_id',
        'name',
        'manager_id',
        'type',
        'resource_id',
        'status',
        'total_enrollments',
        'total_course_finished',
        'total_examined',
        'total_passed',
        'exam_enabled',
        'exam_limit',
        'exam_limit_count',
        'exam_condition',
        'exam_mode',
        'exam_at',
        'face_capture_enabled',
        'face_capture_count',
        'template_custom',
        'template_hour_id',
        'template_archive_id',
        'start_at',
        'end_at',
        'actual_end_at',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'manager_id' => 'integer',
        'resource_id' => 'integer',
        'status' => 'integer',
        'total_enrollments' => 'integer',
        'total_course_finished' => 'integer',
        'total_examined' => 'integer',
        'total_passed' => 'integer',
        'exam_enabled' => 'boolean',
        'exam_limit' => 'boolean',
        'exam_limit_count' => 'integer',
        'face_capture_enabled' => 'boolean',
        'face_capture_count' => 'integer',
        'template_custom' => 'boolean',
        'template_hour_id' => 'integer',
        'template_archive_id' => 'integer',
        'exam_at' => 'datetime',
        'start_at' => 'datetime',
        'end_at' => 'datetime',
        'actual_end_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $hidden = ['id', 'created_at', 'updated_at'];

    protected $appends = ['sid'];

    public static function addHashIdSalt()
    {
        return 'OrganizationClass';
    }

    public function resource()
    {
        return $this->morphTo('resource', 'type', 'resource_id');
    }

    public function course()
    {
        return $this->belongsTo(Course::class, 'resource_id', 'course_id');
    }

    public function manager()
    {
        return $this->belongsTo(Admin::class, 'manager_id', 'id');
    }

    public function toResource(bool $toHour = false)
    {
        if ($this->resource && in_array($this->type, [OrgClass::TYPE_COURSE, OrgClass::TYPE_COURSE_PACK])) {
            $content = $this->resource->content;

            $content && $this->resource->name = $content->title;

            if ($toHour) {
                $this->resource->hour = OrgClassService::getHour($this);
            }
        }

        return $this->resource;
    }
}
