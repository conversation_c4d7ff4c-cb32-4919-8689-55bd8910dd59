<?php

namespace App\Models\Org;

use App\Models\Order\Order;
use App\Models\Org;
use App\Models\User;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 机构报名模型
 *
 * @property int $id
 * @property int $org_id 机构ID
 * @property int $user_id 用户ID
 * @property int $student_id 学员ID
 * @property int $enroll_config_id 报名配置ID
 * @property int|null $order_id 订单ID
 * @property int $status 状态 0 待审核，1 已驳回，2 待支付，3 已支付，4 退款中，5 退款关闭，6 退款成功
 * @property int $is_invoiced 是否已开具发票 0 未开票，1 已开票
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read string $status_label 状态标签
 */
class Enroll extends Model
{
    protected $table = 'org_enrolls';

    // 状态常量
    const STATUS_PENDING_REVIEW = 0;    // 待审核
    const STATUS_REJECTED = 1;          // 已驳回
    const STATUS_PENDING_PAYMENT = 2;   // 待支付
    const STATUS_PAID = 3;              // 已支付
    const STATUS_REFUNDING = 4;         // 退款中
    const STATUS_REFUND_CLOSED = 5;     // 退款关闭
    const STATUS_REFUNDED = 6;          // 退款成功

    const AUDIT_STATUS_AGREE = 1;       // 审核同意
    const AUDIT_STATUS_REJECT = 2;      // 审核驳回

    protected $fillable = [
        'org_id',
        'user_id',
        'student_id',
        'enroll_config_id',
        'order_id',
        'status',
        'is_invoiced',
    ];

    protected $casts = [
        'org_id' => 'integer',
        'user_id' => 'integer',
        'student_id' => 'integer',
        'enroll_config_id' => 'integer',
        'order_id' => 'integer',
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $appends = ['status_label'];

    /**
     * 关联机构
     */
    public function org(): BelongsTo
    {
        return $this->belongsTo(Org::class, 'org_id', 'id');
    }

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联学员
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }

    /**
     * 关联报名配置
     */
    public function enrollConfig(): BelongsTo
    {
        return $this->belongsTo(EnrollConfig::class, 'enroll_config_id', 'id');
    }

    /**
     * 关联订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    /**
     * 关联操作记录
     */
    public function operateRecords(): HasMany
    {
        return $this->hasMany(EnrollOperateRecord::class, 'enroll_id', 'id');
    }

    /**
     * 状态标签
     */
    public static function getStatusLabels(): array
    {
        return [
            self::STATUS_PENDING_REVIEW => '待审核',
            self::STATUS_REJECTED => '已驳回',
            self::STATUS_PENDING_PAYMENT => '待支付',
            self::STATUS_PAID => '已支付',
            self::STATUS_REFUNDING => '退款中',
            self::STATUS_REFUND_CLOSED => '退款关闭',
            self::STATUS_REFUNDED => '已退款',
        ];
    }

    /**
     * 获取状态标签
     */
    protected function statusLabel(): Attribute
    {
        return Attribute::make(
            get: fn() => self::getStatusLabels()[$this->status] ?? '未知状态',
        );
    }
}
