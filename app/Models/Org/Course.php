<?php
namespace App\Models\Org;

use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Services\Org\TopicService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCourseSection;


/**
 * @property int $id
 * @property int $org_id 机构ID
 * @property int $course_id 课程ID
 * @property int $hour 学时
 * @property string $price_original 原价
 * @property string $price_sell 售价
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 *
 * @property ContentCourse $contentCourse 课程其他信息
 * @property Content $content 课程信息
 *
 */
class Course extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'org_courses';

    /** @var int 隐藏状态 */
    public const STATUS_HIDDEN = 0;
    /** @var int 显示状态 */
    public const STATUS_VISIBLE = 1;

    /** @var string 类型-章 */
    const TYPE_CHAPTER = 'chapter';
    /** @var string 类型-节 */
    const TYPE_SECTION = 'section';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'org_id',
        'course_id',
        'hour',
        'duration',
        'lessons',
        'price_original',
        'price_sell',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'course_id' => 'integer',
        'hour' => 'integer',
        'price_original' => 'float',
        'price_sell' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 模型事件 - 创建后
     */
    protected static function booted()
    {
        // 创建课程时自动创建章节
        static::created(function (self $course) {
            /** @var ContentCourse $contentCourse */
            $contentCourse = $course->contentCourse()->with([
                'chapters' => function ($query) {
                    $query->where('status', ContentCourseChapter::STATUS_SHOW)->orderByRaw('sort desc,id asc');
                },
                'chapters.sections' => function ($query) {
                    $query->where('status', ContentCourseSection::STATUS_SHOW)->orderByDesc('sort');
                },
                'topic'
            ])->first();

            if (!$contentCourse) {
                return;
            }

            // 旧代码begin-脚本执行后删除
            $insertSubs = [];

            foreach ($contentCourse->chapters as $chapter) {
                // 添加章
                $insertSubs[] = [
                    'org_id' => $course->org_id,
                    'course_id' => $course->course_id,
                    'type' => CourseSub::TYPE_CHAPTER,
                    'resource_id' => $chapter->id,
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];

                // 添加节
                foreach ($chapter->sections as $section) {
                    $insertSubs[] = [
                        'org_id' => $course->org_id,
                        'course_id' => $course->course_id,
                        'type' => CourseSub::TYPE_SECTION,
                        'resource_id' => $section->id,
                        'created_at' => Carbon::now()->toDateTimeString(),
                        'updated_at' => Carbon::now()->toDateTimeString(),
                    ];
                }
            }

            if (!empty($insertSubs)) {
                CourseSub::query()->insert($insertSubs);
            }
            // 旧代码end-脚本执行后删除

            $chapters = [];
            $totalChapterDuration = 0;

            // 添加课程题库
            if ($contentCourse->topic_id > 0 && $contentCourse->topic) {
                TopicService::checkCreate($course->org_id, $contentCourse->topic);
            }

            // 兼容首次执行迁移脚本时，还未添加这两个字段的情况
            if (!isset($course->duration) || !isset($course->lessons)) {
                return;
            }

            foreach ($contentCourse->chapters as $chapter) {
                $sections = [];
                $totalSectionDuration = 0;

                foreach ($chapter->sections as $section) {
                    $sections[] = [
                        'id' => $section->id,
                        'enabled' => true,
                        'hour' => $contentCourse->studyHour($section->actual_duration),
                        'duration' => $section->actual_duration ?? 0
                    ];
                    $totalSectionDuration += $section->actual_duration;
                }

                $totalChapterHour = $contentCourse->studyHour($totalSectionDuration);

                $chapters[] = [
                    'id' => $chapter->id,
                    'enabled' => true,
                    'hour' => (string)$totalChapterHour,
                    'duration' => (string)$totalSectionDuration,
                    'sections' => $sections
                ];
                $totalChapterDuration += $totalSectionDuration;
            }

            $course->duration = $totalChapterDuration;
            $course->hour = $contentCourse->studyHour($totalChapterDuration, false);
            $course->lessons = json_encode($chapters);
            $course->save();
        });
    }

    public function scopePublicFields($query)
    {
        return $query->select(['id', 'org_id', 'course_id', 'hour', 'duration', 'price_sell']);
    }

    public function enrollment()
    {
        return $this->morphOne(Enrollment::class, 'recourse');
    }

    public function content(): HasOne
    {
        return $this->hasOne(Content::class, 'id', 'course_id');
    }

    public function contentCourse(): HasOne
    {
        return $this->hasOne(ContentCourse::class, 'content_id', 'course_id');
    }
}
