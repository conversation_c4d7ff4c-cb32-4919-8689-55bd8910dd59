<?php

namespace App\Models\Org;

use App\Services\Common\AttachmentService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property int $org_id 机构ID
 * @property string $name 模板名称
 * @property string $type 模板类别
 * @property int $is_default 是否默认
 * @property string $tpl_path 模板文件路径
 * @property string $format 下载格式
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class Template extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'org_templates';

    /** @var string 下载格式-word */
    const FORMAT_WORD = 'word';
    /** @var string 下载格式-excel */
    const FORMAT_EXCEL = 'excel';
    /** @var string 下载格式-pdf */
    const FORMAT_PDF = 'pdf';

    /** @var string 模板类别-学时证明 */
    public const TYPE_HOUR_CERT = 'hour_cert';
    /** @var string 模板类别-学生档案 */
    public const TYPE_STUDENT_ARCHIVE = 'student_archive';
    /** @var string 模板类别-学习记录 */
    public const TYPE_HOUR_RECORD = 'hour_record';
    /** @var string 模板类别-报名表单 */
    public const TYPE_ENROLLMENT_FORM = 'org_enrollment_form';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'org_id',
        'name',
        'type',
        'is_default',
        'tpl_path',
        'format',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'is_default' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $appends = [
        'tpl_path_url'
    ];

    public function getTplPathUrlAttribute()
    {
        return $this->tpl_path ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->tpl_path) : '';
    }
}
