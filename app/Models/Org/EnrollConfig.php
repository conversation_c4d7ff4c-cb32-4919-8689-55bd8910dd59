<?php

namespace App\Models\Org;

use App\Core\OrderableInterface;
use App\Models\Order\Order;
use App\Models\Org;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 机构报名配置模型
 *
 * @property int $id
 * @property int $org_id 机构ID
 * @property string $title 报名课程名称
 * @property float $amount 报名价格
 * @property int $sort 排序
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class EnrollConfig extends Model implements OrderableInterface
{
    protected $table = 'org_enroll_config';

    protected $fillable = [
        'org_id',
        'title',
        'amount',
        'sort',
    ];

    protected $casts = [
        'org_id' => 'integer',
        'amount' => 'decimal:2',
        'sort' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联机构
     */
    public function org(): BelongsTo
    {
        return $this->belongsTo(Org::class, 'org_id', 'id');
    }

    /**
     * 关联报名记录
     */
    public function enrolls(): HasMany
    {
        return $this->hasMany(Enroll::class, 'enroll_config_id', 'id');
    }

    public function orderDelivery(Order $order): bool
    {
        // TODO::修改用户报名记录状态 改为已支付
        logger()->debug("报名机构课程订单", [
            'org_id' => $this->org_id,
            'order' => $order->toArray(),
        ]);
        $enroll = Enroll::query()
            ->where('org_id', $this->org_id)
            ->where('user_id', $order->user_id)
            ->where('enroll_config_id', $this->id)
            ->where('status', Enroll::STATUS_PENDING_PAYMENT)
            ->first();
        if ($enroll instanceof Enroll) {
            $enroll->status = Enroll::STATUS_PAID;
            $enroll->order_id = $order->id;
            $enroll->save();
            return true;
        }
        logger()->error("没有符合支付条件的报名记录", [
            'org_id' => $this->org_id,
            'user_id' => $order->user_id,
            'enroll_config_id' => $this->id,
            'order' => $order->toArray(),
        ]);
        return false;
    }

    public function orderName(): string
    {
        return $this->title;
    }
}
