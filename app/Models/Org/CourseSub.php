<?php

namespace App\Models\Org;

use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCourseSection;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $org_id 机构ID
 * @property int $course_id 课程ID
 * @property string $type 类型:章或节
 * @property int $resource_id 章或节ID
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class CourseSub extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'org_course_subs';

    /** @var string 类型-章 */
    const TYPE_CHAPTER = 'chapter';
    /** @var string 类型-节 */
    const TYPE_SECTION = 'section';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'org_id',
        'course_id',
        'type',
        'resource_id',
        'status',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'course_id' => 'integer',
        'resource_id' => 'integer',
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /** @var array|string[] 关联资源 */
    public static array $resource = [
        self::TYPE_CHAPTER => ContentCourseChapter::class,
        self::TYPE_SECTION => ContentCourseSection::class,
    ];

    public function resource()
    {
        return $this->morphTo('resource', 'type', 'resource_id');
    }

    public function chapter()
    {
        return $this->belongsTo(ContentCourseChapter::class, 'resource_id', 'id');
    }

    public function section()
    {
        return $this->belongsTo(ContentCourseSection::class, 'resource_id', 'id');
    }

    /**
     * 获取课程的内容信息
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function courseContent()
    {
        return $this->belongsTo(Content::class, 'course_id', 'id');
    }

    /**
     * 获取指定机构课程的章节资源树
     *
     * @param int $orgId
     * @param int $courseId
     * @param bool $wrapSn 是否附加序号
     * @return Collection<ContentCourseChapter> 获取到的章节只包含机构选中的 sections 关联
     */
    public static function getResourceTree(int $orgId, int $courseId, bool $wrapSn=false)
    {
        return self::getCoursesResourceTree($orgId, [$courseId], $wrapSn)->get($courseId, collect());
    }

    /**
     * 获取指定机构课程的章节资源树
     *
     * @param int $orgId
     * @param array $courseIds
     * @param bool $wrapSn 是否附加序号
     * @return Collection<int, Collection<ContentCourseChapter>> 获取到的章节只包含机构选中的 sections 关联
     */
    public static function getCoursesResourceTree(int $orgId, array $courseIds, bool $wrapSn = false)
    {
        $courseResources = self::query()
            ->where('org_id', $orgId)
            ->whereIn('course_id', $courseIds)
            ->with('resource', fn($query) => $query->where('status', 1)) //两个类的型的常量，值相同，暂没有的引用办法
            ->get()
            ->groupBy('course_id');

        $result = collect();

        foreach ($courseResources as $courseId => $resources) {
            $items = $resources->pluck('resource')->sortBy([
                ['sort', 'desc'],
                ['id', 'asc']
            ]);

            $sections = $items->filter(fn($item) => $item instanceof ContentCourseSection);

            $chapters = $items->filter(fn($item) => $item instanceof ContentCourseChapter)
                ->each(fn($item) => $item->setRelation('sections', $sections->where('chapter_id', $item->id)
                    ->sortByDesc('sort')));

            if ($wrapSn) {
                ContentCourseChapter::wrapSn($chapters);
            }

            $result->put($courseId, $chapters);
        }

        return $result;
    }

}
