<?php

namespace App\Models\Inspect;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Task extends Model
{
    // 巡检任务表
    protected $table = 'inspect_tasks';

    protected $fillable = [
        'user_id',
        'name',
        'device_count',
        'frequency',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function devices(): BelongsToMany
    {
        return $this->belongsToMany(Device::class, 'inspect_task_devices', 'task_id', 'device_id')->withTimestamps();
    }

    public function taskDevicesRecords(): hasMany
    {
        return $this->hasMany(TaskDevicesRecord::class, 'task_id');
    }
}
