<?php

namespace App\Models\Inspect;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class DeviceItem extends Model
{
    use SoftDeletes;
    // 设备巡检项表
    protected $table = 'inspect_device_items';

    protected $fillable = [
        'device_id',
        'name',
    ];


    /**
     *  设备
     * @return BelongsTo
     */
    public function device(): BelongsTo
    {
        return $this->belongsTo(Device::class, 'device_id')->withTrashed();
    }
}
