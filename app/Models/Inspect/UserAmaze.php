<?php

namespace App\Models\Inspect;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserAmaze extends Model
{
    // 顶赞类型
    const DING_TYPE = 1;
    // 踩赞类型
    const CAI_TYPE = 2;

    // 用户顶踩记录
    protected $table = 'inspect_user_amazes';

    protected $fillable = [
        'user_id',
        'hidden_danger_id',
        'type',
    ];

    // 用户
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // 隐患
    public function hiddenDanger(): BelongsTo
    {
        return $this->belongsTo(HiddenDanger::class, 'hidden_danger_id');
    }
}

