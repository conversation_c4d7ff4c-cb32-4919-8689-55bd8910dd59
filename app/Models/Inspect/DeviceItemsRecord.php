<?php

namespace App\Models\Inspect;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeviceItemsRecord extends Model
{

    // 异常状态
    const ABNORMAL_STATUS = 0;
    // 正常状态
    const NORMAL_STATUS = 1;

    // 巡检项记录表
    protected $table = 'inspect_device_items_records';

    public $timestamps = false;

    protected $fillable = [
        'task_devices_record_id',
        'item_id',
        'status',
    ];

    public function deviceItem(): BelongsTo
    {
        return $this->belongsTo(DeviceItem::class, 'item_id')->withTrashed();
    }
}
