<?php

namespace App\Models\Inspect;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Clues extends Model
{
    // 客户线索表
    protected $table = 'inspect_clues';

    protected $fillable = [
        'target_id',
        'target_type',
        'name',
        'phone',
        'company',
    ];

    /**
     * 通用多态关系：指向来源记录
     */
    public function target(): MorphTo
    {
        return $this->morphTo();
    }

}
