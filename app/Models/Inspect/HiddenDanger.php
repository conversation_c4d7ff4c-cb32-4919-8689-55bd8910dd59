<?php

namespace App\Models\Inspect;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class HiddenDanger extends Model
{
    use SoftDeletes;

    // 是否公开, 0:否, 1:是
    const IS_PUBLIC_NO = 0;
    const IS_PUBLIC_YES = 1;

    // 是否需要审批, 0:不需要, 1:需要
    const APPROVE_NO = 0;
    const APPROVE_YES = 1;

    // 审批状态:0=不存在隐患,1=存在隐患
    const APPROVE_WAIT = 0;
    const APPROVE_DONE = 1;

    // 请求来源:mine, hall，用于接口传参
    const REQUEST_ORIGIN = ['mine', 'hall'];
    // 来自 我的
    const REQUEST_ORIGIN_MINE = 'mine';
    // 来自 大厅
    const REQUEST_ORIGIN_HALL = 'hall';

    // 隐患记录表
    protected $table = 'inspect_hidden_dangers';

    protected $fillable = [
        'question',
        'suggestion',
        'images',
        'is_public',
        'is_approve',
        'amazed',
        'no_amazed',
        'user_id',
        'approver_id',
        'approver_status',
        'approver_at',
    ];

    protected $casts = [
        'images' => 'array',
        'approver_at' => 'datetime',
    ];

    // 发布者
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // 顶踩记录
    public function amazes()
    {
        return $this->hasMany(UserAmaze::class, 'hidden_danger_id');
    }

    /**
     * 线索（多态：Clues）
     *
     * 后期扩展其他模型，在其模型下定义：
     * public function clues()
     * {
     * return $this->morphMany(Demo::class, 'target');
     * }
     *
     * @return MorphMany
     */
    public function clues(): MorphMany
    {
        return $this->morphMany(Clues::class, 'target');
    }
}
