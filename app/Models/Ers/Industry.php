<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Industry
 *
 * @property int $id
 * @property string $name 名称
 * @property int $sort
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 * @property-read Collection<EnterpriseCategory> $enterpriseCategories 拥有的企业类别
 *
 * @mixin \Eloquent
 */
class Industry extends Model
{
	protected $table = 'ers_industries';

    protected $casts = [
        'sort' => 'integer',
    ];

	protected $fillable = [
		'name',
        'sort'
	];

    public function scopePublicFields($query)
    {
        $query->select(['id', 'name']);
    }

    public function enterpriseCategories()
    {
        return $this->hasMany(EnterpriseCategory::class);
    }

}
