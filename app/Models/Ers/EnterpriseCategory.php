<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Illuminate\Database\Eloquent\Model;

/**
 * Class EnterpriseCategory
 *
 * @property int $id
 * @property int $industry_id 所属行业 ID
 * @property string $name 名称
 * @property int $sort
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 * @property-read Industry $industry 所属行业
 *
 * @mixin \Eloquent
 */
class EnterpriseCategory extends Model
{
	protected $table = 'ers_enterprise_categories';

    protected $casts = [
        'industry_id' => 'integer',
        'sort' => 'integer',
    ];

	protected $fillable = [
        'industry_id',
		'name',
        'sort'
	];

    public function scopePublicFields($query)
    {
        $query->select(['id', 'name']);
    }

    public function industry()
    {
        return $this->belongsTo(Industry::class);
    }

}
