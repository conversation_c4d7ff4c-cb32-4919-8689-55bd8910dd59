<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class FormProjectInput
 *
 * @package App\Models
 * @property int $id
 * @property int $project_id 项目ID
 * @property int $flow_id 流程ID
 * @property int $step_id 步骤ID
 * @property int $project_form_id 项目流程模块表单ID
 * @property int $form_library_id 引用基础组件库ID 0 代表非引用，非0代表引用，如果是引用，则项目内是只读状态，不能单独修改
 * @property string $title 标题
 * @property string $type 类型（不可更改）
 * @property int $sort 排序
 * @property bool $is_required 是否必填
 * @property string $desc 描述
 * @property array $options 限制或选项
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @mixin \Eloquent
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 */
class FormProjectInput extends Model
{
	use SoftDeletes;

	protected $table = 'ers_form_project_inputs';

	protected $casts = [
		'project_id' => 'integer',
		'flow_id' => 'integer',
		'step_id' => 'integer',
		'project_form_id' => 'integer',
		'form_library_id' => 'integer',
		'sort' => 'integer',
		'is_required' => 'boolean',
		'options' => 'array'
	];

	protected $fillable = [
		'project_id',
		'flow_id',
		'step_id',
		'project_form_id',
		'project_group_id',
		'form_library_id',
		'title',
		'type',
		'sort',
		'is_required',
		'desc',
		'options'
	];

    const TYPE_TEXT = 'text';
    const TYPE_TEXTAREA = 'textarea';
    const TYPE_IMAGE = 'image';
    const TYPE_FILE = 'file';
    const TYPE_GROUP = 'group';
    const TYPE_SELECT = 'select';
    const TYPE_CHECKBOX = 'checkbox';

    public function scopePublicFields($query)
    {
        return $query->select(['id', 'type', 'project_form_id', 'title', 'is_required', 'options']);
    }

}
