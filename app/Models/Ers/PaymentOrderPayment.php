<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use App\Core\OrderableInterface;
use App\Models\Order\Order;
use App\Services\Ers\ModuleService;
use App\Services\Ers\ServiceOrderStepService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PaymentOrderPayment
 *
 * @package App\Models
 * @property int $id
 * @property int $order_id 工单ID
 * @property int $order_step_id 工单步骤ID
 * @property string $type 支付类型，normal=普通直接支付，advance=预付尾款模式的预付款，final=预付尾款模式的尾款
 * @property float $total_amount 总金额
 * @property float $pay_amount 需付款金额
 * @property int $pay_order_id 支付订单 ID
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 * @property-read ServiceOrder $order 工单
 * @property-read ServiceOrderStep $orderStep 所属工单步骤
 * @property-read Order $payOrder 实际支付的订单
 */
class PaymentOrderPayment extends Model implements OrderableInterface
{
	protected $table = 'ers_payment_order_payments';

	protected $casts = [
		'order_id' => 'integer',
        'order_step_id' => 'integer',
        'pay_order_id' => 'integer'
	];

	protected $fillable = [
		'order_id',
		'order_step_id',
        'type',
		'total_amount',
		'pay_amount',
        'pay_order_id'
	];

    protected $hidden = ['id', 'order_id'];

    public function order()
    {
        return $this->belongsTo(ServiceOrder::class, 'order_id');
    }

    public function orderStep()
    {
        return $this->belongsTo(ServiceOrderStep::class, 'order_step_id');
    }

    public function payOrder()
    {
        return $this->hasOne(Order::class, 'id', 'pay_order_id');
    }

    public function orderDelivery(Order $order): bool
    {
        $this->pay_order_id = $order->id;
        $this->save();

        $this->orderStep->status = ServiceOrderStep::STATUS_FINISH;
        $this->orderStep->finished_by = ServiceOrderStep::FINISHED_BY_USER;
        $this->orderStep->last_user_handled_at = now();
        $this->orderStep->save();

        //推进至下一个流程步骤
        ServiceOrderStepService::forward($this->orderStep->order, $this->orderStep);

        return true;
    }

    public function orderName(): string
    {
        $module = $this->orderStep->module;
        $stepName = $this->orderStep->step->name ?: ModuleService::getModule($module)::configure()->defaultNameFlow;
        return ($this->order->project->title ?? '需求方案').'-'.$stepName;
    }

}
