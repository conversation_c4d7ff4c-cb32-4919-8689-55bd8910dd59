<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FormOrderForm
 *
 * @property int $id
 * @property int $order_id 工单ID
 * @property int $project_form_id 项目表单ID
 * @property int $order_step_id 工单步骤ID
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 * @property-read Collection<FormOrderData> $data 表单输入值
 */
class FormOrderForm extends Model
{
	protected $table = 'ers_form_order_forms';

	protected $casts = [
		'order_id' => 'integer',
		'project_form_id' => 'integer',
        'order_step_id' => 'integer'
	];

	protected $fillable = [
		'order_id',
		'project_form_id',
		'order_step_id'
	];

    protected $hidden = ['id', 'order_id', 'order_step_id'];

    protected $with = ['data'];

    public function data()
    {
        return $this->hasMany(FormOrderData::class, 'order_form_id');
    }

}
