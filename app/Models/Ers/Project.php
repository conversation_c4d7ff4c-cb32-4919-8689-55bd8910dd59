<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * Class Project
 *
 * @property int $id
 * @property string $title 标题
 * @property int $sort 排序
 * @property string $icon 图标
 * @property string $intro 介绍
 * @property int $status 状态 0 停用，1 启用
 * @property int $is_bind_category 是否绑定类别
 * @property int $flow_id 流程ID
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property Flow $flow
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 * @mixin \Eloquent
 */
class Project extends Model
{
	protected $table = 'ers_projects';

	protected $casts = [
		'sort' => 'integer',
		'status' => 'integer',
		'is_bind_category' => 'boolean',
		'flow_id' => 'integer'
	];

	protected $fillable = [
		'title',
		'sort',
		'icon',
		'intro',
		'status',
		'is_bind_category',
		'flow_id'
	];

    protected $appends = ['icon_src'];

    /** @var int 停用 */
    const STATUS_STOP = 0;
    /** @var int 启用 */
    const STATUS_NORMAL = 1;

    protected function getIconSrcAttribute(): string
    {
        return $this->icon ? AttachmentService::url(Storage::disk(), $this->icon) : '';
    }

    public function scopePublicFields($query)
    {
        $query->select(['id', 'title', 'icon', 'intro', 'is_bind_category', 'flow_id']);
    }

    public function flow()
    {
        return $this->hasOne(Flow::class, 'id', 'flow_id');
    }
}
