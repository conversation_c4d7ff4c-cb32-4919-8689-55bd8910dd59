<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class ServiceOrderRecord
 *
 * @package App\Models
 * @property int $id
 * @property int $order_id 工单ID
 * @property int $user_id 用户ID
 * @property int $admin_id 管理员ID
 * @property int $step_id 到达步骤ID
 * @property string $desc 描述
 * @property int $status 状态 0 代办，1 已办
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @mixin \Eloquent
 */
class ServiceOrderRecord extends Model
{
	use SoftDeletes;
	protected $table = 'ers_service_order_records';

	protected $casts = [
		'order_id' => 'integer',
		'user_id' => 'integer',
		'admin_id' => 'integer',
		'step_id' => 'integer',
		'status' => 'integer'
	];

	protected $fillable = [
		'order_id',
		'user_id',
		'admin_id',
		'step_id',
		'desc',
		'status'
	];
}
