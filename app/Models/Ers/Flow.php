<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Flow
 *
 * @package App\Models
 * @property int $id
 * @property string $name 名称
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 */
class Flow extends Model
{
	protected $table = 'ers_flows';

	protected $fillable = [
		'name'
	];

    public function steps()
    {
        return $this->hasMany(FlowStep::class, 'flow_id', 'id');
    }
}
