<?php

namespace App\Models\Cms;

use Illuminate\Database\Eloquent\Model;

/**
 * 学习进度缓冲模型
 * 
 * @property int $id 主键ID
 * @property int $org_id 机构ID
 * @property int $user_id 用户ID
 * @property int $content_id 内容ID
 * @property int $enroll_id 报名表ID
 * @property int $section_id 章节ID
 * @property int $duration_delta 时长增量(秒)
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 */
class ContentCourseProgressBuffer extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'cms_content_course_progress_buffer';

    /**
     * 可批量赋值的字段
     *
     * @var array
     */
    protected $fillable = [
        'org_id',
        'user_id',
        'content_id', 
        'enroll_id',
        'section_id',
        'duration_delta'
    ];

    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'org_id' => 'integer',
        'user_id' => 'integer',
        'content_id' => 'integer',
        'enroll_id' => 'integer',
        'section_id' => 'integer',
        'duration_delta' => 'integer'
    ];
}