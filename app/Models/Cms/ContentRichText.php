<?php

namespace App\Models\Cms;

use App\Models\Cms\Contract\ContentResourceInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $content_id 内容ID
 * @property string $content 内容
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class ContentRichText extends Model implements ContentResourceInterface
{
    /**
     * The table associated with the model.
     */
    protected $table = 'cms_content_rich_texts';

    protected $primaryKey = 'content_id';

    protected $hidden = ['content_id'];

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['content_id', 'content', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['content_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    public function scopeDetail($query)
    {
        return $query->select(['content_id', 'content']);
    }

    public function scopePublicFields($query)
    {
        return $query->select(['content_id', 'content']);
    }

    public function detailAppend($data = []) {
        // TODO: Implement detailAppend() method.
    }

    public function scopeOwn($query, $userId) {
        // TODO: Implement scopeOwn() method.
    }

    public function ownAppend() {
        // TODO: Implement ownAppend() method.
    }
}
