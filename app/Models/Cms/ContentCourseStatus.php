<?php

namespace App\Models\Cms;

use Illuminate\Database\Eloquent\Model;

class ContentCourseStatus extends Model
{
    protected $table = 'cms_content_course_status';
    
    protected $fillable = [
        'org_id',
        'user_id',
        'content_id',
        'enroll_id',
        'duration',
        'valid_duration',
        'hour',
        'percent',
        'finished',
    ];
    
    protected $casts = [
        'finished' => 'boolean',
    ];
    
    /** @var int 未完成 */
    public const FINISHED_NOT = 0;

    /** @var int 已完成 */
    public const FINISHED = 1;
}