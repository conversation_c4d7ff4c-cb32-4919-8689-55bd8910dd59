<?php

namespace App\Models\Cms;

use App\Jobs\CourseUpdateJob;
use App\Models\Cms\Contract\BelongsToSetInterface;
use App\Models\Cms\Contract\ContentResourceInterface;
use App\Models\Org\CoursePack;
use App\Models\Org\Enrollment;
use App\Models\Train\Topic;
use App\Models\User\UserOwnContent;
use App\Services\Cms\ContentCourseProgressService;
use App\Services\Cms\ContentDocService;
use App\Services\Org\CourseService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

/**
 * @property int $content_id 内容ID
 * @property string $teacher_name 讲师名称
 * @property int $learning_count 在学人数
 * @property int $learning_count_add
 * @property int $sections_count 课程、学时数量
 * @property int $try_view_count 试看课程节数
 * @property int $topic_id 题库ID（练习、考试）
 * @property int $hour_per_minutes 一课时分钟数
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read Collection|ContentCourseChapter[] $chapters
 * @property-read Collection|ContentCourseDoc[] $docs
 * @property-read Topic $topic
 * @property-read Content $content
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 * @method \Illuminate\Database\Eloquent\Builder|self org($orgId, $contentId, $scope = 'publicFields')
 */
class ContentCourse extends Model implements BelongsToSetInterface, ContentResourceInterface
{
    /**
     * The table associated with the model.
     */
    protected $table = 'cms_content_courses';

    protected $primaryKey = 'content_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['content_id', 'teacher_name', 'learning_count', 'learning_count_add', 'hour', 'duration', 'sections_count', 'try_view_count', 'topic_id', 'hour_per_minutes', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['content_id' => 'integer', 'learning_count' => 'integer', 'learning_count_add'=>'integer', 'sections_count' => 'integer', 'try_view_count' => 'integer', 'topic_id' => 'integer', 'hour_per_minutes' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['content_id', 'learning_count_add', 'topic_id'];

    protected static function booted()
    {
        static::deleting(function (self $course) {
            //清除关联章
            $course->chapters->each(fn($chapter) => $chapter->delete());

            //清理关联节
            $course->sections->each(fn($section) => $section->delete()); //chapter 实际上也会删除，但有这道能避免一些可能的问题

            //清除关联学习记录
            ContentCourseProgress::query()->where('content_id', $course->content_id)->delete();
        });

        static::updated(function (self $course) {
            // 如果课程题库发生变化，则分配到拥有课程的机构题库
            if ($course->isDirty('topic_id')) {
                CourseUpdateJob::dispatch($course->content_id, 0, CourseUpdateJob::TYPE_TOPIC);
            }
        });

    }

    public function content()
    {
        return $this->belongsTo(Content::class, 'content_id', 'id')->withTrashed();
    }

    public function chapters()
    {
        return $this->hasMany(ContentCourseChapter::class, 'content_id', 'content_id');
    }

    public function sections()
    {
        return $this->hasMany(ContentCourseSection::class, 'content_id', 'content_id');
    }

    public function progress()
    {
        return $this->hasMany(ContentCourseProgress::class, 'content_id', 'content_id');
    }

    public function docs()
    {
        return $this->hasMany(ContentCourseDoc::class, 'content_id', 'content_id');
    }

    public function topic()
    {
        return $this->belongsTo(Topic::class, 'topic_id', 'id');
    }

    public function scopeDetail($query)
    {
        return $query->select(['content_id', 'hour', 'duration', 'teacher_name', 'try_view_count', 'learning_count', 'learning_count_add', 'sections_count'])
            ->with([
                'chapters' => fn ($query) => $query->content(),
                'docs'
            ]);
    }

    public function scopeOrg($query, $orgId, $contentId, $scope = 'publicFields')
    {
        list($chapterIds, $sectionIds) = CourseService::orgCourseChapSecIds($orgId, $contentId);

        return $query->select(['content_id', 'hour', 'duration', 'topic_id', 'teacher_name', 'learning_count', 'learning_count_add', 'sections_count'])
            ->with([
                'chapters' => function ($query) use ($chapterIds, $sectionIds, $scope) {
                    $query->whereIn('id', $chapterIds)
                        ->content()
                        ->with(['sections' => function ($query) use ($sectionIds, $scope) {
                            $query->$scope()->whereIn('id', $sectionIds)
                                ->orderbyDesc('sort')
                                ->orderBy('id');
                        }])
                        ->orderbyDesc('sort')
                        ->orderBy('id');
                },
                'topic' => fn ($query) => $query->publicFields(),
                'docs'
            ]);
    }

    public function scopePublicFields($query)
    {
        return $query->select(['content_id', 'hour', 'duration', 'teacher_name', 'try_view_count', 'learning_count', 'learning_count_add', 'sections_count']);
    }

    public static function belongsToSet(Content $content)
    {
        return ContentCoursePackList::query()
            ->where('course_id', $content->id)
            ->select('content_id')
            ->pluck('content_id')
            ->toArray();
    }

    /**
     * 显示叠加的计数
     */
    public function learningCount(): Attribute
    {
        $add = $this->learning_count_add ?? 0;
        //这里的 set 是为了不对实际修改 views 时造成影响
        return Attribute::make(
            get: fn($value, array $attrs) => $value + $add,
            set: fn($value, array $attrs) => $value - $add
        );
    }

    public function detailAppend($data = []): void
    {
        $userId = Auth::id();
        $this->sid = Content::encodeId($this->content_id);

        /** @var Enrollment|null $enrollment */
        $enrollment = $data['enrollment'] ?? null;

        $orgId = $enrollment ? $enrollment->org_id : 0;
        $enrollId = $enrollment ? $enrollment->id : 0;

        $this->current_section = $userId ? ContentCourseProgressService::currentSection($userId, $this->content_id, $enrollId) : null;
        $this->course_progress = ContentCourseProgressService::progress($userId, $this->content_id, $orgId, $enrollId);
        $this->course_expired = UserOwnContent::query()
            ->where('user_id', $userId)
            ->where('content_id', $this->content_id)
            ->where('enroll_id', $enrollId)
            ->value('expired_at');

        $content = $data['content'] ?? null;
        if (!$content) {
            return;
        }

        foreach ($this->docs as &$doc) {
            $doc->file_type = ContentDocService::getFileType($doc->filepath);
            if (!$content->download) {
                $doc->filepath = '';
                $doc->filepath_src = '';
            }
        }

        $this->docs?->makeHidden(['id', 'content_id']);

        // 课程章节进度
        $progresses = $userId ? ContentCourseProgressService::getCourseProgress($userId, $this->content_id, $orgId, $enrollId) : null;
        $tryViewCount = 0;
        $contentCourse = $this;
        if (!$this->hour_per_minutes) {
           $contentCourse =  ContentCourse::query()->where('content_id', $this->content_id)->first();
        }
        $totalCourseDuration = 0;
        foreach ($this->chapters as &$chapter) {
            $totalDuration = 0;
            foreach ($chapter->sections as $section) {
                $totalDuration += $section->duration;
                if (!$content->download) {// 未付费
                    if ($tryViewCount < $this->try_view_count && !$enrollment) {// 在试看数量内
                        $section->see = 2;
                        $tryViewCount++;
                    } else {
                        $section->see = 0;
                    }
                } else {
                    $section->see = 1;
                }
                if ($progresses && $progresses->isNotEmpty()) {
                    /** @var ContentCourseProgress|null $progress */
                    $progress = $progresses->get($section->id);
                    $sectionDuration = $section->video ? $section->video->duration : $section->duration;
                    $progressStatus = match ($progress) {
                        null => '学习进度 0%',
                        default => match ($progress->finished) {
                            true => '学习进度 100%',
                            false => match (true) {
                                $section->duration > 0 => '学习进度 ' . min(bcdiv($progress->duration,$sectionDuration, 4), 0.9999) * 100 . '%',
                                default => '学习进度 0%'
                            }
                        }
                    };
                    $section->progress = $progressStatus;
                } else {
                    $section->progress = '';
                }
            }
            $totalCourseDuration += $totalDuration;
            $chapter->hour = $contentCourse->studyHour($totalDuration);
        }
        
        unset($chapter);
        // 机构课时取表中值, 平台课时取计算值
        $orgId == 0 && $this->hour = $contentCourse->studyHour($totalCourseDuration, false);

        //附加自动序号
        ContentCourseChapter::wrapSn($this->chapters);
    }

    public function scopeOwn($query, $userId)
    {
        return $query->withCount([
            'progress' => function ($query) use ($userId) {
                $query->where('user_id', $userId)->where('finished', ContentCourseProgress::FINISHED);
            }
        ]);
    }

    public function ownAppend()
    {
        $userId = Auth::id();
        $this->current_section = $userId ? ContentCourseProgressService::currentSection($userId, $this->content_id) : null;
        $this->sid = Content::encodeId($this->content_id);
    }

    public function orgAppend(int $orgId, int $enrollId)
    {
        $userId = Auth::id();
        $course = CourseService::getCourse($orgId, $this->content_id);
        $this->hour = $course?->hour ?? $this->hour;

        if ($userId && $enrollId) {
            $this->study_progress = ContentCourseProgressService::orgCourseProgress($userId, $orgId, $enrollId, $this->content_id, $this->hour);
        } else {
            $this->study_progress = null;
        }
    }

    /**
     * 课时统一计算
     * @param $duration
     * @param $isDecimal
     * @return mixed
     */
    public function studyHour($duration, $isDecimal = true)
    {
        $num = $isDecimal ? 1 : 0;
        $studySeconds =  $this->hour_per_minutes * 60;
        $hour = bcdiv($duration, $studySeconds, $num);

        return max($hour, 0);
    }

    /**
     * 计算课时与百分比进度
     * @param $duration
     * @param $isDecimal
     * @return mixed
     */
    public function studyHourForPercent($duration, $courseHour, $isDecimal = true)
    {
        $studyHour = $this->studyHour($duration, $isDecimal);
        $studyHour >= $courseHour && $studyHour = $courseHour;

        $studyPercent = $courseHour > 0 ? number_format($studyHour / $courseHour * 100, 1, '.', '') : 0;
        return [$studyHour, $studyPercent];
    }

    public function charageAmountAppend(int $orgId)
    {
        /** @var CoursePack $coursePack */
        $course = CourseService::getCourse($orgId, $this->content_id);
        $this->charage_amount = $course?->price_sell ? number_format($course->price_sell, 2, '.', '') : 0;
    }
}