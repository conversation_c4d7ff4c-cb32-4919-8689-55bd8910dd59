<?php

namespace App\Models\Cms;

use App\Jobs\CourseUpdateJob;
use App\Models\Cms\Contract\ContentResourceInterface;
use App\Models\Org\Course;
use App\Models\Org\CoursePack;
use App\Models\Train\Topic;
use App\Models\User\UserOwnContent;
use App\Services\Cms\ContentCourseProgressService;
use App\Services\Cms\ContentCourseService;
use App\Services\Org\CoursePackService;
use App\Services\Org\CourseService;
use Eloquent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

/**
 * 课程包
 *
 * @property int $content_id 内容ID
 * @property int $topic_id 题库ID
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Collection<Content> $courses
 * @property-read Collection<ContentCourse> $contentCourses
 * @property-read Content $content
 * @property-read Topic $topic
 * @property-read Collection<ContentCoursePackList> $coursePackLists
 * @property-read int[] $courseIds
 * @mixin Eloquent
 */
class ContentCoursePack extends Model implements ContentResourceInterface
{
    use HasFactory;

    protected $table = 'cms_content_course_packs';

    protected $primaryKey = 'content_id';

    protected $hidden = ['content_id'];

    protected $fillable = ['content_id', 'topic_id', 'hour', 'duration',];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'content_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected static function booted()
    {
        static::deleting(function (self $model) {
            ContentCoursePackList::query()->where('content_id', $model->content_id)->delete();
        });

        static::updated(function (self $course) {
            // 如果课程题库发生变化，则分配到拥有课程的机构题库
            if ($course->isDirty('topic_id')) {
                CourseUpdateJob::dispatch($course->content_id, 0, CourseUpdateJob::TYPE_COURSE_PACK);
            }
        });
    }

    public function scopePublicFields($query)
    {
        return $query;
    }

    public function scopeDetail($query)
    {
        return $query->with([
            'courses' => fn($query) => $query->whereIn('cms_contents.status', [Content::STATUS_NORMAL, Content::STATUS_HIDE]),
            'courses.resource' => fn($query) => $query->publicFields()
        ]);
    }

    public function courses()
    {
        $prefix = 'cms_contents';
        $fields = ['id', 'title', 'intro', 'type', 'cover', 'views', 'views_add', 'release_at', 'view_limit'];

        return $this->hasManyThrough(Content::class, ContentCoursePackList::class, 'content_id', 'id', 'content_id', 'course_id')
            ->select(array_map(fn($item) => $prefix . '.' . $item, $fields))->orderBy('cms_content_course_pack_lists.order');
    }

    public function contentCourses()
    {
        return $this->hasManyThrough(ContentCourse::class, ContentCoursePackList::class, 'content_id', 'content_id', 'content_id', 'course_id')
            ->orderBy('cms_content_course_pack_lists.order');
    }

    public function coursePackLists()
    {
        return $this->hasMany(ContentCoursePackList::class, 'content_id', 'content_id')->orderBy('cms_content_course_pack_lists.order');
    }

    public function getCourseIdsAttribute()
    {
        return $this->coursePackLists->pluck('course_id')->toArray();
    }

    public function detailAppend($data = [])
    {
        $userId = auth()->id();

        $enrollment = $data['enrollment'] ?? null;

        $orgId = $enrollment ? $enrollment->org_id : 0;
        $enrollId = $enrollment ? $enrollment->id : 0;

        $this->progress_percent = 0;

        $progresses = [];
        $courseIds = $this->courses->pluck('id')->toArray();

        if ($userId) {
            if ($courseIds) {
                $progresses = ContentCourseProgressService::multipleCourseProgresses(auth()->id(), $courseIds, $orgId, $enrollId);
                $this->progress_percent = number_format(array_sum($progresses) / count($progresses), 2, '.', '');
            }

            $this->expired_at = UserOwnContent::query()
                ->where('user_id', $userId)
                ->where('content_id', $this->content_id)
                ->value('expired_at');
        }

        $inCourseSubIds = [];

        if ($orgId > 0) {
            $inCourseSubIds = Course::query()
                ->where('org_id', $orgId)
                ->whereIn('course_id', $courseIds)
                ->pluck('course_id')
                ->toArray();
        }

        $this->courses->each(function ($course) use ($progresses, $orgId, $enrollId, $inCourseSubIds) {
            if ($orgId > 0 && $enrollId > 0) {
                // $course->resource->orgAppend($orgId, $enrollId);
                // $course->resource->course_name = $course->title;

                if (in_array($course->id, $inCourseSubIds)) {
                    list($chapterIds, $sectionIds) = CourseService::orgCourseChapSecIds($orgId, $course->id);
                    $course->resource->chapters_count = count($chapterIds);
                    $course->resource->sections_count = count($sectionIds);
                } else {
                    $course->resource->chapters_count = ContentCourseService::getChapterCount($course->id);
                    $course->resource->sections_count = ContentCourseService::getSectionCount($course->id);
                }
            }

            $course->resource->progress_percent = isset($progresses[$course->id]) ? number_format($progresses[$course->id], 2, '.', '') : '0';
        });
    }

    public function scopeOwn($query, $userId)
    {
        $pgTable = (new ContentCourseProgress())->getTable();

        return $this->scopePublicFields($query)
            ->withCount([
                'progress' => fn($query) => $query->where("$pgTable.user_id", $userId)->where("$pgTable.finished", 1),
                'sections'
            ]);
    }

    /**
     * 节关联，用于 scopeOwn
     */
    public function sections()
    {
        return $this->hasManyThrough(ContentCourseSection::class, ContentCoursePackList::class, 'content_id', 'content_id', 'content_id', 'course_id')
            ->where('status', ContentCourseSection::STATUS_SHOW);
    }

    public function content()
    {
        return $this->belongsTo(Content::class, 'content_id', 'id')->withTrashed();
    }

    public function topic()
    {
        return $this->belongsTo(Topic::class, 'topic_id', 'id');
    }

    /**
     * 进度关联，用于 scopeOwn
     */
    public function progress()
    {
        return $this->hasManyThrough(ContentCourseProgress::class, ContentCoursePackList::class, 'content_id', 'content_id', 'content_id', 'course_id');
    }

    public function ownAppend()
    {
        $this->progress_percent = number_format($this->progress_count / $this->sections_count * 100, 2, '.', '');
        $this->makeHidden('progress_count');
    }

    public function orgAppend(int $orgId, int $enrollId)
    {
        $userId = Auth::id();
        $this->hour = CoursePackService::calCoursePackHour($orgId, $this->content_id);

        if ($userId && $enrollId) {
            $this->study_progress = ContentCourseProgressService::orgCoursePackProgress($userId, $orgId, $enrollId, $this->content_id, $this->hour);
        }else {
            $this->study_progress = null;
        }
}

    public function charageAmountAppend(int $orgId)
    {
        /** @var CoursePack $coursePack */
        $coursePack = CoursePack::query()->where('org_id', $orgId)->where('course_pack_id', $this->content_id)->first();
        $this->charage_amount = $coursePack?->price_sell ? number_format($coursePack->price_sell, 2, '.', '') : 0;
    }
}
