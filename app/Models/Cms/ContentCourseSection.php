<?php

namespace App\Models\Cms;

use App\Core\Enums\BusinessType;
use App\Core\HashIdAttribute;
use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Jobs\CourseUpdateJob;
use App\Models\Org\Course;
use App\Services\Org\CourseService;
use Illuminate\Support\Facades\Log;

/**
 * @property int $id
 * @property int $content_id 内容ID
 * @property int $chapter_id 章ID
 * @property int $ref_video_id 引用的视频ID
 * @property string $name 名称
 * @property int $status 状态  0 隐藏，1 显示 2 处理中
 * @property string $filepath 视频文件
 * @property int $filesize 文件大小
 * @property array|null $extend 扩展信息
 * @property int $play_count 播放次数
 * @property int $play_complete_count 播放完成次数
 * @property int $duration 视频时长（单位：秒）
 * @property int $sort 排序
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read ContentCourseChapter $chapter
 * @property-read Content $content
 * @property-read ContentVideo $video
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 * @method \Illuminate\Database\Eloquent\Builder|self detailFields()
 */
class ContentCourseSection extends Model
{

    use HashIdAttribute;

    /**
     * The table associated with the model.
     */
    protected $table = 'cms_content_course_sections';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'content_id', 'chapter_id', 'ref_video_id', 'name', 'status', 'filepath', 'filesize', 'extend', 'play_count', 'play_complete_count', 'duration', 'sort', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'content_id' => 'integer', 'chapter_id' => 'integer', 'ref_video_id' => 'integer', 'status' => 'integer', 'filesize' => 'integer', 'extend' => 'array', 'play_count' => 'integer', 'play_complete_count' => 'integer', 'duration' => 'integer', 'sort' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'content_id', 'chapter_id', 'ref_video_id', 'sort', 'extend'];

    protected $appends = ['sid', 'video_src', 'filepath_src', 'video_cover', 'actual_duration'];

    /** @var int 显示 */
    const STATUS_SHOW = 1;

    /** @var int 隐藏 */
    const STATUS_HIDE = 0;

    /** @var int 待处理 */
    const STATUS_PADDING = 2;
    const STATUS_PROCESSING = 3;

    protected static function booted()
    {
        static::created(function (self $section) {
            Log::info('新增章节');
            CourseService::updateCourseLessons('add', $section, null);
        });

        static::deleting(function (self $section) {
            //清理关联附件
            $paths = [];
            $section->filepath && $paths[] = $section->filepath;
            if ($section->extend) {
                $section->extend['hd'] && $paths[] = $section->extend['hd'];
                $section->extend['screenshot'] && $paths[] = $section->extend['screenshot'];
            }
            AttachmentService::removeRelations(BusinessType::Content, $section->content_id, $paths);

            //清除关联学习记录
            ContentCourseProgress::query()->where('section_id', $section->id)->delete();

            Log::info('删除章节');
            if ($section->status == self::STATUS_SHOW) {
                updateContentCourse($section, -$section->duration);
            }

            CourseService::updateCourseLessons('del', $section, null);
        });

        static::updated(function (self $section) {
            if ($section->isDirty('duration')) {
                Log::info('更新章节时长');

                if ($section->status == self::STATUS_SHOW) {
                    $modfiyDuration = $section->duration - $section->getOriginal('duration');
                    updateContentCourse($section, $modfiyDuration);
                }

                CourseService::updateCourseLessons('update', $section, null);
            }

            if ($section->isDirty('status')) {
                Log::info('更新章节状态');
                $modfiyDuration = $section->status == self::STATUS_SHOW ? $section->duration : -$section->duration;
                updateContentCourse($section, $modfiyDuration);
                CourseService::updateCourseLessons('update', $section, null);
            }
        });

        /**
         * 更新有效学时
         */
        function updateContentCourse($section, $modfiyDuration) {
            $contentCourse = $section->contentCourse;

            $oldDuration = $contentCourse->getOriginal('duration');
            $oldHour = $contentCourse->getOriginal('hour');

            $contentCourse->duration = $oldDuration + $modfiyDuration;
            $contentCourse->hour = $contentCourse->studyHour($contentCourse->duration, false);
            $contentCourse->save();

            Log::info('平台课程有效学时改变', [
                'course_id' => $contentCourse->id,
                'section_id' => $section->id,
                'old_hour' => $oldHour,
                'hour' => $contentCourse->hour,
                'old_duration' => $oldDuration,
                'duration' => $contentCourse->duration,
                'modfiy_duration' => $modfiyDuration,
            ]);
        }
    }

    public function scopePublicFields($query, $download = false)
    {
        $fields = ['id', 'content_id', 'chapter_id', 'name', 'duration', 'sort'];
        if ($download) {
            array_push($fields, 'filepath', 'extend');
        }

        $query->select($fields);
    }

    public function scopeDetailFields($query)
    {
        return $query->select(['id', 'content_id', 'chapter_id', 'ref_video_id', 'name', 'duration', 'sort', 'filepath', 'extend']);
    }

    public function getVideoSrcAttribute()
    {
        if ($this->duration > 0 && is_array($this->extend) && array_key_exists('hd', $this->extend)) {
            if (isset($this->extend['hd'])) {
                return AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->extend['hd']);
            } else {
                return $this->getFilepathSrcAttribute();
            }
        }

        return "";
    }

    public function getVideoCoverAttribute()
    {
        if (is_array($this->extend) && array_key_exists('screenshot', $this->extend) && isset($this->extend['screenshot'])) {
            return AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->extend['screenshot']);
        }

        return "";
    }

    /**
     * 小节实际用于计算的时长
     *
     * @return int

     */
    public function getActualDurationAttribute()
    {
        return $this->video?->duration ?? $this->duration;
    }

    protected function getFilepathSrcAttribute(): string
    {
        return $this->filepath ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->filepath) : '';
    }

    public static function addHashIdSalt()
    {
        return 'CMS-Course-Section';
    }

    public function content()
    {
        return $this->belongsTo(Content::class);
    }

    public function chapter()
    {
        return $this->belongsTo(ContentCourseChapter::class);
    }

    public function video()
    {
        return $this->belongsTo(ContentVideo::class, 'ref_video_id', 'content_id');
    }

    public function contentCourse()
    {
        return $this->belongsTo(ContentCourse::class, 'content_id', 'content_id');
    }
}