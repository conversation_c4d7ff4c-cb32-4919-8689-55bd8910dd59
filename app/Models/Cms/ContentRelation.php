<?php

namespace App\Models\Cms;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $content_id 内容ID
 * @property int $related_id 关联内容ID
 * @property \Carbon\Carbon $created_at
 */
class ContentRelation extends Model
{
    const UPDATED_AT = null;

    /**
     * The table associated with the model.
     */
    protected $table = 'cms_content_relations';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'content_id', 'related_id', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'content_id' => 'integer', 'related_id' => 'integer', 'created_at' => 'datetime'];
}
