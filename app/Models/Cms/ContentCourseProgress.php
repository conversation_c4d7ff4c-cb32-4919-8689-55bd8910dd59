<?php

namespace App\Models\Cms;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $content_id 内容ID
 * @property int $chapter_id 章ID
 * @property int $section_id 节ID
 * @property int $org_id 机构ID
 * @property int $enroll_id 报名表ID
 * @property int $duration 观看时长（单位：秒）
 * @property int $pos 目前时间位置（单位：秒）
 * @property bool $finished 是否已看完  0 否，1 是（观看时长大于视频时长的60%，并且视频看到了最后10秒内则认为看完）
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read ContentCourseSection $section
 */
class ContentCourseProgress extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'cms_content_course_progresses';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'content_id', 'progs_status_id', 'chapter_id', 'section_id', 'org_id', 'enroll_id', 'duration', 'valid_duration', 'pos', 'finished', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'content_id' => 'integer', 'chapter_id' => 'integer', 'section_id' => 'integer', 'org_id' => 'integer', 'enroll_id' => 'integer', 'duration' => 'integer', 'pos' => 'integer', 'finished' => 'boolean', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'user_id', 'content_id', 'chapter_id', 'section_id'];

    /** @var int 播放时长 */
    public static int $playDuration = 10;

    /** @var int 播放完成百分比 */
    public static int $completePercent = 80;

    /** @var int 未完成 */
    const FINISHED_NOT = 0;

    /** @var int 已完成 */
    const FINISHED = 1;

    public function getSectionSidAttribute()
    {
        return ContentCourseSection::encodeId($this->section_id);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function section()
    {
        return $this->belongsTo(ContentCourseSection::class);
    }

    /**
     * 学习进度状态文案
     *
     * @return string[]
     */
    public static function progressLabel(): array
    {
        return [
            1 => '未学',
            2 => '学习中',
            3 => '已学'
        ];
    }

    /**
     * 播放是否完成
     *
     * @param int $playDuration 当前播放时长
     * @param int $playPos 当前播放位置
     * @param int $duration 视频时长
     * @return int
     */
    public static function playComplete(int $playDuration, int $playPos, int $duration): int
    {
        // 播放时长百分比
        $percentage = bcdiv($playDuration, $duration, 2) * 100;

        // 剩余播放时间
        $remainTime = $duration - $playPos;

        if ($percentage >= self::$completePercent && $remainTime <= 10) {
            return true;
        }

        return false;
    }

    /**
     * 进度状态文案
     *
     * @param $progress
     * @return string
     */
    public static function progressStatus($progress)
    {
        $sectionDuration = $progress?->section->actual_duration;

        return match ($progress) {
            null => '0%',
            default => match ($progress->finished) {
                true => '已完成',
                false => match (true) {
                    $sectionDuration > 0 => min(bcdiv($progress->duration, $sectionDuration, 4), 0.9999) * 100 . '%',
                    default => '0%'
                }
            }
        };
    }

}
