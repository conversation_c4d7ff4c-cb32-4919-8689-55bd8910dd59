<?php

namespace App\Models\Cms;

use App\Core\HashIdAttribute;
use App\Services\Cms\ContentDocService;
use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * 课程资料
 *
 * @property int $id
 * @property int $content_id 所属内容ID
 * @property string $filename 文件名
 * @property string $filepath 文件路径
 * @property int $sort 排序
 * @property int $download_count 下载统计
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 */
class ContentCourseDoc extends Model
{
    use HasFactory, HashIdAttribute;

    protected $table = 'cms_content_course_docs';

    protected $fillable = ['id', 'content_id', 'filename', 'filepath', 'sort', 'download_count', 'created_at', 'updated_at'];

    protected $casts = ['content_id' => 'integer', 'sort' => 'integer', 'download_count' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $appends = ['sid', 'filepath_src'];

    public function getFilepathSrcAttribute()
    {
        return $this->filepath ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->filepath) : '';
    }
}
