<?php

namespace App\Models\Cms;

use App\Models\Cms\Contract\ContentResourceInterface;
use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $content_id 内容ID
 * @property string $format 格式  Word, Excel, PPT, PDF
 * @property int $page_count 页数
 * @property int $filesize 文件大小
 * @property string $filepath 文件路径
 * @property string $filename 文件名称
 * @property int $download_count 下载次数
 * @property int $download_count_add
 * @property array $preview_images 文档预览图列表
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read ContentDoc[]|Collection $contentDocs
 */
class ContentDoc extends Model implements ContentResourceInterface
{
    /**
     * The table associated with the model.
     */
    protected $table = 'cms_content_docs';

    protected $primaryKey = 'content_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['content_id', 'format', 'page_count', 'filesize', 'filepath', 'filename', 'download_count', 'download_count_add', 'preview_images', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['content_id' => 'integer', 'page_count' => 'integer', 'filesize' => 'integer', 'download_count' => 'integer', 'download_count_add'=>'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['content_id', 'download_count_add'];
    protected $appends = ['preview_images_src', 'filepath_src'];

    protected function getFilepathSrcAttribute(): string
    {
        return $this->filepath ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->filepath, $this->filename) : '';
    }

    protected function getPreviewImagesSrcAttribute(): array
    {
        $list = $this->preview_images;

        foreach ($list as $index => $item) {
            $list[$index] = AttachmentService::url(Storage::disk(), $item);
        }

        return $list;
    }

    public static function getFormat(): array
    {
        return ['word', 'excel', 'ppt', 'pdf'];
    }

    public function previewImages(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                return $value ? json_decode($value, true) : [];
            },
            set: function ($value) {
                return json_encode($value);
            }
        );
    }

    public function scopeDetail($query, $download = false)
    {
        $fields = ['content_id', 'format', 'page_count', 'download_count', 'download_count_add', 'filename', 'filesize', 'preview_images'];
        if ($download) {
            array_push($fields, 'filepath');
        }

        return $query->select($fields);
    }

    public function scopePublicFields($query)
    {
        return $query->select(['content_id', 'format', 'page_count', 'download_count', 'download_count_add', 'filename', 'filepath', 'preview_images']);
    }

    public function content()
    {
        return $this->belongsTo(Content::class, 'content_id', 'id');
    }

    /**
     * 显示叠加的计数
     */
    public function downloadCount(): Attribute
    {
        $add = $this->download_count_add ?? 0;
        //这里的 set 是为了不对实际修改 views 时造成影响
        return Attribute::make(
            get: fn($value, array $attrs) => $value + $add,
            set: fn($value, array $attrs) => $value - $add
        );
    }

    public function detailAppend($data = []) {
        // TODO: Implement detailAppend() method.
    }

    public function scopeOwn($query, $userId) {
        // TODO: Implement scopeOwn() method.
    }

    public function ownAppend() {
        // TODO: Implement ownAppend() method.
    }
}
