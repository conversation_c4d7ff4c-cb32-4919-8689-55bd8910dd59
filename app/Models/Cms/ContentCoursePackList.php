<?php

namespace App\Models\Cms;

use App\Jobs\CoursePackAddCourseJob;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 课程包课程列表
 *
 * @property int $id
 * @property int $content_id
 * @property int $course_id 关联课程的ID
 * @property int $order 排序
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 */
class ContentCoursePackList extends Model
{
    use HasFactory;

    protected $table = 'cms_content_course_pack_lists';

    protected $fillable = ['id', 'content_id', 'course_id', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'content_id' => 'integer', 'course_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected static function booted()
    {
        static::created(function (self $contentCoursePackList) {
            // 课程包添加课程触发更新机构分配课程包的用户添加机构课程记录
            CoursePackAddCourseJob::dispatch($contentCoursePackList->content_id, $contentCoursePackList->course_id);
        });
    }
}
