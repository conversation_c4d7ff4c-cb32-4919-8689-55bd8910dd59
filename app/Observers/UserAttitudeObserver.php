<?php

namespace App\Observers;


use App\Models\User\UserAttitude;

class UserAttitudeObserver
{
    /**
     * 处理收藏「创建之前」事件。
     *
     * @param  \App\Models\User\UserAttitude  $attitude
     * @return void
     */
    public function created(UserAttitude $attitude)
    {
        if (in_array($attitude->business_type, ["topic", "answer"])){
            $attitude->resource->attitudeCreating($attitude->attitude);
        }
    }


    /**
     * 处理收藏「删除之前」事件。
     *
     * @param  \App\Models\User\UserAttitude  $attitude
     * @return void
     */
    public function deleting(UserAttitude $attitude)
    {
        if (in_array($attitude->business_type, ["topic", "answer"])){
            $attitude->resource->attitudeDeleting($attitude->attitude);
        }
    }
}