<?php

namespace App\Observers;

use App\Models\Org\EnrollArchive;
use App\Models\Org\Enrollment;

class OrgEnrollmentObserver
{
    /**
     * Handle the ServiceOrder "updated" event.
     */
    public function updated(Enrollment $enrollment): void
    {
        if ($enrollment->getOriginal('status') != Enrollment::STATUS_COMPLETED && $enrollment->status == Enrollment::STATUS_COMPLETED) {
            EnrollArchive::query()->firstOrCreate([
                'enroll_id' => $enrollment->id,
            ], [
                'name' => $enrollment->student->name,
                'phone' => $enrollment->student->phone,
                'photo' => $enrollment->student->photo,
                'id_card_number' => $enrollment->student->id_card_number,
                'id_card_front' => $enrollment->student->id_card_front,
                'id_card_back' => $enrollment->student->id_card_back,
                'extra' => $enrollment->student->extra,
            ]);

            Enrollment::query()->where('id', $enrollment->id)->update(['archived' => 1]);
        }
    }
}
