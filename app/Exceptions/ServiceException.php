<?php

namespace App\Exceptions;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * 服务异常
 *
 * 为了防止未处理的异常，此异常继承了 BadRequestHttpException，但是在实际业务中不要依靠 ServiceException 来直接向前端抛异常。
 * ServiceException 定义为服务内部异常，未考虑到的内部异常实际上就应该输出为 500 错误，请根据实际调用情况对 ServiceException 进行捕获和错误提示。
 */
class ServiceException extends BadRequestHttpException
{

    public function __construct($message = "", $code = 0, \Exception $previous = null)
    {
        parent::__construct($message, $previous, $code);
    }

}
