<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $e)
    {
        if (substr($request->getPathInfo(), 1, 3) == 'api' || substr($request->getPathInfo(), 1, 5) == 'admin') {
            $realException = $e->getPrevious() ?: $e;
            $data = $this->convertExceptionToArray($realException);

            $headers = [];
            if ($e instanceof HttpExceptionInterface) {
                $data['code'] = $e->getStatusCode();
                $headers = $e->getHeaders();

                if ($data['code'] == 404 && $data['message'] == '') {
                    $data['message'] = 'Resource not found.';
                } else {
                    $data['message'] = $e->getMessage();
                }

                $data['code'] = $data['code'] ?: 400;

            } elseif ($e instanceof ValidationException) {
                throw new BadRequestHttpException($e->getMessage());
            } elseif ($e instanceof AuthenticationException) {
                $data['code'] = 401;
                $data['message'] = '您尚未登录';
            } else {
                $data['code'] = 500;
            }

            if (isset($data['trace']) && count($data['trace']) > 10) {
                $data['trace'] = array_slice($data['trace'], 0, 10);
            }

            return response()->json($data, $data['code'], $headers, JSON_UNESCAPED_UNICODE);
        } else {
            return parent::render($request, $e);
        }
    }

}
