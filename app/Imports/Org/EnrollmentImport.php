<?php

namespace App\Imports\Org;

use App\Models\Org\Enrollment;
use App\Models\Org\EnrollmentForm;
use App\Models\Org\OrgClass;
use App\Models\User;
use App\Services\Org\EnrollmentService;
use App\Services\Org\StudentService;
use App\Services\User\LoginService;
use Exception;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Throwable;

class EnrollmentImport implements ToCollection, WithHeadingRow
{
    use Importable;

    /**
     * 机构ID
     *
     * @var int
     */
    protected int $orgId;

    /**
     * 字段映射
     *
     * @var array
     * [
     *  'name' => ['姓名', '学员姓名', 'name'],
     *  'phone' => ['手机号', '电话', '联系电话', 'phone'],
     *  'id_card_number' => ['身份证号', '身份证号码', 'id_card_number'],
     * ]
     */
    protected array $mappings = [];

    /**
     * 验证规则
     *
     * @var array
     * [
     *  'name' =>'string|max:64|required',
     *  'phone' =>'string|max:20|required',
     *  'id_card_number' =>'string|max:18|required',
     * ]
     */
    protected array $rules = [];

    /**
     * 导入结果
     *
     * @var array
     * [
     *  'success' => 0,
     *  'failed' => 0,
     *  'errors' => [],
     * ]
     */
    protected array $results = [
        'success' => 0,
        'failed' => 0,
        'errors' => [],
    ];

    /**
     * 扩展字段前缀
     *
     * @var string
     */
    const EXTRA_FILED_PRE = 'extra_';

    /**
     * 表单选项
     *
     * @var EnrollmentForm|null
     */
    protected ?EnrollmentForm $form = null;

    public function __construct(int $orgId)
    {
        $this->orgId = $orgId;

        // 获取机构表单配置
        /** @var EnrollmentForm $form */
        $form = EnrollmentForm::query()->where('org_id', $this->orgId)->first();

        if ($form) {
            $this->form = $form;
        }

        $this->setupMappings();
        $this->setupRules();
    }

    /**
     * 处理导入的数据集合
     *
     * @param Collection $collection
     * @return void
     * @throws Throwable
     */
    public function collection(Collection $collection): void
    {
        foreach ($collection as $index => $row) {
            try {
                // 准备并验证数据
                list($data, $fieldHeaders) = $this->prepareData($row);

                // 检查是否存在必填字段
                if (!$this->validateRequiredFields($data, $collection->count())) {
                    return;
                }

                // 验证并转换数据
                foreach ($data as $field => $value) {
                    if (isset($this->rules[$field])) {
                        $rules = explode('|', $this->rules[$field]);
                        $data[$field] = $this->processRules($value, $rules, $fieldHeaders[$field] ?? $field);
                    }
                }

                $extras = [];

                // 遍历所有数据，提取额外字段
                foreach ($data as $key => $value) {
                    if (str_starts_with($key, self::EXTRA_FILED_PRE)) {
                        $fieldId = substr($key, strlen(self::EXTRA_FILED_PRE));
                        $extras[$fieldId] = $value;
                    }
                }

                // 创建学员记录
                $enrollment = $this->createEnrollment(
                    $data['name'],
                    $data['phone'],
                    $data['id_card_number'],
                    $data['class_id'] ?? 0,
                    $extras
                );

                // 只有新创建的才计数
                if ($enrollment->wasRecentlyCreated) {
                    $this->results['success']++;
                }
            } catch (Throwable $e) {
                $this->results['failed']++;
                $this->results['errors'][] = [
                    'row' => $index + 2, // Excel行号从2开始（1是标题）
                    'message' => $e->getMessage()
                ];
            }
        }
    }

    /**
     * 获取导入结果
     *
     * @return array
     */
    public function getResults(): array
    {
        return $this->results;
    }

    /**
     * 根据表单配置设置字段映射
     */
    protected function setupMappings(): void
    {
        // 基础字段映射（必须包含的）
        $this->mappings = [
            'name' => ['姓名', '学员姓名', 'name'],
            'phone' => ['手机号', '电话', '联系电话', 'phone'],
            'id_card_number' => ['身份证号', '身份证号码', 'id_card_number'],
            'class_id' => ['班级ID', '班级编号', 'class_id'],
        ];
        $formFields = $this->form->fields ?? [];

        // 根据表单字段添加额外映射，所有字段都映射，不考虑是否必填
        foreach ($formFields as $field) {
            $fieldName = $field['name'];
            $fieldId = $field['id'];
            $fieldType = $field['type'];

            // 根据字段类型添加映射，支持所有可导入的字段类型
            switch ($fieldType) {
                case EnrollmentForm::TYPE_TEXT:
                case EnrollmentForm::TYPE_TEXTAREA:
                case EnrollmentForm::TYPE_SELECT:
                case EnrollmentForm::TYPE_RADIO:
                case EnrollmentForm::TYPE_CHECKBOX:
                case EnrollmentForm::TYPE_DATE:
                    // 将表单字段名称添加到映射中
                    $this->mappings[self::EXTRA_FILED_PRE . $fieldId] = [$fieldName];
                    break;
            }
        }
    }

    /**
     * 根据表单配置设置验证规则
     */
    protected function setupRules(): void
    {
        // 基础验证规则，为必填字段添加长度限制
        $this->rules = [
            'name' => 'string|max:64|required',
            'phone' => 'string|max:20|required',
            'id_card_number' => 'string|max:18|required',
            'class_id' => 'integer',
        ];
        $formFields = $this->form->fields ?? [];

        // 根据表单字段添加额外验证规则
        foreach ($formFields as $field) {
            $fieldId = $field['id'];
            $fieldType = $field['type'];
            $fieldConfig = $field['config'] ?? [];

            // 构建规则，只关注类型
            $rule = '';

            switch ($fieldType) {
                case EnrollmentForm::TYPE_TEXT:
                case EnrollmentForm::TYPE_TEXTAREA:
                case EnrollmentForm::TYPE_SELECT:
                case EnrollmentForm::TYPE_RADIO:
                case EnrollmentForm::TYPE_CHECKBOX:
                    $rule = 'string';

                    // 如果是数字类型
                    if (isset($fieldConfig['mask']) && $fieldConfig['mask'] == 'number') {
                        $rule = 'number';
                    }

                    break;
                case EnrollmentForm::TYPE_DATE:
                    $rule = 'date';
                    break;
            }

            if (!empty($rule)) {
                $this->rules[self::EXTRA_FILED_PRE . $fieldId] = $rule;
            }
        }
    }

    /**
     * 处理规则验证和类型转换
     *
     * @param mixed $value 字段值
     * @param array $rules 规则数组
     * @param string $fieldHeader 字段标题（用于错误提示）
     * @return mixed 处理后的值
     * @throws Exception 验证失败时抛出异常
     */
    protected function processRules(mixed $value, array $rules, string $fieldHeader): mixed
    {
        // 处理必填项
        if (in_array('required', $rules) && empty($value)) {
            throw new Exception($fieldHeader . '为必填项');
        }

        // 处理不同类型的转换和验证
        if (in_array('integer', $rules) || in_array('number', $rules)) {
            // 检查是否为有效数字
            if (!is_numeric($value)) {
                return 0; // 非数字返回0
            }

            $numericValue = floatval($value);

            // 如果是整数类型，转换为整数
            if (in_array('integer', $rules)) {
                return intval($numericValue);
            }

            return $numericValue;
        }

        // 处理日期类型
        if (in_array('date', $rules)) {
            try {
                // 尝试转换日期
                $timestamp = strtotime($value);
                if ($timestamp === false) {
                    return ''; // 无效日期返回当前日期
                }
                return date('Y-m-d', $timestamp);
            } catch (Exception $e) {
                return ''; // 出错返回当前日期
            }
        }

        if (in_array('string', $rules)) {
            $stringValue = (string)$value;

            // 字符串长度限制，防止插入数据库出错
            foreach ($rules as $rule) {
                if (str_starts_with($rule, 'max:')) {
                    $maxLength = (int)substr($rule, 4);
                    if (mb_strlen($stringValue) > $maxLength) {
                        $stringValue = mb_substr($stringValue, 0, $maxLength);
                    }
                }
            }

            return $stringValue;
        }

        return $value;
    }

    /**
     * 创建学员基础信息
     *
     * @param string $name 姓名
     * @param string $phone 手机号
     * @param string $idCardNumber 身份证号
     * @param int $classId 班级ID
     * @param array $extras
     * @return Enrollment
     * @throws Throwable
     */
    protected function createEnrollment(string $name, string $phone, string $idCardNumber, int $classId = 0, array $extras = []): Enrollment
    {
        // 获取或创建用户
        $userId = User::query()->where('phone', $phone)->value('id');

        if (!$userId) {
            $userId = LoginService::createUser($phone, '')->id;
        }

        /** @var OrgClass $class */
        $class = OrgClass::query()->where('id', $classId)->first();

        if (!$class) {
            $classId = 0;
        } elseif ($class->status == OrgClass::STATUS_FINISHED) {
            throw new Exception('需要分配的班级已结束');
        }

        $student = StudentService::check($this->orgId, $userId, $name, $phone, $idCardNumber, extras: $extras);

        if ($classId == 0) {
            return new Enrollment();
        }

        return EnrollmentService::create($this->orgId, $userId, $student->id, $classId);
    }

    /**
     * 根据映射关系准备数据
     *
     * @param Collection $row Excel行数据
     * @return array [数据数组, 字段标题数组]
     */
    protected function prepareData(Collection $row): array
    {
        $data = [];
        $fieldHeaders = [];

        // 根据映射关系提取数据
        foreach ($this->mappings as $field => $possibleHeaders) {
            // 查找匹配的表头
            $matchedHeader = collect($possibleHeaders)
                ->first(fn($header) => $row->has($header));

            if ($matchedHeader) {
                $data[$field] = trim($row[$matchedHeader]);
                $fieldHeaders[$field] = $matchedHeader;
            }
        }

        return [$data, $fieldHeaders];
    }

    /**
     * 验证必传字段
     *
     * @param array $data
     * @param int $total
     * @return bool
     */
    protected function validateRequiredFields(array $data, int $total): bool
    {
        $requiredFields = array_filter($this->rules, function ($rule) {
            return str_contains($rule, 'required');
        });

        $requiredFailedFields = [];

        foreach ($requiredFields as $field => $rule) {
            if (empty($data[$field])) {
                $requiredFailedFields[] = $this->mappings[$field][0] ?? $field;
            }
        }

        if (!empty($requiredFailedFields)) {
            $this->results = [
                'success' => 0,
                'failed' => $total,
                'errors' => [
                    [
                        'row' => 1,
                        'message' => '缺少必传字段：' . implode(',', $requiredFailedFields),
                    ]
                ]
            ];

            return false;
        }

        return true;
    }
}
