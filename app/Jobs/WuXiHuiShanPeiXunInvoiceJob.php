<?php

namespace App\Jobs;

use App\Models\Org\Enroll;
use App\Services\Org\BwjfService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

/**
 * 无锡市惠山区安全培训中心 - 开具发票队列任务
 */
class WuXiHuiShanPeiXunInvoiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // 报名ID
    private int $enrollId;

    public function __construct(int $enrollId)
    {
        $this->enrollId = $enrollId;
    }

    public function handle(): void
    {
        $enroll = Enroll::find($this->enrollId);
        if (!$enroll) {
            logger()->warning("无锡市惠山区安全培训中心 - 开具发票队列任务，报名记录不存在", [
                'enroll_id' => $this->enrollId
            ]);
            return;
        }
        if ($enroll->is_invoiced) {
            logger()->warning("无锡市惠山区安全培训中心 - 开具发票队列任务，报名记录已开票", [
                'enroll_id' => $this->enrollId
            ]);
            return;
        }
        if (!in_array($enroll->status, [Enroll::STATUS_PAID, Enroll::STATUS_REFUND_CLOSED])) {
            logger()->warning("无锡市惠山区安全培训中心 - 开具发票队列任务，报名记录此状态下，不能开票", [
                'enroll_id' => $this->enrollId,
                'status' => $enroll->status
            ]);
            return;
        }
        if ($enroll?->order?->payment_amount <= 0) {
            logger()->warning("无锡市惠山区安全培训中心 - 开具发票队列任务，报名记录金额为0，不能开票", [
                'enroll_id' => $this->enrollId,
                'amount' => $enroll?->order?->payment_amount
            ]);
            return;
        }
        $name = $enroll->student->name;
        $phone = $enroll->student->phone;
        $amount = $enroll->order->payment_amount;
        $res = app(BwjfService::class)->issueInvoice($name, $phone, $amount);
        if ($res['success']) {
            $enroll->is_invoiced = true;
            $enroll->save();
            logger()->info("无锡市惠山区安全培训中心 - 开具发票队列任务，开票成功", [
                'enroll_id' => $this->enrollId,
                'data' => $res['data']
            ]);
        } else {
            logger()->error("无锡市惠山区安全培训中心 - 开具发票队列任务，开票失败", [
                'enroll_id' => $this->enrollId,
                'message' => $res['message']
            ]);
        }
    }
}
