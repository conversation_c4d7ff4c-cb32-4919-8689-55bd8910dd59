<?php
/**
 * CoursePackAddCourseJob.php class file.
 *
 * <AUTHOR>
 * @time 2025/4/30 17:10
 * @copyright 2025 pp.cc All Right Reserved
 */

namespace App\Jobs;

use App\Models\Org\EnrollCourse;
use App\Models\Org\Enrollment;
use DragonCode\Contracts\Queue\ShouldQueue;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CoursePackAddCourseJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public int $coursePackId,
        public int $courseId,
    ){}

    public function handle(): void
    {
        $logger = Log::channel('task');

        // 查询所有绑定该课程的学员
        $enrollQuery = Enrollment::query()
            ->where('type', Enrollment::TYPE_COURSE_PACK)
            ->where('resource_id', $this->coursePackId);

        $enrollQuery->chunkById(100, function ($enrolls) use ($logger) {
            foreach ($enrolls as $enroll) {
                // 为学员添加课程
                $exists = EnrollCourse::query()
                    ->where('enroll_id', $enroll->id)
                    ->where('course_id', $this->courseId)
                    ->exists();

                if (!$exists) {
                    $enrollCourse = new EnrollCourse();
                    $enrollCourse->org_id = $enroll->org_id;
                    $enrollCourse->enroll_id = $enroll->id;
                    $enrollCourse->course_id = $this->courseId;
                    $enrollCourse->learned_duration = 0;
                    $enrollCourse->learn_finished = false;
                    $enrollCourse->save();

                    $logger->info('课程包新增课程 EnrollCourse 插入成功', [
                        'enroll_id' => $enroll->id,
                        'course_id' => $this->courseId,
                    ]);
                } else {
                    $logger->info('课程包新增课程 EnrollCourse 已存在，跳过', [
                        'enroll_id' => $enroll->id,
                        'course_id' => $this->courseId,
                    ]);
                }
            }
        }, 'id');



    }
}
