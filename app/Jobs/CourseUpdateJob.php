<?php
/**
 * CourseUpdateJob.php class file.
 * 更新课程时长
 * <AUTHOR>
 * @time 2025/3/17 17:42
 * @copyright 2025 pp.cc All Right Reserved
 */

namespace App\Jobs;

use App\Models\Cms\ContentCoursePack;
use App\Models\Cms\ContentCourseSection;
use App\Models\Org\Course;
use App\Models\Org\CoursePack;
use App\Services\Org\TopicService;
use DragonCode\Contracts\Queue\ShouldQueue;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePackList;
use App\Models\Cms\ContentCourseStatus;
use App\Services\Org\CourseService;
use Psr\Log\LoggerInterface;


class CourseUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const TYPE_HOUR = 'hour';
    const TYPE_TOPIC = 'topic';

    const TYPE_COURSE_PACK = 'course_pack';

    public function __construct(
        public int $courseId,
        public int $orgId = 0,
        public string $type = self::TYPE_HOUR,
        public int $userId = 0,
        public int $sectionId = 0,
    ){}

    public function handle(): void
    {
        $logger = Log::channel('task');

       match($this->type) {
            self::TYPE_HOUR => $this->handleHour($logger),
            self::TYPE_TOPIC => $this->handleTopic($logger),
            self::TYPE_COURSE_PACK => $this->handleCoursePack($logger),
            default => $logger->warning('未知的课程更新队列触发类型: ' . $this->type),
        };
    }

    /**
     * 更新课程时长
     * @param $logger
     */
    protected function handleHour($logger)
    {
        try {
            $logger->info('开始更新学时时长', [
                'course_id' => $this->courseId,
                'org_id' => $this->orgId,
                'user_id' => $this->userId,
                'section_id' => $this->sectionId, // 暂未支持
            ]);

            /** @var ContentCourse $contentCourse */
            $contentCourse = ContentCourse::where('content_id', $this->courseId)
                ->with(['chapters' => function($q) {
                    $q->select('id', 'status', 'content_id')->where('status', ContentCourseChapter::STATUS_SHOW);
                }])
                ->with(['sections' => function($q) {
                    $q->select('id', 'status', 'content_id')->where('status', ContentCourseSection::STATUS_SHOW);
                }])
                ->first();

            if (!$contentCourse) {
                $logger->warning('学时更新失败，未找到内容课程', [
                    'org_id' => $this->orgId,
                    'course_id' => $this->courseId,
                ]);
                return;
            }

            // 执行事务
            DB::transaction(function() use ($logger, $contentCourse) {
                $difference = [];
                
                $this->updateCourse($logger, $contentCourse, $difference);
                $this->updateCoursePack($logger, $difference);
                $this->updateContentCourseStatus($logger, $contentCourse);
            });

            $logger->info('学时更新事务完成', [
                'course_id' => $this->courseId,
                'org_id' => $this->orgId,
                'user_id' => $this->userId,
            ]);

        } catch (\Throwable $e) {
            $logger->error('学时更新事务失败', [
                'course_id' => $this->courseId,
                'org_id' => $this->orgId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    private function updateCourse($logger, $contentCourse, &$difference)
    { 
        $chapterIds = $contentCourse->chapters->pluck('id')->toArray();
        $sectionIds = $contentCourse->sections->pluck('id')->toArray();

        if (empty($chapterIds) || empty($sectionIds)) {
            $logger->info('课程时长更新失败，未找到内容章或节', [
                'org_id' => $this->orgId,
                'course_id' => $this->courseId,
            ]);
        }

        $query = Course::query()->where('course_id', $this->courseId);

        if ($this->orgId) {
            $query->where('org_id', $this->orgId);
        }

        $query->chunk(100, function ($courses) use ($logger, $contentCourse, $sectionIds, $chapterIds, &$difference) {
            foreach ($courses as $course) {
                $lessons = json_decode($course->lessons, true) ?? [];

                $totalEnabledChapDuration = 0;
                foreach ($lessons as &$chapter) {
                    // 显示 且 已开启
                    if ($chapter['enabled'] == false || !in_array($chapter['id'], $chapterIds) || empty($chapter['sections'])) {
                        continue;
                    }

                    $totalEnabledSecDuration = 0;
                    foreach ($chapter['sections'] as &$section) {

                        // 节未开启 / 节不在显示列表中
                        if ($section['enabled'] == false || !in_array($section['id'], $sectionIds)) {
                            continue;
                        }
                        $section['hour'] = $contentCourse->studyHour($section['duration']);
                        $totalEnabledSecDuration += $section['duration'];
                    }

                    $chapter['duration'] = $totalEnabledSecDuration;
                    $chapter['hour'] = $contentCourse->studyHour($totalEnabledSecDuration);
                    $totalEnabledChapDuration += $totalEnabledSecDuration;
                }

                $hour = $contentCourse->studyHour($totalEnabledChapDuration, false);
                $oldHour = $course->getOriginal('hour');
                $oldDuration = $course->getOriginal('duration');

                $difference[$course->org_id] = [
                    'hour' => $hour - $oldHour,
                    'duration' => $totalEnabledChapDuration - $oldDuration,
                ];

                $course->hour = $hour;
                $course->duration = $totalEnabledChapDuration;
                $course->lessons = json_encode($lessons);
                $course->save();
                
                $logger->info('课程时长更新成功', [
                    'org_id' => $course->org_id,
                    'course_id' => $course->course_id,
                    'old_hour' => $oldHour,
                    'hour' => $course->hour,
                    'old_duration' => $oldDuration,
                    'duration' => $course->duration,
                    'difference' => $difference
                ]);
            }
        });
    }

    private function updateCoursePack($logger, $difference)
    {
        if ($difference == null) {
            return;
        }

        $logger->info('开始更新课程包时长', [
            'course_id' => $this->courseId,
            'org_id' => $this->orgId
        ]);

        $coursePackQuery = CoursePack::query()
            ->whereIn('course_pack_id', function($query) {
                $query->select('content_id')
                    ->from((new ContentCoursePackList())->getTable())
                    ->where('course_id', $this->courseId);
            });

        if ($this->orgId) {
            $coursePackQuery->where('org_id', $this->orgId);
        }

        $packs = $coursePackQuery->get();
        $updatedCount = 0;

        foreach ($packs as $pack) {
            isset($difference[$pack->org_id]) && $updatedCount += $pack->update([
                'hour' => DB::raw("GREATEST(0, hour + {$difference[$pack->org_id]['hour']})"),
                'duration' => DB::raw("GREATEST(0, duration + {$difference[$pack->org_id]['duration']})"),
            ]);
        }

        $logger->info('完成课程包时长更新', [
            'updated_count' => $updatedCount,
        ]);
    }

    private function updateContentCourseStatus($logger, $contentCourse)
    { 
        $logger->info('开始更新用户课程进度', [
            'course_id' => $this->courseId,
            'org_id' => $this->orgId
        ]);

        $statusQuery = ContentCourseStatus::query()
            ->where('content_id', $this->courseId)
            ->where('finished', ContentCourseStatus::FINISHED_NOT);

        if ($this->orgId) {
            $statusQuery->where('org_id', $this->orgId);
        }

        if ($this->userId) {
            $statusQuery->where('user_id', $this->userId);
        }

        $statusQuery->chunkById(100, function ($statuses) use ($logger, $contentCourse) { 
            $updatedCount = 0;
            foreach ($statuses as $status) {
                list($courseHour, $courseDuration) = CourseService::getCourseHourAndDuration($contentCourse, $status->org_id);

                list($studyHour, $studyPercent) = 
                    $contentCourse->studyHourForPercent($status->valid_duration, $courseHour);

                $updatedCount += $status->update([
                    'hour' => (int)$studyHour,
                    'percent' => $studyPercent,
                ]);
            }

            $logger->info('完成用户课程进度更新', ['updated_count' => $updatedCount]);
        });
    }

    /**
     * 更新课程题库
     * @param LoggerInterface $logger
     */
    protected function handleTopic($logger)
    {
        $logger->info('开始更新课程题库', [
            'course_id' => $this->courseId,
            'org_id' => $this->orgId
        ]);

        /** @var ContentCourse $contentCourse */
        $contentCourse = ContentCourse::query()
            ->with('topic')
            ->where('content_id', $this->courseId)
            ->first();

        // 如果课程题库变化，且题库不为空，则更新机构题库
        if ($contentCourse && $contentCourse->topic) {
            $orgCourseQuery = Course::query()->where('course_id', $this->courseId);

            $orgCourseQuery->chunkById(100, function ($courses) use ($logger, $contentCourse) {
                $logData = [];

                /** @var Course $course */
                foreach ($courses as $course) {
                    $save = TopicService::checkCreate($course->org_id, $contentCourse->topic);

                    if ($save) {
                        $logData[] = [
                            'org_id' => $course->org_id,
                            'course_id' => $course->course_id,
                            'topic_id' => $contentCourse->topic_id
                        ];
                    }
                }

                $logger->info('机构新增题库成功', $logData);
            });
        }
    }

    /**
     * 更新课程包题库
     * @param LoggerInterface $logger
     */
    protected function handleCoursePack($logger)
    {
        $logger->info('开始更新课程包题库', [
            'course_pack_id' => $this->courseId,
            'org_id' => $this->orgId
        ]);

        /** @var ContentCoursePack $contentCoursePack */
        $contentCoursePack = ContentCoursePack::query()
            ->with('topic')
            ->where('content_id', $this->courseId)
            ->first();

        // 如果课程题库变化，且题库不为空，则更新机构题库
        if ($contentCoursePack && $contentCoursePack->topic) {
            $orgCoursePackQuery = CoursePack::query()->where('course_pack_id', $this->courseId);

            $orgCoursePackQuery->chunkById(100, function ($coursePacks) use ($logger, $contentCoursePack) {
                $logData = [];

                /** @var CoursePack $coursePack */
                foreach ($coursePacks as $coursePack) {
                    $save = TopicService::checkCreate($coursePack->org_id, $contentCoursePack->topic);

                    if ($save) {
                        $logData[] = [
                            'org_id' => $coursePack->org_id,
                            'course_pack_id' => $coursePack->course_pack_id,
                            'topic_id' => $contentCoursePack->topic_id
                        ];
                    }
                }

                $logger->info('机构新增题库成功', $logData);
            });
        }
    }
}