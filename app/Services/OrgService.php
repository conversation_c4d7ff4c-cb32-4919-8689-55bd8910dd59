<?php

namespace App\Services;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Org;
use App\Services\Admin\AttachmentRelationService;

class OrgService
{
    /**
     * 创建机构
     *
     * @param array $data
     * @return Org
     */
    public static function create(array $data): Org
    {
        $org = new Org();
        $org->fill($data);
        $org->save();

        return $org;
    }

    public static function update(int $id, array $params): Org
    {
        /** @var Org $org */
        $org = Org::query()->find($id);

        if (!$org) {
            throw new ServiceException('机构不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $org->getFillable()) && !in_array($key, ['logo', 'business_license', 'service_qrcode', 'official_seal_image', 'enroll_bg_img'])) {
                $org->{$key} = $val;
            }
        }

        $org->save();

        AttachmentRelationService::saveAttachment($org, $params, 'org', BusinessType::Org, false,  'logo', 'logo');
        AttachmentRelationService::saveAttachment($org, $params, 'org', BusinessType::Org, false,  'business_license', 'business_license');
        AttachmentRelationService::saveAttachment($org, $params, 'org', BusinessType::Org, false,  'service_qrcode', 'service_qrcode');
        AttachmentRelationService::saveAttachment($org, $params, 'org', BusinessType::Org, false,  'official_seal_image', 'official_seal_image');
        AttachmentRelationService::saveAttachment($org, $params, 'org', BusinessType::Org, false,  'enroll_bg_img', 'enroll_bg_img');

        return $org;
    }
}
