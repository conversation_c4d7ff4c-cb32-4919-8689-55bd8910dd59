<?php

namespace App\Services\Stat;

use App\Core\Enums\BusinessType;
use App\Models\Cms\Category;
use App\Models\Cms\Content;
use App\Models\Cms\ContentDoc;
use App\Models\Cms\Special;
use App\Models\Order\Order;
use App\Models\Stat\DailyPromoter;
use App\Models\User;
use App\Models\User\UserCreditLog;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class DailyPromoterService extends StatService
{
    public static function increment(int $adminId, string $field = 'content_view', int $number = 1): void
    {
        if ($adminId <= 0) {
            return;
        }

        $date = Carbon::now()->toDateString();

        $stat = DailyPromoter::query()->where('admin_id', $adminId)->where('date', $date)->first();

        if (!$stat) {
            $stat = self::yesterday($adminId, $date);
        }

        $stat->increment($field, $number);
    }

    public static function toSumSelects(): array
    {
        return [
            'sum(consume_credit) consume_credit',
            'sum(consume_credit_content) consume_credit_content',
            'sum(consume_credit_special) consume_credit_special',
            'sum(payment_order) payment_order',
            'sum(payment_amount) payment_amount',
            'sum(payment_user) payment_user',
            'sum(payment_course_order) payment_course_order',
            'sum(payment_course_amount) payment_course_amount',
            'sum(payment_course_user) payment_course_user',
            'sum(content) content',
            'sum(content_material) content_material',
            'sum(content_course) content_course',
            'sum(content_news) content_news',
            'sum(content_view) content_view',
            'sum(content_download) content_download',
            'sum(content_special) content_special'
        ];
    }

    /**
     * 获取统计数据
     * @param int $adminId
     * @param null $startDate
     * @param null $endDate
     * @return array
     */
    public static function getSubtotalData(int $adminId, $startDate = null, $endDate = null): array
    {
        $dayDateRange = self::getDateRange($startDate, $endDate);

        $select = self::toSumSelects();

        $statisticModel = new DailyPromoter();
        $builder = $statisticModel::query()->selectRaw(implode(',', $select))->where('admin_id', $adminId);

        if (!empty($dayDateRange)) {
            $builder->whereBetween('date', $dayDateRange);
        }

        $data = $builder->first();

        if ($data->consume_credit != null) {
            $data = $data->toArray();

            $platformData = $statisticModel::query()
                ->select(['platform_content', 'platform_content_material', 'platform_content_course', 'platform_content_news'])
                ->where('admin_id', $adminId)
                ->orderBy('id', 'desc')
                ->first();

            if ($platformData) {
                $platformData = $platformData->toArray();
            } else {
                $platformData = [
                    'platform_content' => 0,
                    'platform_content_material' => 0,
                    'platform_content_course' => 0,
                    'platform_content_news' => 0,
                ];
            }

            $data = array_merge($data, $platformData);
        } else {
            $data = $statisticModel->getAttributes();
        }

        return $data;
    }

    /**
     * 保存昨天的数据
     * @param int $adminId
     * @param null $date
     * @return DailyPromoter
     */
    public static function yesterday(int $adminId, $date = null): DailyPromoter
    {
        $data = self::day($adminId, $date);

        /** @var DailyPromoter $statistic */
        $statistic = DailyPromoter::query()->updateOrCreate(['admin_id' => $adminId, 'date' => $data['date']], $data);

        return $statistic;
    }

    /**
     * 获取某天的统计数据
     * @param int $adminId
     * @param null $date
     * @return array
     */
    public static function day(int $adminId, $date = null): array
    {
        if ($date) {
            $startDate = $date;
        } else {
            $startDate = Carbon::now()->subDay()->toDateString();
        }

        $dateRange = [Carbon::parse($startDate)->startOfDay()->toDateTimeString(), Carbon::parse($startDate)->endOfDay()->toDateTimeString()];
        $day = ['date' => $startDate];

        // 课程、资料的积分
        $contentConsumeCredit = UserCreditLog::query()
            ->whereBetween('created_at', $dateRange)
            ->where('type', UserCreditLog::TYPE_CONSUME)
            ->whereIn('business_type', [BusinessType::CmsCourse, BusinessType::CmsMaterial])
            ->whereIn('business_id', function (Builder $builder) use ($adminId) {
                $builder->from((new Content())->getTable())->select(['id'])->where('admin_id', $adminId);
            })
            ->sum('real_change_credit');

        // 专题的积分
        $specialConsumeCredit = UserCreditLog::query()
            ->whereBetween('created_at', $dateRange)
            ->where('type', UserCreditLog::TYPE_CONSUME)
            ->where('business_type', BusinessType::Special)
            ->whereIn('business_id', function (Builder $builder) use ($adminId) {
                $builder->from((new Special())->getTable())->select(['id'])->where('admin_id', $adminId);
            })
            ->sum('real_change_credit');

        // 积分统计
        $day['consume_credit_content'] = abs($contentConsumeCredit);
        $day['consume_credit_special'] = abs($specialConsumeCredit);
        $day['consume_credit'] = $day['consume_credit_content'] + $day['consume_credit_special'];

        // 内容统计
        $day['content'] = Content::query()
            ->where('admin_id', $adminId)
            ->whereBetween('release_at', $dateRange)
            ->count();
        $day['content_material'] = Content::query()
            ->whereBetween('release_at', $dateRange)
            ->where('admin_id', $adminId)
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_MATERIAL);
            })->count();
        $day['content_course'] = Content::query()
            ->whereBetween('release_at', $dateRange)
            ->where('admin_id', $adminId)
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_COURSE);
            })->count();
        $day['content_news'] = Content::query()
            ->where('admin_id', $adminId)
            ->whereBetween('release_at', $dateRange)
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_NEWS);
            })->count();
        $day['content_download'] = User\UserContentDownload::query()
            ->whereBetween('created_at', $dateRange)
            ->whereIn('content_id', function (Builder $builder) use ($adminId) {
                $builder->from((new Content())->getTable())->select(['id'])->where('admin_id', $adminId);
            })
            ->count();
        $day['content_special'] = Special::query()
            ->where('admin_id', $adminId)
            ->whereBetween('created_at', $dateRange)
            ->count();

        // 平台内容统计
        $day['platform_content'] = Content::query()
            ->where('admin_id', $adminId)
            ->whereNotNull('release_at')
            ->count();
        $day['platform_content_material'] = Content::query()
            ->where('admin_id', $adminId)
            ->whereNotNull('release_at')
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_MATERIAL);
            })->count();
        $day['platform_content_course'] = Content::query()
            ->where('admin_id', $adminId)
            ->whereNotNull('release_at')
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_COURSE);
            })->count();
        $day['platform_content_news'] = Content::query()
            ->where('admin_id', $adminId)
            ->whereNotNull('release_at')
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_NEWS);
            })->count();

        // 订单统计
        $orderStatus = [Order::STATUS_PAID, Order::STATUS_REF_PEN, Order::STATUS_REF_HAVE, Order::STATUS_REF_DONE];
        $day['payment_order'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->whereIn('business_type', [BusinessType::CmsCourse, BusinessType::CmsMaterial])
            ->whereIn('business_id', function (Builder $builder) use ($adminId) {
                $builder->from((new Content())->getTable())->select(['id'])->where('admin_id', $adminId);
            })
            ->count();
        $day['payment_amount'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->whereIn('business_type', [BusinessType::CmsCourse, BusinessType::CmsMaterial])
            ->whereIn('business_id', function (Builder $builder) use ($adminId) {
                $builder->from((new Content())->getTable())->select(['id'])->where('admin_id', $adminId);
            })
            ->sum('payment_amount');
        $day['payment_user'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->whereIn('business_type', [BusinessType::CmsCourse, BusinessType::CmsMaterial])
            ->whereIn('business_id', function (Builder $builder) use ($adminId) {
                $builder->from((new Content())->getTable())->select(['id'])->where('admin_id', $adminId);
            })
            ->count(DB::raw('distinct user_id'));
        $day['payment_course_order'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::CmsCourse)
            ->whereIn('business_id', function (Builder $builder) use ($adminId) {
                $builder->from((new Content())->getTable())->select(['id'])->where('admin_id', $adminId);
            })
            ->count();
        $day['payment_course_amount'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::CmsCourse)
            ->whereIn('business_id', function (Builder $builder) use ($adminId) {
                $builder->from((new Content())->getTable())->select(['id'])->where('admin_id', $adminId);
            })
            ->sum('payment_amount');

        $day['platform_material_amount'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::CmsMaterial)
            ->whereIn('business_id', function (Builder $builder) use ($adminId) {
                $builder->from((new Content())->getTable())->select(['id'])->where('admin_id', $adminId);
            })
            ->sum('payment_amount');
        $day['payment_course_user'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::CmsCourse)
            ->whereIn('business_id', function (Builder $builder) use ($adminId) {
                $builder->from((new Content())->getTable())->select(['id'])->where('admin_id', $adminId);
            })
            ->count(DB::raw('distinct user_id'));

        return $day;
    }

    /**
     * 管理员资料排行
     *
     * @param int $adminId 管理员ID
     * @return Collection
     */
    public static function contentDocRank(int $adminId): Collection
    {
        return ContentDoc::query()
            ->with('content', fn($q) => $q->publicFields())
            ->whereHas('content', fn ($q) => $q->where('admin_id', $adminId))
            ->orderByDesc('download_count')
            ->limit(50)
            ->get();
    }
}
