<?php

namespace App\Services\Stat;

use App\Core\Enums\BusinessType;
use App\Models\Cms\Category;
use App\Models\Cms\Content;
use App\Models\Cms\Special;
use App\Models\Order\Order;
use App\Models\Qa\Answer;
use App\Models\Qa\Question;
use App\Models\Stat\DailyOverview;
use App\Models\Train\Test;
use App\Models\User;
use App\Models\User\UserCreditLog;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class DailyOverviewService extends StatService
{
    public static function increment(string $field, int $number = 1): void
    {
        $date = Carbon::now()->toDateString();

        $overview = DailyOverview::query()->where('date', $date)->first();

        if (!$overview) {
            $overview = self::yesterday($date);
        }

        $overview->increment($field, $number);
    }

    /**
     * 获取统计数据
     * @param $startDate
     * @param $endDate
     * @return array
     */
    public static function getSubtotalData($startDate = null, $endDate = null): array
    {
        $dayDateRange = self::getDateRange($startDate, $endDate);

        $select = [
            'sum(user_register) user_register',
            'sum(user_active) user_active',
            'sum(payment_order) payment_order',
            'sum(payment_amount) payment_amount',
            'sum(payment_user) payment_user',
            'sum(payment_credit_order) payment_credit_order',
            'sum(payment_credit_amount) payment_credit_amount',
            'sum(payment_credit_user) payment_credit_user',
            'sum(payment_course_order) payment_course_order',
            'sum(payment_course_amount) payment_course_amount',
            'sum(payment_course_user) payment_course_user',
            'sum(payment_material_user) payment_material_user',
            'sum(payment_topic_order) payment_topic_order',
            'sum(payment_topic_amount) payment_topic_amount',
            'sum(payment_topic_user) payment_topic_user',
            'sum(consume_credit) consume_credit',
            'sum(content) content',
            'sum(content_material) content_material',
            'sum(content_course) content_course',
            'sum(content_news) content_news',
            'sum(content_view) content_view',
            'sum(content_download) content_download',
            'sum(content_special) content_special',
            'sum(practise) practise',
            'sum(question) question',
            'sum(answer) answer',
            'sum(search) search',
            'sum(collect) collect',
        ];

        $statisticModel = new DailyOverview();
        $builder = $statisticModel::query()->selectRaw(implode(',', $select));

        if (!empty($dayDateRange)) {
            $builder->whereBetween('date', $dayDateRange);
        }

        $data = $builder->first();

        if ($data->user_register != null) {
            $data = $data->toArray();

            $platformData = $statisticModel::query()
                ->select(['platform_content', 'platform_content_material', 'platform_content_course', 'platform_content_news'])
                ->orderBy('id', 'desc')
                ->first();

            if ($platformData) {
                $platformData = $platformData->toArray();
            } else {
                $platformData = [
                    'platform_content' => 0,
                    'platform_content_material' => 0,
                    'platform_content_course' => 0,
                    'platform_content_news' => 0,
                ];
            }

            $data = array_merge($data, $platformData);
        } else {
            $data = $statisticModel->getAttributes();
        }

        return $data;
    }

    /**
     * 保存昨天的数据
     * @param $date
     * @return DailyOverview
     */
    public static function yesterday($date = null): DailyOverview
    {
        $data = self::day($date);

        /** @var DailyOverview $statistic */
        $statistic = DailyOverview::query()->updateOrCreate(['date' => $data['date']], $data);

        return $statistic;
    }

    /**
     * 获取某天的统计数据
     * @param $date
     * @return array
     */
    public static function day($date = null): array
    {
        if ($date) {
            $startDate = $date;
        } else {
            $startDate = Carbon::now()->subDay()->toDateString();
        }

        $dateRange = [Carbon::parse($startDate)->startOfDay()->toDateTimeString(), Carbon::parse($startDate)->endOfDay()->toDateTimeString()];
        $day = ['date' => $startDate];

        // 用户统计
        $day['user_register'] = User::query()->whereBetween('created_at', $dateRange)->count();
        $day['user_active'] = User::query()->whereBetween('last_active_at', $dateRange)->count();

        // 积分统计
        $day['consume_credit'] = UserCreditLog::query()
            ->whereBetween('created_at', $dateRange)
            ->where('type', UserCreditLog::TYPE_CONSUME)
            ->sum('real_change_credit');

        $day['consume_credit'] = abs($day['consume_credit']);

        // 收藏统计
        $day['collect'] = User\UserFavorite::query()->whereBetween('created_at', $dateRange)->count();

        // 内容统计
        $day['content'] = Content::query()->whereBetween('release_at', $dateRange)->count();
        $day['content_material'] = Content::query()
            ->whereBetween('release_at', $dateRange)
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_MATERIAL);
            })->count();
        $day['content_course'] = Content::query()
            ->whereBetween('release_at', $dateRange)
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_COURSE);
            })->count();
        $day['content_news'] = Content::query()
            ->whereBetween('release_at', $dateRange)
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_NEWS);
            })->count();
        $day['content_download'] = User\UserContentDownload::query()->whereBetween('created_at', $dateRange)->count();
        $day['content_special'] = Special::query()->whereBetween('created_at', $dateRange)->count();

        // 平台内容统计
        $day['platform_content'] = Content::query()->whereNotNull('release_at')->count();
        $day['platform_content_material'] = Content::query()
            ->whereNotNull('release_at')
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_MATERIAL);
            })->count();
        $day['platform_content_course'] = Content::query()
            ->whereNotNull('release_at')
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_COURSE);
            })->count();
        $day['platform_content_news'] = Content::query()
            ->whereNotNull('release_at')
            ->whereIn('category_id', function (Builder $builder) {
                $builder->from((new Category())->getTable())->select('id')->where('classify', Category::CLASSIFY_NEWS);
            })->count();

        // 订单统计
        $orderStatus = [Order::STATUS_PAID, Order::STATUS_REF_PEN, Order::STATUS_REF_HAVE, Order::STATUS_REF_DONE];
        $day['payment_order'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            // 机构报名订单不计入统计
            ->whereNotIn('business_type', [BusinessType::Enroll])
            ->count();
        $day['payment_amount'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            // 机构报名订单不计入统计
            ->whereNotIn('business_type', [BusinessType::Enroll])
            ->sum('payment_amount');
        $day['payment_user'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            // 机构报名订单不计入统计
            ->whereNotIn('business_type', [BusinessType::Enroll])
            ->count(DB::raw('distinct user_id'));
        $day['payment_credit_order'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::Credit)
            ->count();
        $day['payment_credit_amount'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::Credit)
            ->sum('payment_amount');
        $day['payment_credit_user'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::Credit)
            ->count(DB::raw('distinct user_id'));
        $day['payment_course_order'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::CmsCourse)
            ->count();
        $day['payment_course_amount'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::CmsCourse)
            ->sum('payment_amount');
        $day['platform_material_amount'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::CmsMaterial)
            ->sum('payment_amount');
        //资料付款人数
        $day['payment_material_user'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::CmsMaterial)
            ->count(DB::raw('distinct user_id'));
        $day['payment_course_user'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::CmsCourse)
            ->count(DB::raw('distinct user_id'));

        $day['payment_topic_order'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::Topic)
            ->count();
        $day['payment_topic_amount'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::Topic)
            ->sum('payment_amount');
        $day['payment_topic_user'] = Order::withTrashed()
            ->whereBetween('payment_at', $dateRange)
            ->whereIn('status', $orderStatus)
            ->where('business_type', BusinessType::Topic)
            ->count(DB::raw('distinct user_id'));

        // 考试统计
        $day['practise'] = Test::query()->whereBetween('created_at', $dateRange)->count();

        // 问答统计
        $day['question'] = Question::query()->whereBetween('created_at', $dateRange)->count();
        $day['answer'] = Answer::query()->whereBetween('created_at', $dateRange)->count();

        return $day;
    }
}
