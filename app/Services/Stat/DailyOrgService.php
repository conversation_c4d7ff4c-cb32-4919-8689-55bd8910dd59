<?php

namespace App\Services\Stat;

use App\Models\Org\Enrollment;
use App\Models\Org\OrgClass;
use App\Models\Stat\Org as StatOrg;
use App\Models\Train\Test;
use Carbon\Carbon;

class DailyOrgService extends StatService
{
    public static function increment(int $orgId, string $field = 'enrollments', int $number = 1): void
    {
        if ($orgId <= 0) {
            return;
        }

        $date = Carbon::now()->toDateString();

        $stat = StatOrg::query()->where('org_id', $orgId)->where('date', $date)->first();

        if (!$stat) {
            $stat = self::yesterday($orgId, $date);
        }

        $stat->increment($field, $number);
    }

    public static function toSumSelects(): array
    {
        return [
            'sum(enrollments) enrollments',
            'sum(classes) classes',
            'sum(exams) exams',
            'sum(trained) trained',
        ];
    }

    /**
     * 获取统计数据
     * @param int $orgId
     * @param null $startDate
     * @param null $endDate
     * @return array
     */
    public static function getSubtotalData(int $orgId, $startDate = null, $endDate = null): array
    {
        $dayDateRange = self::getDateRange($startDate, $endDate);

        $select = self::toSumSelects();

        $statisticModel = new StatOrg();
        $builder = $statisticModel::query()->selectRaw(implode(',', $select))->where('org_id', $orgId);

        if (!empty($dayDateRange)) {
            $builder->whereBetween('date', $dayDateRange);
        }

        $data = $builder->first();

        if ($data->enrollments != null) {
            $data = $data->toArray();
        } else {
            $data = $statisticModel->getAttributes();
        }

        return $data;
    }

    /**
     * 保存昨天的数据
     * @param int $orgId
     * @param null $date
     * @return StatOrg
     */
    public static function yesterday(int $orgId, $date = null): StatOrg
    {
        $data = self::day($orgId, $date);

        /** @var StatOrg $statistic */
        $statistic = StatOrg::query()->updateOrCreate(['org_id' => $orgId, 'date' => $data['date']], $data);

        return $statistic;
    }

    /**
     * 获取某天的统计数据
     * @param int $orgId
     * @param null $date
     * @return array
     */
    public static function day(int $orgId, $date = null): array
    {
        if ($date) {
            $startDate = $date;
        } else {
            $startDate = Carbon::now()->subDay()->toDateString();
        }

        $dateRange = [Carbon::parse($startDate)->startOfDay()->toDateTimeString(), Carbon::parse($startDate)->endOfDay()->toDateTimeString()];
        $day = ['date' => $startDate];

        $day['enrollments'] = Enrollment::query()->where('org_id', $orgId)->whereBetween('created_at', $dateRange)->count();
        $day['classes'] = OrgClass::query()->where('org_id', $orgId)->whereBetween('created_at', $dateRange)->count();
        $day['exams'] = Test::query()->where('org_id', $orgId)->whereBetween('created_at', $dateRange)->count();
        $day['trained'] = Enrollment::query()
            ->where('org_id', $orgId)
            ->where('status', Enrollment::STATUS_COMPLETED)
            ->whereBetween('updated_at', $dateRange)
            ->count();

        return $day;
    }
}
