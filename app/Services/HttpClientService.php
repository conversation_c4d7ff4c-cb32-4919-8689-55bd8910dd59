<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class HttpClientService
{
    protected ?Client $client = null;
    protected array $headers = [];

    public function __construct()
    {
        $this->headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json'
        ];

        if (!$this->client) {
            $this->client = new Client([
                'headers' => $this->headers,
                'verify' => false // 是否验证SSL证书
            ]);
        }
    }

    /**
     * 设置请求头
     *
     * @param array $headers
     * @return $this
     */
    public function setHeaders(array $headers): static
    {
        $this->headers = array_merge($this->headers, $headers);
        return $this;
    }

    /**
     * GET请求
     *
     * @param string $uri
     * @param array $query
     * @return array
     * @throws GuzzleException
     */
    public function get(string $uri, array $query = []): array
    {
        try {
            $response = $this->client->get($uri, [
                'query' => $query,
                'headers' => $this->headers
            ]);
            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $e) {
            throw $e;
        }
    }

    /**
     * POST请求
     *
     * @param string $uri
     * @param array $data
     * @param bool $isJson
     * @return array
     * @throws GuzzleException
     */
    public function post(string $uri, array $data = [], bool $isJson = true): array
    {
        try {
            $options['headers'] = $this->headers;

            if ($isJson) {
                $options['json'] = $data;  // 发送 JSON 数据
            } else {
                $options['form_params'] = $data;  // 发送表单数据
            }

            $response = $this->client->post($uri, $options);
            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $e) {
            throw $e;
        }
    }

    /**
     * PUT请求
     *
     * @param string $uri
     * @param array $data
     * @return array
     * @throws GuzzleException
     */
    public function put(string $uri, array $data = []): array
    {
        try {
            $response = $this->client->put($uri, [
                'json' => $data,
                'headers' => $this->headers
            ]);
            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $e) {
            throw $e;
        }
    }

    /**
     * DELETE请求
     *
     * @param string $uri
     * @param array $data
     * @return array
     * @throws GuzzleException
     */
    public function delete(string $uri, array $data = []): array
    {
        try {
            $response = $this->client->delete($uri, [
                'json' => $data,
                'headers' => $this->headers
            ]);
            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $e) {
            throw $e;
        }
    }
}
