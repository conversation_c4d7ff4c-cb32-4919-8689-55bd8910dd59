<?php

namespace App\Services\Org;

use App\Exceptions\ServiceException;
use App\Jobs\CourseUpdateJob;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseProgress;
use App\Models\Cms\ContentCourseSection;
use App\Models\Org\Course;
use App\Models\Org\CourseSub;
use App\Models\Org\EnrollCourse;
use App\Models\Org\Enrollment;
use App\Models\Org\OrgClass;
use App\Services\Cms\ContentCourseProgressService;
use App\Services\Cms\ContentCourseService;
use Illuminate\Database\Eloquent\Collection;

class CourseSubService
{
    /**
     * 获取章节和课时资源ID
     *
     * @param int $orgId 机构ID
     * @param int $contentId 课程ID
     * @return array
     */
    public static function getResourceIds(int $orgId, int $contentId): array
    {
        $courseSubs = CourseSub::query()
            ->select(['type', 'resource_id'])
            ->where('org_id', $orgId)
            ->where('course_id', $contentId)
            ->get();

        $chapterIds = $courseSubs->where('type', CourseSub::TYPE_CHAPTER)->pluck('resource_id')->toArray();
        $sectionIds = $courseSubs->where('type', CourseSub::TYPE_SECTION)->pluck('resource_id')->toArray();

        return [$chapterIds, $sectionIds];
    }

    public static function operate(int $orgId, int $courseId, string $type, int $resourceId, string $operate): void
    {
        if ($operate == 'open') {
            CourseSub::query()->firstOrCreate([
                'org_id' => $orgId,
                'type' => $type,
                'resource_id' => $resourceId,
            ], [
                'course_id' => $courseId
            ]);
        } else {
            CourseSub::query()
                ->where('org_id', $orgId)
                ->where('type', $type)
                ->where('resource_id', $resourceId)
                ->delete();
        }

        CourseUpdateJob::dispatch($courseId, $orgId);
    }

    /**
     * 机构课程章|节数量
     *
     * @param int $orgId 机构ID
     * @param int $courseId 课程ID
     * @param string $type 类型
     * @return int
     */
    public static function orgCourseTypeCount(int $orgId, int $courseId, string $type): int
    {
        return CourseSub::query()
            ->where('org_id', $orgId)
            ->where('course_id', $courseId)
            ->where('type', $type)
            ->count();
    }

    /**
     * 更新学员学习时长
     *
     * @param int $orgId
     * @param int $contentId 课程id
     * @param int $userId
     * @param Enrollment $enrollment 学员分配记录
     * @return void
     */
    public static function updateLearnedDuration(int $orgId, int $contentId, int $userId, Enrollment $enrollment): void
    {
        $chapterSections = CourseSub::getResourceTree($orgId, $contentId);

        if ($enrollment->type == Enrollment::TYPE_COURSE_PACK && $chapterSections->isEmpty()) {
            $chapterSections = ContentCourseChapter::getCoursesTree([$contentId])->get($contentId);
        }

        if ($chapterSections->isEmpty()) {
            return;
        }

        /** @var Collection<ContentCourseProgress> $progresses */
        $progresses = ContentCourseProgressService::getCourseProgress($userId, $contentId, $orgId, $enrollment->id);
        if ($progresses->isEmpty()) {
            return;
        }

        $learnedDuration = 0;

        foreach ($chapterSections as $chapter) {
            foreach ($chapter->sections as $section) {
                /** @var ContentCourseProgress|null $progress */
                $progress = $progresses->get($section->id);
                if (!$progress) {
                    continue;
                }

                if ($progress->finished) {
                    $learnedDuration += $section->duration;
                } else {
                    $learnedDuration += $progress->duration >= $section->duration ? $section->duration - 1 : $progress->duration;
                }
            }
        }

        /** @var EnrollCourse $enrollCourse */
        $enrollCourse = EnrollCourse::query()->firstOrCreate([
            'org_id' => $orgId,
            'enroll_id' => $enrollment->id,
            'course_id' => $contentId,
        ], [
            'learned_duration' => 0,
            'learn_finished' => false,
        ]);

        $enrollCourse->learned_duration = $learnedDuration;

        if (!$enrollCourse->learn_finished) {
            $orgEffectiveHour = Course::query()
                ->where('org_id', $orgId)
                ->where('course_id', $contentId)
                ->select('hour')
                ->value('hour');

            if (!isset($orgEffectiveHour)) {
                $orgEffectiveHour = ContentCourseService::getCoursesHour([$contentId])[$contentId] ?? 0;
            }

            $contentCourse = ContentCourse::query()->where('content_id', $contentId)->first();

            if ($contentCourse->studyHour($learnedDuration, false) >= $orgEffectiveHour) {
                $enrollCourse->learn_finished = true;
            }
        }

        $enrollCourse->save();

        if ($enrollment->type == Enrollment::TYPE_COURSE_PACK) {
            // 查询所有子课程
            $coursePackLearnedDuration = EnrollCourse::query()
                ->where('org_id', $orgId)
                ->where('enroll_id', $enrollment->id)
                ->sum('learned_duration');

            $coursePackLearnedFinished = EnrollCourse::query()
                ->where('org_id', $orgId)
                ->where('enroll_id', $enrollment->id)
                ->where('learn_finished', 0)
                ->exists();

            $enrollment->learned_duration = $coursePackLearnedDuration;
            $enrollment->learn_finished = !$coursePackLearnedFinished;
        } elseif ($enrollment->type == Enrollment::TYPE_COURSE) {
            $enrollment->learned_duration = $enrollCourse->learned_duration;
            $enrollment->learn_finished = $enrollCourse->learn_finished;

            // 报名完成: 没有考试的班 或 考试已完成且通过
            if ($enrollment->learn_finished && $enrollment->classroom
                && ($enrollment->classroom->exam_enabled == 0 || $enrollment->exam_passed == 1)) 
            {
                $enrollment->status = Enrollment::STATUS_COMPLETED;
            }
        }

        $enrollment->save();

        if ($enrollment->class_id) {
            $learnFinishedCount = Enrollment::query()
                ->where('class_id', $enrollment->class_id)
                ->where('learn_finished', true)
                ->count();

            OrgClass::query()
                ->where('id', $enrollment->class_id)
                ->update(['total_course_finished' => $learnFinishedCount]);
        }
    }
}
