<?php

namespace App\Services\Org;

use App\Exceptions\ServiceException;
use App\Models\Order\Order;
use App\Models\Order\Payment;
use App\Models\Order\Refund;
use App\Models\Org\Enroll;
use App\Models\Org\EnrollOperateRecord;
use App\Services\Common\PaymentService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HigherOrderWhenProxy;

/**
 * 机构报名服务
 */
class EnrollService
{

    /**
     * 保存报名信息
     * @param array $params
     * @return Enroll
     */
    public function create(array $params): Enroll
    {
        try {
            if (empty($params['org_id'])) {
                throw new \InvalidArgumentException('org_id is required');
            }
            if (empty($params['user_id'])) {
                throw new \InvalidArgumentException('user_id is required');
            }
            if (empty($params['student_id'])) {
                throw new \InvalidArgumentException('student_id is required');
            }
            if (empty($params['enroll_config_id'])) {
                throw new \InvalidArgumentException('enroll_config_id is required');
            }
            $enrollConfig = app(EnrollConfigService::class)->getEnrollConfigById($params['enroll_config_id']);
            if (!$enrollConfig) {
                throw new ServiceException('报名配置不存在');
            }
            $enroll = Enroll::query()->firstOrCreate([
                'org_id' => $params['org_id'],
                'user_id' => $params['user_id'],
                'student_id' => $params['student_id'],
                'enroll_config_id' => $params['enroll_config_id'],
            ], $params);
            if ($enroll->wasRecentlyCreated) {
                // 记录操作
                app(EnrollOperateRecordService::class)->create($enroll->id, EnrollOperateRecord::TYPE_SUBMIT, 0, '用户提交报名信息');
            } else {
                if ($enroll->status == Enroll::STATUS_REJECTED) {
                    // 驳回之后再次提交，需要重新审核
                    $enroll->status = Enroll::STATUS_PENDING_REVIEW;
                    $enroll->save();
                    app(EnrollOperateRecordService::class)->create($enroll->id, EnrollOperateRecord::TYPE_SUBMIT, 0, '驳回修改之后，用户再次提交报名信息');
                }
                if ($enroll->status == Enroll::STATUS_REFUNDED) {
                    // 退款成功之后再次提交，需要重新审核
                    $enroll->status = Enroll::STATUS_PENDING_REVIEW;
                    $enroll->save();
                    app(EnrollOperateRecordService::class)->create($enroll->id, EnrollOperateRecord::TYPE_SUBMIT, 0, '退款成功之后，用户再次提交报名信息');
                }
            }

            return $enroll;
        } catch (\Throwable $e) {
            logger()->error("保存报名失败", [
                'params' => $params,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("保存报名失败");
        }
    }

    /**
     * 获取机构报名列表
     * @param int $orgId
     * @param array $filter
     * @return Builder|HigherOrderWhenProxy
     */
    public function getEnrollListByOrgId(int $orgId, array $filter = []): Builder|HigherOrderWhenProxy
    {
        try {
            if (empty($orgId)) {
                throw new \InvalidArgumentException('org_id is required');
            }
            return Enroll::query()
                ->with(['enrollConfig:id,title,amount', 'student:id,name,phone,id_card_number'])
                ->where('org_id', $orgId)
                ->when(!empty($filter['status']), fn($query) => $query->where('status', $filter['status']))
                ->when(!empty($filter['title']), fn($query) => $query->whereHas('enrollConfig', fn($q) => $q->where('title', 'like', '%' . $filter['title'] . '%')))
                ->when(
                    !empty($filter['name']) || !empty($filter['phone']) || !empty($filter['id_card_number']),
                    fn($query) => $query->whereHas('student', function ($q) use ($filter) {
                        $q->when(!empty($filter['name']), fn($sq) => $sq->where('name', $filter['name']))
                            ->when(!empty($filter['phone']), fn($sq) => $sq->where('phone', $filter['phone']))
                            ->when(!empty($filter['id_card_number']), fn($sq) => $sq->where('id_card_number', $filter['id_card_number']));
                    })
                )
                ->orderByDesc('id');
        } catch (\Throwable $e) {
            logger()->error("获取报名列表失败", [
                'org_id' => $orgId,
                'filter' => $filter,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取报名列表失败");
        }
    }


    /**
     * 获取用户报名列表
     * @param int $orgId
     * @param int $userId
     * @return array
     */
    public function getEnrollListByOrgIdAndUserId(int $orgId, int $userId): array
    {
        try {
            if (empty($orgId)) {
                throw new \InvalidArgumentException('org_id is required');
            }
            if (empty($userId)) {
                throw new \InvalidArgumentException('user_id is required');
            }

            $baseUrl = 'https://hgb-pc.shiwusuo100.com/';
            if (app()->isLocal()) {
                $baseUrl = 'http://localhost:5182/';
            } elseif (app()->environment('testing')) {
                $baseUrl = 'https://hgb-pc.test.pp.cc/';
            }
            return Enroll::query()
                ->with(['enrollConfig', 'operateRecords', 'order'])
                ->where('org_id', $orgId)
                ->where('user_id', $userId)
                ->orderByDesc('id')
                ->get()
                ->map(function (Enroll $enroll)  use ($baseUrl) {
                    $rejectReason = '';
                    $applyUrl = '';
                    if ($enroll->status == Enroll::STATUS_REJECTED) {
                        $remark = $enroll->operateRecords->where('type', EnrollOperateRecord::TYPE_REVIEW)->last()?->remark ?? '';
                        $rejectReason = str_replace("审核结论：驳回<br/>", "驳回原因：", $remark);
                        $applyUrl = $baseUrl . $enroll->org->sid . '/apply?org_enroll_config_id=' . $enroll->enrollConfig->id . '&again=1';
                    }

                    return [
                        'id' => $enroll->id,
                        'title' => $enroll->enrollConfig?->title ?? '未知课程',
                        'status' => $enroll->status,
                        'status_label' => $enroll->status_label,
                        'amount' => $enroll->order?->total_amount ?? $enroll->enrollConfig?->amount ?? '-',
                        'reject_reason' => $rejectReason,
                        'apply_url' => $applyUrl,
                    ];
                })
                ->toArray();

        } catch (\Throwable $e) {
            logger()->error("获取用户报名列表失败", [
                'org_id' => $orgId,
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return [];
        }
    }

    /**
     * 获取报名信息
     * @param int $id
     * @return Enroll
     */
    public function getEnrollById(int $id): Enroll
    {
        try {
            if (empty($id)) {
                throw new \InvalidArgumentException('id is required');
            }
            return Enroll::query()
                ->with(['enrollConfig', 'student', 'operateRecords', 'order'])
                ->find($id);
        } catch (\Throwable $e) {
            logger()->error("获取报名信息失败", [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取报名信息失败");
        }
    }


    /**
     * 报名记录审核
     * @param int $id
     * @param int $status
     * @param string $remark
     * @return void
     */
    public function audit(int $id, int $status, string $remark = ''): void
    {
        // 先处理非退款的审核逻辑
        $refundData = null;

        DB::transaction(function () use ($id, $status, $remark, &$refundData) {
            $enroll = $this->getEnrollById($id);
            $enrollId = $enroll->id;
            $adminId = auth('org')->user()->id;

            // 审核报名信息
            if ($enroll->status === Enroll::STATUS_PENDING_REVIEW) {
                $enroll->status = $status == Enroll::AUDIT_STATUS_AGREE
                    ? Enroll::STATUS_PENDING_PAYMENT
                    : Enroll::STATUS_REJECTED;
                $enroll->save();

                $recordRemark = $status == Enroll::AUDIT_STATUS_AGREE
                    ? '审核结论：通过'
                    : "审核结论：驳回<br/>{$remark}";

                app(EnrollOperateRecordService::class)->create(
                    $enrollId,
                    EnrollOperateRecord::TYPE_REVIEW,
                    $adminId,
                    $recordRemark
                );
            }

            // 审核退款信息
            if ($enroll->status === Enroll::STATUS_REFUNDING) {
                $isAgree = $status == Enroll::AUDIT_STATUS_AGREE;

                $recordRemark = $isAgree
                    ? '审核结论：通过'
                    : "审核结论：驳回<br/>{$remark}";

                app(EnrollOperateRecordService::class)->create(
                    $enrollId,
                    EnrollOperateRecord::TYPE_REFUND_REVIEW,
                    $adminId,
                    $recordRemark
                );

                if ($isAgree) {
                    $enroll->load(['order']);
                    /** @var Order $order */
                    $order = $enroll->order;

                    if (!$order || !$order->order_no) {
                        throw new \Exception('订单信息异常，无法发起退款');
                    }

                    $subMchId = $order->sub_mch_id;
                    if (empty($subMchId)) {
                        throw new \Exception('缺少子商户号，无法发起退款');
                    }

                    // 生成退款单号
                    $outRefundNo = 'refund_' . $order->id . '_' . time();

                    // 创建退款记录
                    $refund = Refund::create([
                        'user_id' => $order->user_id,
                        'order_id' => $order->id,
                        'payment_id' => $order->payment_id,
                        'refund_no' => $outRefundNo,
                        'refund_amount' => $order->payment_amount,
                        'status' => 0, // 退款中
                        'remark' => $remark ?: '用户申请退款审核通过',
                    ]);

                    // 更新订单状态为已发起退款
                    $order->status = Order::STATUS_REF_PEN;
                    $order->save();

                    // 准备退款数据，在事务外执行
                    $refundData = [
                        'enroll_id' => $enrollId,
                        'order' => $order,
                        'refund_no' => $outRefundNo,
                        'sub_mch_id' => $subMchId,
                    ];
                } else {
                    // 退款审核驳回
                    $enroll->status = Enroll::STATUS_REFUND_CLOSED;
                    $enroll->save();

                    app(EnrollOperateRecordService::class)->create(
                        $enrollId,
                        EnrollOperateRecord::TYPE_CANCEL_REFUND,
                        $adminId,
                    );
                }
            }
        });

        // 在事务外处理微信退款
        if ($refundData) {
            $this->processWechatRefund($refundData);
        }
    }

    /**
     * 处理微信退款（在事务外执行）
     * @param array $refundData
     * @return void
     */
    private function processWechatRefund(array $refundData): void
    {
        try {
            $order = $refundData['order'];
            $outRefundNo = $refundData['refund_no'];
            $subMchId = $refundData['sub_mch_id'];
            $enrollId = $refundData['enroll_id'];

            // 发起微信退款
            $refundResponse = PaymentService::wechatRefund([
                'out_trade_no' => $order->order_no,
                'out_refund_no' => $outRefundNo,
                'amount' => [
                    'refund' => intval($order->payment_amount * 100),  // 单位：分
                    'total' => intval($order->payment_amount * 100),
                    'currency' => 'CNY'
                ],
                'reason' => '用户申请退款（'.$order->title.'）审核通过'
            ], $subMchId);

            if (in_array($refundResponse->status ?? '', ['SUCCESS', 'PROCESSING'])) {
                // 微信退款发起成功，更新相关状态
                DB::transaction(function () use ($order, $enrollId) {
                    // 更新订单状态为退款处理中
                    $order->status = Order::STATUS_REF_HAVE;
                    $order->save();
                    $order->payment->status = Payment::STATUS_REF_HAVE;
                    $order->payment->save();

                    // 更新报名状态为退款成功, 走到这里用户的钱已退回；微信退款回调里面只处理整个退款留痕状态变更
                    $enroll = Enroll::find($enrollId);
                    if ($enroll) {
                        $enroll->status = Enroll::STATUS_REFUNDED;
                        $enroll->save();
                    }
                });

                logger()->info('微信退款发起成功', [
                    'order_no' => $order->order_no,
                    'refund_no' => $outRefundNo,
                    'response' => $refundResponse->toArray()
                ]);
            } else {
                // 微信退款失败，回滚状态
                $this->handleRefundFailure($order, $enrollId, $outRefundNo, $refundResponse);
            }
        } catch (\Throwable $e) {
            // 微信退款异常，回滚状态
            $this->handleRefundFailure($order, $enrollId, $outRefundNo, null, $e);
        }
    }

    /**
     * 处理退款失败的情况
     * @param Order $order
     * @param int $enrollId
     * @param string $outRefundNo
     * @param mixed|null $refundResponse
     * @param \Throwable|null $exception
     * @return void
     */
    private function handleRefundFailure(Order $order, int $enrollId, string $outRefundNo, mixed $refundResponse = null, ?\Throwable $exception = null): void
    {
        DB::transaction(function () use ($order, $enrollId, $outRefundNo) {
            // 回滚订单状态
            $order->status = Order::STATUS_PAID;
            $order->save();
            $order->payment->status = Payment::STATUS_PAID;
            $order->payment->save();

            // 回滚报名状态
            $enroll = Enroll::find($enrollId);
            if ($enroll) {
                $enroll->status = Enroll::STATUS_REFUND_CLOSED;
                $enroll->save();
            }

            // 删除退款记录
            Refund::where('refund_no', $outRefundNo)->delete();

            // 记录退款失败操作
            app(EnrollOperateRecordService::class)->create(
                $enrollId,
                EnrollOperateRecord::TYPE_CANCEL_REFUND,
                auth('org')->user()->id,
                '微信退款发起失败，已回滚状态'
            );
        });

        // 记录错误日志
        $errorData = [
            'order_no' => $order->order_no,
            'refund_no' => $outRefundNo,
        ];

        if ($exception) {
            $errorData['exception'] = $exception->getMessage();
            logger()->error('微信退款异常', $errorData);
        } else {
            $errorData['response'] = $refundResponse ? $refundResponse->toArray() : null;
            logger()->error('微信退款失败', $errorData);
        }

        // 抛出异常，让上层知道退款失败
        throw new \Exception('微信退款失败，请稍后重试');
    }
}
