<?php

namespace App\Services\Org;

use App\Models\Org\OrgOrder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HigherOrderWhenProxy;

class OrgOrderService
{
    /**
     * 获取机构下的订单
     * @param int $orgId
     * @param array $filter
     * @return Builder|HigherOrderWhenProxy
     */
    public function getOrderByOrgID(int $orgId, array $filter = []): Builder|HigherOrderWhenProxy
    {
        return OrgOrder::query()
            ->where('org_id', $orgId)
            ->when(isset($filter['id']), fn($query) => $query->where('id', $filter['id']))
            ->when(isset($filter['name']), fn($query) => $query->where('name', 'like', '%' . $filter['name'] . '%'))
            ->when(isset($filter['phone']), fn($query) => $query->where('phone', $filter['phone']))
            ->when(isset($filter['payment_channel']), fn($query) => $query->where('payment_channel', $filter['payment_channel']))
            ->when(isset($filter['business_type']), fn($query) => $query->where('business_type', $filter['business_type']))
            ->when(isset($filter['order_no']), fn($query) => $query->where('order_no', 'like', '%' . $filter['order_no'] . '%'))
            ->when(isset($filter['transaction_no']), fn($query) => $query->where('transaction_no', 'like', '%' . $filter['transaction_no'] . '%'))
            ->when(isset($filter['status']), fn($query) => $query->where('status', $filter['status']))
            ->when(isset($filter['payment_at']), fn($query) => $query->whereBetween('payment_at', $filter['payment_at']))
            ->when(isset($filter['created_at']), fn($query) => $query->whereBetween('created_at', $filter['created_at']))
            ->orderByDesc('id');
    }
}
