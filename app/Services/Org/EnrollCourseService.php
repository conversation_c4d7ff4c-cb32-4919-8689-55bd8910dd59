<?php

namespace App\Services\Org;

use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePackList;
use App\Models\Org\Course;
use App\Models\Org\EnrollCourse;
use App\Models\Org\Enrollment;

class EnrollCourseService
{
    public static function batchSave(int $orgId, array $enrollmentIds, string $subjectType, int $subjectId): void
    {
        if ($subjectType == Enrollment::TYPE_COURSE) {
            $courseIds[] = $subjectId;
        } elseif ($subjectType == Enrollment::TYPE_COURSE_PACK) {
            $courseIds = ContentCoursePackList::query()->where('content_id', $subjectId)->pluck('course_id')->toArray();
        } else {
            return;
        }

        // 分批保存
        $chunk = array_chunk($enrollmentIds, 20);

        foreach ($chunk as $data) {
            foreach ($data as $enrollmentId) {
                $now = now();
                $insert = [];

                foreach ($courseIds as $courseId) {
                    $insert[] = [
                        'org_id' => $orgId,
                        'enroll_id' => $enrollmentId,
                        'course_id' => $courseId,
                        'learned_duration' => 0,
                        'learn_finished' => false,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }

                EnrollCourse::query()->insert($insert);
            }
        }
    }

    public static function create(Enrollment $enrollment): void
    {
        $courseIds = [];

        if ($enrollment->type == Enrollment::TYPE_COURSE) {
            $courseIds[] = $enrollment->resource_id;
        } elseif ($enrollment->type == Enrollment::TYPE_COURSE_PACK) {
            $courseIds = ContentCoursePackList::query()
                ->where('content_id', $enrollment->resource_id)
                ->pluck('course_id')
                ->toArray();

            $insertedCourseIds = EnrollCourse::query()
                ->where('enroll_id', $enrollment->id)
                ->where('org_id', $enrollment->org_id)
                ->pluck('course_id')
                ->toArray();

            $courseIds = array_diff($courseIds, $insertedCourseIds);
        }

        if (!empty($courseIds)) {
            $now = now();
            $insert = [];

            foreach ($courseIds as $courseId) {
                $insert[] = [
                    'org_id' => $enrollment->org_id,
                    'enroll_id' => $enrollment->id,
                    'course_id' => $courseId,
                    'learned_duration' => 0,
                    'learn_finished' => false,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            EnrollCourse::query()->insert($insert);
        }
    }

    public static function remove(Enrollment $enrollment): void
    {
        EnrollCourse::query()->where('enroll_id', $enrollment->id)->delete();
    }

    /**
     * 获取学习时长
     * @param Enrollment $enrollment
     * @return float
     */
    public static function getStudyHour(Enrollment $enrollment): float
    {
        $studyHour = 0;

        if ($enrollment->resource) {
            if ($enrollment->type == Enrollment::TYPE_COURSE) {
                /** @var ContentCourse $contentCourse */
                $contentCourse = $enrollment->resource;
                $studyHour = $contentCourse->studyHour($enrollment->learned_duration, false);
            } elseif ($enrollment->type == Enrollment::TYPE_COURSE_PACK) {
                $contentCourses = $enrollment->resource->contentCourses;
                $enrollCourses = EnrollCourse::query()
                    ->where('enroll_id', $enrollment->id)
                    ->get()
                    ->keyBy('course_id');

                foreach ($contentCourses as $contentCourse) {
                    $studyHour += $contentCourse->studyHour($enrollCourses->get($contentCourse->content_id)->learned_duration ?? 0, false);
                }
            }
        }

        return $studyHour;
    }

    /**
     * 获取课时
     * @param Enrollment $enroll
     * @return int
     */
    public static function getHour(Enrollment $enroll): int
    {
        $hour = 0;

        if ($enroll->type == Enrollment::TYPE_COURSE) {
            $hour = Course::publicFields()
                ->where('org_id', $enroll->org_id)
                ->where('course_id', $enroll->resource_id)
                ->value('hour');
        } elseif ($enroll->type == Enrollment::TYPE_COURSE_PACK) {
            $hour = CoursePackService::calCoursePackHour($enroll->org_id, $enroll->resource_id);
        }

        return $hour;
    }

}
