<?php

namespace App\Services\Org;

use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePackList;
use App\Models\Org\CoursePack;

class CoursePackService
{
    /**
     * 添加机构课程
     *
     * @param int $orgId
     * @param array $coursePacks
     * @return void
     */
    public static function add(int $orgId, array $coursePacks = [])
    {
        // 批量获取所有课程的总时长
        foreach ($coursePacks as $coursePackId => $coursePack) {
            $model = new CoursePack();
            $model->org_id = $orgId;
            $model->course_pack_id = $coursePackId;
            $model->status = CoursePack::STATUS_VISIBLE;
            $model->price_original = $coursePack['price'] ?? 0;
            $model->price_sell = $coursePack['price'] ?? 0;

            list($hour, $duration) = CourseService::addByPackId($orgId, $coursePackId);

            $model->hour = $hour;
            $model->duration = $duration;
            $model->save();
        }
    }

    public static function getOrgCoursePacks(int $orgId): array
    {
        $list = CoursePack::query()
            ->with(['contentCoursePack', 'contentCoursePack.content', 'contentCoursePack.topic' => function ($query) {
                $query->select(['id', 'name']);
            }])
            ->where('org_id', $orgId)
            ->where('status', CoursePack::STATUS_VISIBLE)
            ->get();

        $courses = [];

        /** @var CoursePack $item */
        foreach ($list as $item) {
            $topic = $item->contentCoursePack->topic;
            if ($topic) {
                $topic->setHidden([]);
            }

            $courses[] = [
                'id' => $item->id,
                'course_pack_id' => $item->course_pack_id,
                'name' => $item->contentCoursePack->content->title,
                'price_original' => $item->price_original,
                'price_sell' => $item->price_sell,
                'topic' => $topic,
            ];
        }

        return $courses;
    }

    /**
     * 计算课程包课时
     *
     * @param int $orgId
     * @param int $coursePackId
     * @return int
     */
    public static function calCoursePackHour(int $orgId, int $coursePackId)
    {
        if ($orgId > 0) {
            return CoursePack::query()
                ->where('org_id', $orgId)
                ->where('course_pack_id', $coursePackId)
                ->value('hour') ?? 0;
        }

        $courseIds = ContentCoursePackList::query()
            ->where('content_id', $coursePackId)
            ->pluck('course_id')
            ->toArray();

        return ContentCourse::query()
            ->select('hour', 'content_id')
            ->whereIn('content_id', $courseIds)
            ->sum('hour');
    }
    
    /**
     * 计算课程包有效时长
     *
     * @param int $orgId
     * @param int $coursePackId
     * @return int
     */
    public static function calCoursePackDuration(int $orgId, int $coursePackId)
    {
        if ($orgId > 0) {
            return CoursePack::query()
                ->where('org_id', $orgId)
                ->where('course_pack_id', $coursePackId)
                ->value('duration') ?? 0;
        }

        $courseIds = ContentCoursePackList::query()
            ->where('content_id', $coursePackId)
            ->pluck('course_id')
            ->toArray();

        return ContentCourse::query()
            ->select('duration', 'content_id')
            ->whereIn('content_id', $courseIds)
            ->sum('duration');
    }
}