<?php

namespace App\Services\Org;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Org;
use App\Models\Org\Enrollment;
use App\Models\Org\LearnCapture;
use App\Services\Common\AttachmentService;
use App\Services\WechatService;
use Illuminate\Support\Facades\Cache;

/**
 * 抓拍服务
 */
class CaptureService
{

    /**
     * 生成抓拍码（生成的码半小时内有效，或者相同用户重复生成后原先的码失效)
     *
     * @param int $enrollId
     * @param int $courseId
     * @param int $sectionId
     * @return string
     */
    public static function makeCode($enrollId, $courseId, $sectionId)
    {
        /** @var Enrollment $enroll */
        $enroll = Enrollment::query()
            ->select(['id', 'user_id', 'org_id', 'type', 'resource_id', 'status'])
            ->where('id', $enrollId)
            ->first();

        if (!$enroll) {
            throw new \InvalidArgumentException('Unknown enroll id.');
        }

        if ($enroll->status == Enrollment::STATUS_PENDING) {
            throw new ServiceException('暂不能发起抓拍，该学员还未开课。');
        }

        if ($enroll->type == Enrollment::TYPE_COURSE) {
            if ($enroll->resource_id != $courseId) {
                throw new ServiceException('无法发起抓拍，学员课程不一致。');
            }
        } elseif ($enroll->type == Enrollment::TYPE_COURSE_PACK) {
            if (!Org\EnrollCourse::query()->where('enroll_id', $enrollId)->where('course_id', $courseId)->exists()) {
                throw new ServiceException('无法发起抓拍，学员课程包无该课程。');
            }
        } else {
            throw new ServiceException('不支持该类型内容进行抓拍。');
        }

        //作废以前的 Code
        $enrollKey = self::enrollKey($enrollId);
        $userCode = Cache::get($enrollKey);
        if ($userCode) {
            Cache::forget(self::codeKey($userCode['code']));
        }

        $code = WechatService::randomScene('capture');

        //用于验证该报名最新的 code
        Cache::put($enrollKey, [
            'code' => $code
        ], 1800);

        //该 code 关联的信息
        Cache::put(self::codeKey($code), [
            'enroll' => $enroll->setHidden([])->toArray(),
            'courseId' => $courseId,
            'sectionId' => $sectionId,
            'uploaded' => false
        ], 1800);

        return $code;
    }

    /**
     * 验证指定的抓拍码是否有效
     *
     * @param string $scene
     * @param int $userId
     * @return bool
     */
    public static function verifyCode($scene, $userId)
    {
        $codeKey = self::codeKey($scene);
        $codeInfo = Cache::get($codeKey);
        return $codeInfo && $codeInfo['enroll']['user_id'] == $userId;
    }

    /**
     * 检查抓拍状态
     *
     * @param string $scene
     * @return bool 是否已上传抓拍
     */
    public static function check($scene)
    {
        $codeKey = self::codeKey($scene);
        $codeInfo = Cache::get($codeKey);

        if (!$codeInfo) {
            throw new ServiceException('抓码请求已失效，请重试！');
        }

        return $codeInfo['uploaded'];
    }

    /**
     * 通过码获取机构信息
     *
     * @param string $scene
     * @return Org
     */
    public static function getOrg(string $scene): Org
    {
        $codeKey = self::codeKey($scene);
        $codeInfo = Cache::get($codeKey);

        if (!$codeInfo) {
            throw new ServiceException('抓码请求已失效，请重试！');
        }

        /** @var Org $org */
        $org = Org::query()->find($codeInfo['enroll']['org_id']);
        if (!$org) {
            throw new ServiceException('机构信息不存在！');
        }

        return $org;
    }

    /**
     * 保存抓拍
     *
     * @param string $scene
     * @param string $uploadKey 上传的临时文件Key
     * @return void
     */
    public static function save($scene, $uploadKey)
    {
        $codeKey = self::codeKey($scene);
        $codeInfo = Cache::get($codeKey);

        if (!$codeInfo) {
            throw new ServiceException('抓码请求已失效，请重试！');
        }

        /** @var Enrollment $enrollment */
        $enrollment = Enrollment::query()->with(['classroom', 'student'])->find($codeInfo['enroll']['id']);
        if (!$enrollment) {
            throw new ServiceException('报名信息不存在！');
        }

        if (!$enrollment->classroom) {
            throw new ServiceException('班级信息不存在！');
        }

        $captureCount = LearnCaptureService::captureCount($enrollment, $enrollment->resource_id, $codeInfo['sectionId']);
        if ($captureCount >= $enrollment->classroom->face_capture_count) {
            throw new ServiceException('抓拍次数已达标，无需重复抓拍！');
        }

        $basePhotoUrl = $enrollment->student->getPhotoUrlAttribute();
        $type = 'LIVE';
        
        if (empty($basePhotoUrl)) {
            $basePhotoUrl = $enrollment->student->getIdCardFrontUrlAttribute();
            $type = 'CERT';

            if (empty($basePhotoUrl)) {
                throw new ServiceException('无法对比是否本人，请先上传身份证或证件照');
            }
        }

        $photoUrl = AttachmentService::url($uploadKey);
        $match = EnrollmentService::faceMatch($basePhotoUrl, $photoUrl, '', $type);

        if (!$match['pass']) {
            throw new ServiceException('抓拍照片身份验证失败');
        }

        $capture = new LearnCapture();
        $capture->org_id = $codeInfo['enroll']['org_id'];
        $capture->course_id = $codeInfo['courseId'];
        $capture->section_id = $codeInfo['sectionId'];
        $capture->enroll_id = $codeInfo['enroll']['id'];
        $capture->user_id = $codeInfo['enroll']['user_id'];
        $capture->photo = '';
        $capture->save();

        $file = AttachmentService::store($uploadKey, 'learn-captures', BusinessType::LearnCapture, $capture->id);

        $capture->photo = $file->path;
        $capture->save();

        $codeInfo['uploaded'] = true;
        Cache::put($codeKey, $codeInfo, 1800);
    }

    protected static function enrollKey($enrollId)
    {
        return 'capture:enroll:'.$enrollId;
    }

    protected static function codeKey($scene)
    {
        return 'capture:code:'.$scene;
    }

}
