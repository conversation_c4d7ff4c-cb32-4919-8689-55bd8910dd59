<?php

namespace App\Services\Org\Export;

use App\Models\Cms\Category;
use App\Models\Cms\ContentCourseProgress;
use App\Models\User;
use App\Models\User\UserOwnContent;
use App\Services\Cms\ContentCourseProgressService;
use App\Services\Cms\ContentCourseSectionService;
use Generator;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromGenerator;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CourseProgressExportService implements FromGenerator, WithHeadings
{
    use Exportable;

    public function __construct(public int $contentId = 0, public string $phone = '', public string $nickname = '',
                                public int $progressStatus = 0, public int $orgId = 0)
    {}

    /**
     * 设置表头
     *
     * @return string[]
     */
    public function headings(): array
    {
        return [
            '手机号码',
            '昵称',
            '所属机构',
            '课程名称',
            '学习状态',
            '进度',
            '购买时间'
        ];
    }

    /**
     * 使用生成器获取数据
     *
     * @return Generator
     */
    public function generator(): Generator
    {
        $builder = UserOwnContent::query()
            ->with(['user' => fn ($q) => $q->with('org')])
            ->where('content_id', $this->contentId)
            ->where('classify', Category::CLASSIFY_COURSE);

        if (!empty($this->phone)) {
            $userId = User::query()->where('phone', $this->phone)->value('id');
            $builder->where('user_id', $userId ?? 0);
        }

        if (!empty($this->nickname)) {
            $userIds = User::query()
                ->where('nickname', 'like', "%$this->nickname%")
                ->pluck('id')
                ->toArray();
            $builder->whereIn('user_id', $userIds);
        }

        if (!empty($this->orgId)) {
            $builder->whereHas('user', fn($q) => $q->whereHas('org', fn ($q) => $q->where('id', $this->orgId)));
        }

        $list = $builder->orderByDesc('created_at')->get();
        if ($list->isEmpty()) {
            return yield [];
        }

        $userIds = $list->pluck('user_id')->toArray();

        $progresses = ContentCourseProgress::query()
            ->with('section', fn ($q) => $q->with('video'))
            ->select(['user_id', 'duration', 'pos', 'finished', 'section_id'])
            ->where('content_id', $this->contentId)
            ->where('org_id', 0)
            ->where('enroll_id', 0)
            ->whereIn('user_id', $userIds)
            ->get();
        $userProgresses = ContentCourseProgressService::getUserProgresses($progresses);
        $courseDuration = ContentCourseSectionService::getCourseDuration($this->contentId);

        // 学习状态
        if (!empty($this->progressStatus)) {
            switch ($this->progressStatus) {
                case 1:// 未学
                    $studyUserIds = $progresses->pluck('user_id')->toArray();
                    $diffUserIds = array_diff($userIds, $studyUserIds);
                    $list = $list->whereIn('user_id', $diffUserIds);
                    break;
                case 2:// 学习中
                    $userIds = [];
                    foreach ($userProgresses as $key => $progress) {
                        if ($progress['study_duration'] < $courseDuration) {
                            $userIds[] = $key;
                        }
                    }

                    $list = $list->whereIn('user_id', $userIds);
                    break;
                case 3:// 已学
                    $userIds  = [];
                    foreach ($userProgresses as $key => $progress) {
                        if ($progress['study_duration'] == $courseDuration) {
                            $userIds[] = $key;
                        }
                    }
                    $list = $list->whereIn('user_id', $userIds);
                    break;
            }
        }

        if ($list->isEmpty()) {
            return yield [];
        }

        /** @var UserOwnContent $record */
        foreach ($list as &$record) {
            $record = ContentCourseProgressService::setProgress($record, $userProgresses, $courseDuration);

            yield $this->outputData($record);
        }
    }

    public function outputData(UserOwnContent $record): array
    {
        return [
            $record->user->phone . ' ',
            $record->user->nickname,
            $record->user->org?->name,
            $record->content->title,
            ContentCourseProgress::progressLabel()[$record->progress_status],
            $record->progress,
            $record->created_at
        ];
    }
}
