<?php

namespace App\Services\Org\Export;

use App\Exceptions\ServiceException;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourseProgress;
use App\Models\Org;
use App\Models\Org\CoursePack;
use App\Models\Org\Enrollment;
use App\Services\Cms\ContentCourseProgressService;
use App\Services\Org\CourseService;
use App\Services\Org\ExportService;
use App\Services\Org\LearnCaptureService;
use Illuminate\Database\Eloquent\Collection;

/**
 * 学习记录导出处理器
 */
class HourRecordExporter implements ExporterInterface
{

    use ExportTemplateReplacer;

    public static function validateRules(): array
    {
        return [];
    }

    public function __construct(
        private readonly int $orgId,
        public int $checkedId, // 勾选的报名学员ID
        array $extra
    ){}

    public function generateDesc(): string
    {
        if ($this->checkedId == 0) {
            return '学习记录';
        }

        /** @var Enrollment $enrollment */
        $enrollment = Enrollment::query()
            ->with(['classroom' => function ($query) {
                $query->select(['id', 'name']);
            }])
            ->where('id', $this->checkedId)
            ->first();

        // 学习记录-班级名称-学生姓名
        return "学习记录-{$enrollment->classroom->name}-$enrollment->name";
    }

    public function handle(): array
    {
        /** @var Enrollment $enroll */
        $enroll = Enrollment::query()
            ->where('id', $this->checkedId)
            ->where('org_id', $this->orgId)
            ->whereIn('type', [Enrollment::TYPE_COURSE, Enrollment::TYPE_COURSE_PACK])
            ->first();

        if (!$enroll) {
            throw new ServiceException("学员 $this->checkedId 不存在。");
        }

        $student = $enroll->getStudent();

        //基础信息
        $items = [
            ValueItem::text('学号', $enroll->number),
            ValueItem::text('姓名', $student->name),
            ValueItem::text('手机号', $student->phone),
            ValueItem::text('身份证号', $student->id_card_number),
            ValueItem::image('照片', $student->photo, private: true),
            ValueItem::text('课程名称', $enroll->getResourceName() ?: '无'),
            ValueItem::text('开课时间', $enroll->started_at?->format('Y年m月d日') ?: '无')
        ];

        //报名表扩展信息
        if ($student->extra) {
            $extra = ExportService::parseExtra($enroll->org_id, $student->extra);
            $items = array_merge($items, $extra);
        }

        $progressItems = [];

        if (in_array($enroll->type, [Enrollment::TYPE_COURSE_PACK, Enrollment::TYPE_COURSE]) && $enroll->resource_id > 0) {
            // 获取课程/课程包机构配置的章节信息
            if ($enroll->type == Enrollment::TYPE_COURSE_PACK) {
                $chapterSections = CoursePack::getCoursesResourceTree($enroll->org_id, $enroll->resource_id, true);
            } else {
                $chapterSections = CourseService::getCoursesResourceTree($enroll->org_id, [$enroll->resource_id], true);
            }

            $courseIds = $chapterSections->keys()->toArray();

            /** @var Collection<ContentCourseProgress> $progresses */
            $progresses = ContentCourseProgressService::getCourseProgress($enroll->user_id, $enroll->resource_id, $enroll->org_id, $enroll->id);

            // 抓拍照片记录
            $capturePictures = LearnCaptureService::coursesCapturePhotos($enroll->id, $courseIds);

            foreach ($chapterSections as $courseId => $chapterSection) {
                $content = Content::query()->find($courseId);

                if ($enroll->type == Enrollment::TYPE_COURSE_PACK) {
                    $progressItems[] = [
                        ValueItem::text('类型', '课程'),
                        ValueItem::text('名称', $content->title),
                        ValueItem::text('课时', ''),
                        ValueItem::text('开始时间', ''),
                        ValueItem::text('结束时间', ''),
                        ValueItem::text('学习进度', ''),
                        ValueItem::list('抓拍记录', []),
                    ];
                }

                foreach ($chapterSection as $chapter) {
                    $progressItems[] = [
                        ValueItem::text('类型', '章'),
                        ValueItem::text('名称', $chapter['sn'] . ' ' . $chapter['name']),
                        ValueItem::text('课时', ''),
                        ValueItem::text('开始时间', ''),
                        ValueItem::text('结束时间', ''),
                        ValueItem::text('学习进度', ''),
                        ValueItem::list('抓拍记录', []),
                    ];

                    if (empty($chapter['sections'])) {
                        continue;
                    }

                    foreach ($chapter['sections'] as $section) {
                        /** @var ContentCourseProgress|null $progress */
                        $progress = $progresses->get($section['id']);

                        $capturePictureItems = [];
                        if (isset($capturePictures[$section['id']])) {
                            foreach ($capturePictures[$section['id']] as $capturePicture) {
                                $capturePictureItems[] = ValueItem::image('抓拍照片', $capturePicture['photo'], private: true);
                            }
                        }

                        $progressItems[] = [
                            ValueItem::text('类型', '节'),
                            ValueItem::text('名称', "　 " . $section['sn'] . ' ' . $section['name']),
                            ValueItem::text('课时', $section['hour']),
                            ValueItem::text('开始时间', $progress?->created_at->format('Y-m-d H:i:s') ?? '无'),
                            ValueItem::text('结束时间', $progress?->updated_at->format('Y-m-d H:i:s') ?? '无'),
                            ValueItem::text('学习进度', ContentCourseProgress::progressStatus($progress),),
                            ValueItem::list('抓拍记录', $capturePictureItems)
                        ];
                    }
                }
            }
        }

        $items[] = ValueItem::list('课时记录', $progressItems);

        return $this->generateFromTemplate($this->orgId, Org\Export::TYPE_HOUR_RECORD, $items);
    }
}
