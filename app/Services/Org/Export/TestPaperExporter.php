<?php

namespace App\Services\Org\Export;

use App\Exceptions\ServiceException;
use App\Models\Org\Enrollment;
use App\Services\Common\AttachmentService;
use App\Services\Topic\TrainTestService;
use Exception;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Style\Table as TableStyle;
use PhpOffice\PhpWord\SimpleType\JcTable;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\IOFactory;
use Psr\Log\LoggerInterface;

/**
 * 考试试卷导出处理器
 *
 * 该类负责将考试试卷数据导出为Word文档，并打包成ZIP文件
 * 支持批量导出多个学生的考试试卷
 */
class TestPaperExporter implements ExporterInterface
{
    /**
     * 文档样式配置
     *
     * @var array
     */
    private array $documentStyles = [];

    /**
     * 日志记录器
     *
     * @var LoggerInterface
     */
    protected LoggerInterface $logger;

    /**
     * 验证规则
     *
     * @return array 验证规则数组
     */
    public static function validateRules(): array
    {
        return [];
    }

    public function __construct(
        private readonly int $orgId,
        public int $checkedId,
        array $extra
    ){}

    /**
     * 生成导出文件描述
     *
     * @return string 文件描述
     */
    public function generateDesc(): string
    {
        // 批量下载
        if ($this->checkedId == 0) {
            return '考试试卷';
        }

        $enrollment = Enrollment::query()
            ->with(['classroom' => function ($query) {
                $query->select(['id', 'name']);
            }])
            ->where('id', $this->checkedId)
            ->first();

        // 考试试卷-班级名称-学生姓名
        return "考试试卷-{$enrollment->classroom->name}-{$enrollment->getStudent()->name}";
    }

    /**
     * 处理导出任务
     *
     * @return array [文件名, 文件路径]
     * @throws Exception 导出过程中的异常
     */
    public function handle(): array
    {
        // 初始化日志记录器
        $this->logger = Log::channel('task');

        // 获取数据
        $testInfo = $this->fetchData();

        if (empty($testInfo)) {
            $this->logger->warning('导出试卷数据为空', ['enrollment_ids' => $this->checkedId]);
            throw new ServiceException('导出试卷数据为空');
        }

        // 初始化文档样式
        $this->initStyles();

        /** 生成Word文档 */
        $phpWord = new PhpWord();

        // 设置文档属性
        $properties = $phpWord->getDocInfo();
        $properties->setCreator('机构' . $this->orgId);
        $properties->setTitle('考试试卷');
        $properties->setDescription('考试试卷导出文档');

        // 添加文档节
        $section = $phpWord->addSection();

        // 创建基本信息表格
        $table = $section->addTable($this->documentStyles['tableStyle']);
        $table->addRow(400); // 设置行高
        $table->addCell(10000, ['gridSpan' => 4])->addText(
            '考试试卷',
            $this->documentStyles['titleStyle'],
            ['alignment' => 'center', 'valign' => 'center']
        );

        // 添加学生基本信息行
        $this->addStudentInfoRows($table, $testInfo);

        // 添加试题内容（如果存在）
        if (is_array($testInfo['questions'])) {
            $this->addQuestionsToDocument($section, $testInfo['questions']);
        }

        // 生成临时文件
        list(, $tempFile) = AttachmentService::tmpFile('docx', 'test_paper_export_');
        $objWriter = IOFactory::createWriter($phpWord);
        $objWriter->save($tempFile);

        // 文件名
        $fileName = $this->generateDesc();

        return [$fileName, $tempFile];
    }

    /**
     * 初始化文档样式配置
     *
     * @return void
     */
    private function initStyles(): void
    {
        $this->documentStyles = [
            // 标题样式
            'titleStyle' => [
                'name' => '宋体',
                'size' => 16,
                'bold' => true,
                'color' => '000000'
            ],
            // 副标题样式
            'subtitleStyle' => [
                'name' => '宋体',
                'size' => 14,
                'bold' => true,
                'color' => '000000'
            ],
            // 普通文本样式
            'normalTextStyle' => [
                'name' => '宋体',
                'size' => 10,
                'color' => '000000'
            ],
            // 表头单元格样式
            'headerCellStyle' => [
                'name' => '宋体',
                'size' => 10,
                'bold' => true,
                'color' => '000000'
            ],
            // 表格样式
            'tableStyle' => new TableStyle([
                'borderSize' => 6,
                'borderColor' => '000000',
                'width' => 100,
                'unit' => TblWidth::PERCENT,
                'alignment' => JcTable::CENTER
            ])
        ];
    }

    /**
     * 添加学生基本信息行到表格
     *
     * @param Table $table 表格对象
     * @param array $testInfo 学生考试信息
     * @return void
     */
    private function addStudentInfoRows(Table $table, array $testInfo): void
    {
        // 定义要添加的信息行
        $infoRows = [
            ['学号', $testInfo['student_id'] ?? '', '姓名', $testInfo['name'] ?? ''],
            ['联系电话', $testInfo['phone'] ?? '', '身份证号', $testInfo['id_card'] ?? ''],
            ['工作单位', $testInfo['company'] ?? '', '考试课程', $testInfo['test_name'] ?? ''],
            ['考试分数', $testInfo['score'] ?? '', '是否及格', $testInfo['passed'] ?? ''],
            ['考试时间', $testInfo['test_time'] ?? '', '考试用时', $testInfo['duration'] ?? '']
        ];

        // 添加所有信息行
        foreach ($infoRows as $rowData) {
            $row = $table->addRow(400); // 设置行高
            $style = $this->documentStyles['normalTextStyle'];
            $labelStyle = array_merge($style, ['bold' => true]);
            $pStyle = ['alignment' => 'left', 'indentation' => ['left' => 60]];

            // 添加第一组标签和值
            $row->addCell(1500, ['bgColor' => 'F2F2F2'])->addText($rowData[0], $labelStyle, $pStyle);
            $row->addCell(3500)->addText($rowData[1], $style, $pStyle);

            // 添加第二组标签和值
            $row->addCell(1500, ['bgColor' => 'F2F2F2'])->addText($rowData[2], $labelStyle, $pStyle);
            $row->addCell(3500)->addText($rowData[3], $style, $pStyle);
        }
    }

    /**
     * 添加试题到文档
     *
     * @param Section $section 文档节
     * @param array $questions 试题数据
     * @return void
     */
    private function addQuestionsToDocument(Section $section, array $questions): void
    {
        // 创建问题表格
        $questionTable = $section->addTable($this->documentStyles['tableStyle']);

        // 添加试卷标题行
        $titleRow = $questionTable->addRow();
        $titleCell = $titleRow->addCell(10000, ['gridSpan' => 4]);
        $titleCell->addText(
            '答题详情',
            $this->documentStyles['subtitleStyle'],
            ['alignment' => 'center', 'valign' => 'center']
        );

        // 添加表头
        $questionTable->addRow(400);
        $questionTable->addCell(7000, ['bgColor' => 'F2F2F2'])->addText(
            '题目',
            $this->documentStyles['headerCellStyle'],
            ['alignment' => 'center', 'valign' => 'center']
        );
        $questionTable->addCell(1000, ['bgColor' => 'F2F2F2'])->addText(
            '考生答案',
            $this->documentStyles['headerCellStyle'],
            ['alignment' => 'center', 'valign' => 'center']
        );
        $questionTable->addCell(1000, ['bgColor' => 'F2F2F2'])->addText(
            '正确答案',
            $this->documentStyles['headerCellStyle'],
            ['alignment' => 'center', 'valign' => 'center']
        );
        $questionTable->addCell(1000, ['bgColor' => 'F2F2F2'])->addText(
            '是否正确',
            $this->documentStyles['headerCellStyle'],
            ['alignment' => 'center', 'valign' => 'center']
        );

        // 添加每道题目
        foreach ($questions as $index => $question) {
            // 题目行
            $questionTable->addRow();
            $questionTable->addCell(7000)->addText(
                ($index + 1) . '. ' . ($question['content'] ?? ''),
                $this->documentStyles['normalTextStyle'],
                ['alignment' => 'left', 'indentation' => ['left' => 60]]
            );

            // 添加考生答案单元格（合并单元格）
            $studentAnswerCell = $questionTable->addCell(1000, ['vMerge' => 'restart', 'valign' => 'center']);
            $studentAnswerCell->addText(
                $question['student_answer'] ?? '',
                $this->documentStyles['normalTextStyle'],
                ['alignment' => 'center']
            );

            // 添加正确答案单元格（合并单元格）
            $correctAnswerCell = $questionTable->addCell(1000, ['vMerge' => 'restart', 'valign' => 'center']);
            $correctAnswerCell->addText(
                $question['correct_answer'] ?? '',
                $this->documentStyles['normalTextStyle'],
                ['alignment' => 'center']
            );

            // 根据是否正确设置不同颜色
            $isCorrectText = $question['is_correct'] ?? '';
            $isCorrectStyle = $this->documentStyles['normalTextStyle'];

            // 正确显示绿色，错误显示红色
            if ($isCorrectText === '正确') {
                $isCorrectStyle = array_merge($this->documentStyles['normalTextStyle'], ['color' => '008000']); // 绿色
            } elseif ($isCorrectText === '错误') {
                $isCorrectStyle = array_merge($this->documentStyles['normalTextStyle'], ['color' => 'FF0000']); // 红色
            }

            // 添加是否正确单元格（合并单元格）
            $isCorrectCell = $questionTable->addCell(1000, ['vMerge' => 'restart', 'valign' => 'center']);
            $isCorrectCell->addText($isCorrectText, $isCorrectStyle, ['alignment' => 'center']);

            // 如果有选项，添加选项行
            if (!empty($question['options'])) {
                $optionsRow = $questionTable->addRow();
                $optionsCell = $optionsRow->addCell(7000);

                // 添加每个选项
                foreach ($question['options'] as $option) {
                    $optionsCell->addText(
                        $option,
                        $this->documentStyles['normalTextStyle'],
                        ['alignment' => 'left', 'indentation' => ['left' => 60]]
                    );
                }

                // 继续合并单元格 考生答案 正确答案 是否正确
                $optionsRow->addCell(null, ['vMerge' => 'continue', 'valign' => 'center']);
                $optionsRow->addCell(null, ['vMerge' => 'continue', 'valign' => 'center']);
                $optionsRow->addCell(null, ['vMerge' => 'continue', 'valign' => 'center']);
            }
        }
    }

    /**
     * 获取导出数据
     *
     * @return array 导出数据
     */
    public function fetchData(): array
    {
        /** @var Enrollment $enrollment */
        $enrollment = Enrollment::query()
            ->with([
                'resource' => fn ($query) => $query->publicfields(),
                'student',
                'archive'
            ])
            ->whereIn('type', [Enrollment::TYPE_COURSE, Enrollment::TYPE_COURSE_PACK])
            ->where('id', $this->checkedId)
            ->first();

        $trainTest = [];

        if ($enrollment) {
            $enrollment->toStudent();  // 报名信息完成，学员信息改成存档信息
            $trainTest = TrainTestService::lastTrainTest($enrollment);
        }

        $companyField = array_values(array_filter($enrollment->student->extra ?? [], fn($item) => $item['name'] === '工作单位'));
        $company = !empty($companyField) ? $companyField[0]['value'] : '-';

        return [
            'student_id' => $enrollment->number ?? '-',
            'name' => $enrollment->student->name ?? '-',
            'phone' => $enrollment->student->phone ?? '-',
            'id_card' => $enrollment->student->id_card_number ?? '-',
            'company' => $company ?? '-',
            'test_name' => $trainTest->topic->name ?? '未考试',
            'score' => $trainTest->score ?? '-',
            'passed' => $trainTest->test_level ?? '-',
            'test_time' => isset($trainTest->created_at) ? $trainTest->created_at->format('Y-m-d H:i') : '-',
            'duration' => $trainTest->test_time ?? '-',
            'questions' => $trainTest ? TrainTestService::subjectInfo($trainTest) : []
        ];
    }
}
