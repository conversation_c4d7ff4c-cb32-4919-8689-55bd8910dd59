<?php

namespace App\Services\Org\Export;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Libs\Wps\Converter;
use App\Models\Cms\ContentCourse;
use App\Models\Org;
use App\Models\Org\Course;
use App\Models\Org\Enrollment;
use App\Services\Cms\ContentCourseService;
use App\Services\Common\AttachmentService;
use App\Services\Org\CoursePackService;
use App\Services\Org\EnrollCourseService;
use App\Services\Org\ExportService;
use Carbon\Carbon;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\GifWriter;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * 学时证明导出处理器
 */
class HourCertExporter implements ExporterInterface
{

    use ExportTemplateReplacer;

    public function __construct(
        private readonly int $orgId,
        public int $enrollId, // 勾选的报名学员ID
        array $extra
    ){}

    public function handle(): array|null
    {
        /** @var Enrollment $enroll */
        $enroll = Enrollment::query()
            ->where('id', $this->enrollId)
            ->where('org_id', $this->orgId)
            //->where('type', Enrollment::TYPE_COURSE)
            ->with([
                'org' => fn($query) => $query->select(['id', 'name', 'official_seal_image'])
            ])
            ->first();

        if (!$enroll) {
            throw new ServiceException("学员 {$this->enrollId} 不存在。");
        }

        if (!in_array($enroll->type, [Enrollment::TYPE_COURSE_PACK, Enrollment::TYPE_COURSE])) {
            throw new ServiceException("学员 {$this->enrollId} 不是课程或课程包类型。");
        }

        if ((!$enroll->exam_passed && $enroll->classroom?->exam_enabled) || !$enroll->learn_finished) {
            $msg = "导出学时证明，已跳过，因学员 {$this->enrollId} 未完成学习或未考试通过。";
            Log::warning($msg);
            throw new ServiceException($msg);
        }

        // 已有学时证明就不重复生成了
        if ($enroll->hour_cert) {
            $desc = $this->generateDesc();
            $tmp = AttachmentService::download($enroll->hour_cert_image_url);
            return [$desc, $tmp['local_path']];
        }

        // 如果有考试通过时间, 则用, 否则用班级结束时间
        $endDate = $enroll->exam_pass_at?->format('Y-m-d') ?? $enroll->classroom?->end_at->format('Y-m-d');

        // 开课时间值
        $startEndDate = $enroll->classroom?->start_at->format('Y-m-d') . ' 至 ' . $endDate;
        $startEndDateString = $enroll->started_at && $enroll->expired_at ? $startEndDate : '无';
        $student = $enroll->getStudent();

        //基础信息
        $items = [
            ValueItem::text('证书编号', $enroll->number),
            ValueItem::text('姓名', $student->name),
            ValueItem::text('手机号', $student->phone),
            ValueItem::text('身份证号', $student->id_card_number),
            ValueItem::image('照片', $student->photo, private: true),
            ValueItem::text('课程名称', $enroll->getResourceName() ?: '无'),
            ValueItem::text('开课时间', $startEndDateString),
            ValueItem::text('考试成绩', $enroll->exam_score),
            ValueItem::text('机构名称', $enroll->org->name),
            ValueItem::text('应修学时', EnrollCourseService::getHour($enroll)),
            ValueItem::text('实修学时', EnrollCourseService::getStudyHour($enroll)),
            ValueItem::text('创建日期', Carbon::now()->format('Y年m月d日'))
        ];

        // 机构印章填充逻辑
        if (!empty($enroll->org->official_seal_image)) {
            $items[] = ValueItem::image('机构印章', $enroll->org->official_seal_image);
        } else {
            $items[] = ValueItem::text('机构印章', ' ');
        }

        //报名表扩展信息
        if ($student->extra) {
            $extra = ExportService::parseExtra($this->orgId, $student->extra);
            $items = array_merge($items, $extra);
        }

        //二维码
        $qrCode = new QrCode($enroll->study_record_url);
        $writer = new GifWriter();
        $result = $writer->write($qrCode);
        $items[] = ValueItem::imageDataURI('二维码', $result->getDataUri());

        [$desc, $tmpPath] = $this->generateFromTemplate($this->orgId, Org\Export::TYPE_HOUR_CERT, $items);

        //生成外部可访问的地址
        $localStorage = Storage::disk(config('heguibao.storage.local'));
        $localPath = $localStorage->path('');
        $tmpPathRelative = substr($tmpPath, strlen($localPath));
        $tmpUrl = AttachmentService::url($localStorage, $tmpPathRelative);

        //调试 URL，不然本地图 WPS 取不到
        if (config('app.env') == 'local') {
            $tmpUrl = 'https://img-test.shiwusuo100.com/tmp/%E5%AD%A6%E6%97%B6%E8%AF%81%E6%98%8E%202.docx';
        }

        //转换为JPG格式
        $converter = app(Converter::class);
        $wpsTask = $converter->toJpg($tmpUrl, $desc.'.docx', [
            'ranges' => '1'
        ]);

        //异步任务，为了实现同步的流程，这里一直轮询
        $images = [];

        while (true) {
            sleep(1);
            $task = $converter->getTask($wpsTask['task_id']);

            switch ($task['status']) {
                case 'processing':
                    continue 2;
                case 'success':
                    $images = $task['result']['images'];
                    break 2;
                case 'failure':
                    throw new ServiceException('转换为学时图片失败：'.$task['message']);
                default:
                    throw new ServiceException('转换为学时图片失败：未知状态 '.$task['status']);
            }
        }

        //下载图片
        $tmp = AttachmentService::download($images[0]['url']);

        //在导出时触发保存到报名信息中
        $file = AttachmentService::storeFromLocal($tmp['local_path'], config('heguibao.storage.priv'), 'hour_cert', BusinessType::Enrollment, $enroll->id, $desc.'.jpg');

        $enroll->hour_cert = $file->path;
        $enroll->save();

        return [$desc, $tmp['local_path']];
    }

    public static function validateRules(): array
    {
        return [];
    }

    public function generateDesc(): string
    {
        // 批量下载
        if ($this->enrollId == 0) {
            return '学时证明';
        }

        $enrollment = Enrollment::query()
            ->with(['classroom' => function ($query) {
                $query->select(['id', 'name']);
            }])
            ->where('id', $this->enrollId)
            ->first();

        // 学时证明-班级名称-学生姓名
        return "学时证明-{$enrollment->classroom->name}-{$enrollment->name}";
    }

    /**
     * 基础信息
     *
     * @param Enrollment $enroll
     * @return array
     */
    public static function getBaseData(Enrollment $enroll): array
    {
        $student = $enroll->getStudent();

        return [
            ValueItem::text('cert_number', sprintf('%010d', $enroll->id)),
            ValueItem::text('name', $student->name),
            ValueItem::text('phone', $student->phone),
            ValueItem::text('id_card_number', $student->id_card_number),
            ValueItem::image('photo', $student->photo, private: true),
            ValueItem::text('course_name', $enroll->getResourceName() ?: '无'),
            ValueItem::text('course_stared_at', $enroll->started_at?->format('Y-m-d H:i:s') ?: '无'),
            ValueItem::text('exam_score', $enroll->exam_score),
            ValueItem::text('org_name', $enroll->org->name),
            ValueItem::text('应修学时', EnrollCourseService::getHour($enroll)),
            ValueItem::text('实修学时', EnrollCourseService::getStudyHour($enroll)),
            ValueItem::text('created_at', Carbon::now()->format('Y年m月d日')),
        ];
    }
}
