<?php

namespace App\Services\Org\Export;

use App\Exceptions\ServiceException;
use App\Models\Org;
use App\Services\Common\AttachmentService;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Exception;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Models\Org\Enrollment;
use App\Services\Org\EnrollmentService;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

/**
 * 机构学员导出处理器
 */
class OrgEnrollmentExporter implements ExporterInterface
{
    /**
     * 搜索字段
     *
     * @var array<string>
     */
    public array $search;

    /**
     * 导出字段
     *
     * @var array<string>
     */
    public array $exports;

    /**
     * 勾选的机构ID
     *
     * @var int[]
     */
    public array $enrollmentIds;

    /**
     * 日志记录器
     *
     * @var LoggerInterface
     */
    protected LoggerInterface $logger;

    /**
     * 可导出字段列表
     *
     * @var string[]
     */
    protected static array $exportableFields = [
        'id',
        'user_id',
        'name',
        'phone',
        'id_card_number',
        'class_id',
        'resource_id',
        'status',
        'started_at',
        'expired_at',
        'created_at'
    ];

    /**
     * @param int $orgId
     * @param int $checkedId
     * @param array $extra
     */
    public function __construct(protected int $orgId, public int $checkedId, array $extra)
    {
        $this->enrollmentIds = $extra['enrollment_ids'] ?? [];
        $this->search = $extra['search'] ?? [];
        $this->exports = $extra['exports'] ?? [];
        $this->logger = Log::channel('task');
    }

    /**
     * @inheritDoc
     */
    public static function validateRules(): array
    {
        return [
            'enrollment_ids' =>'array',
            'enrollment_ids.*' =>'integer',
            'search' => 'array',
            'search.uid' => 'integer|nullable',
            'search.name' => 'string|nullable',
            'search.phone' => 'string|nullable',
            'search.class_id' => 'integer|nullable',
            'search.status' => 'integer|nullable',
            'search.type' => 'string|nullable',
            'search.resource_id' => 'integer|nullable',
            'exports' => 'array',
            'exports.*.key' => 'string',
            'exports.*.title' =>'string',
            'exports.*.checked' => 'boolean',
        ];
    }

    /**
     * 处理导出任务
     *
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function handle(): array
    {
        // 创建电子表格
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 获取要导出的字段列表
        $fieldsToExport = $this->getFieldsToExport();

        // 如果没有要导出的字段，返回空数组
        if (empty($fieldsToExport)) {
            throw new ServiceException('导出字段匹配后为空，无法正常导出');
        }

        // 设置表头
        $this->setHeaders($sheet, $fieldsToExport);

        // 获取并填充数据
        $data = $this->getExportData();

        if (empty($data)) {
            throw new ServiceException('导出数据为空，无法正常导出');
        }

        $this->fillData($sheet, $data, $fieldsToExport);

        // 保存文件
        return $this->saveFile($spreadsheet);
    }

    /**
     * 获取要导出的字段列表
     *
     * @return array 字段列表 [key => title]
     */
    protected function getFieldsToExport(): array
    {
        $result = [];
        foreach ($this->exports as $field) {
            if (in_array($field['key'], self::$exportableFields) && $field['checked']) {
                $result[$field['key']] = $field['title'];
            }
        }
        return $result;
    }

    /**
     * 获取导出数据
     *
     * @return array 导出数据
     */
    protected function getExportData(): array
    {
        // 查询数据
        $params = $this->search;
        $params['org_id'] = $this->orgId;
        $params['enrollment_ids'] = $this->enrollmentIds;
        $query = EnrollmentService::getSearchQuery($params)->with(['resource', 'student', 'archive']);

        /** @var Collection<Enrollment> $enrollments */
        $enrollments = $query->get();

        if ($enrollments->isEmpty()) {
            $this->logger->warning("没有找到符合条件的数据");
            return [];
        }

        // 转换数据
        $statusDict = Enrollment::$statusDict;
        return $enrollments->map(function (Enrollment $enrollment) use ($statusDict) {
            $enrollment->toStudent();
            return [
                'id' => $enrollment->id,
                'user_id' => $enrollment->user_id,
                'name' => $enrollment->student->name,
                'phone' => $enrollment->student->phone,
                'id_card_number' => $enrollment->student->id_card_number,
                'class_id' => $enrollment->classroom ? $enrollment->classroom->name : '-',
                'resource_id' => $enrollment->getTypeResourceName(),
                'status' => $statusDict[$enrollment->status] ?? '-',
                'started_at' => $enrollment->started_at ?? '-',
                'expired_at' => $enrollment->expired_at ?? '-',
                'created_at' => $enrollment->created_at
            ];
        })->toArray();
    }

    /**
     * 设置表头
     *
     * @param Worksheet $sheet
     * @param array $fields 要导出的字段 [key => title]
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    protected function setHeaders(Worksheet $sheet, array $fields): void
    {
        $column = 'A';
        foreach ($fields as $key => $title) {
            $sheet->setCellValue($column . '1', $title);

            // 设置列宽
            $sheet->getColumnDimension($column)->setWidth(20);

            // 根据字段类型设置不同的列格式
            if (in_array($key, ['phone', 'id_card_number'])) {
                $sheet->getStyle($column)->getNumberFormat()->setFormatCode('@');
            }

            $column++;
        }

        // 设置表头样式
        $lastColumn = chr(ord('A') + count($fields) - 1);  // 计算最后一列的字母
        $headerRange = "A1:{$lastColumn}1";

        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'CCCCCC'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ]);

        // 调整行高
        $sheet->getRowDimension(1);
    }

    /**
     * 填充数据
     *
     * @param Worksheet $sheet
     * @param array $data 数据
     * @param array $fields 要导出的字段 [key => title]
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    protected function fillData(Worksheet $sheet, array $data, array $fields): void
    {
        $row = 2;
        foreach ($data as $item) {
            $column = 'A';
            foreach (array_keys($fields) as $key) {
                // 特殊处理身份证和手机号字段
                if (in_array($key, ['phone', 'id_card_number'])) {
                    $sheet->setCellValueExplicit($column . $row, $item[$key] ?? '-', DataType::TYPE_STRING);
                } else {
                    $sheet->setCellValue($column . $row, $item[$key] ?? '-');
                }

                // 设置数据行样式
                $sheet->getStyle($column . $row)->applyFromArray([
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_LEFT,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                        ],
                    ],
                ]);

                $column++;
            }
            $row++;
        }
    }

    /**
     * 保存文件
     *
     * @param Spreadsheet $spreadsheet
     * @return array [filename, tempFile]
     * @throws Exception
     */
    protected function saveFile(Spreadsheet $spreadsheet): array
    {
        $writer = new Xlsx($spreadsheet);
        list(, $tempFile) = AttachmentService::tmpFile('xlsx', 'enrollment_export_');
        $writer->save($tempFile);
        $filename = $this->generateDesc();

        return [$filename, $tempFile];
    }

    /**
     * @inheritDoc
     */
    public function generateDesc(): string
    {
        $orgName = Org::where('id', $this->orgId)->value('name');
        return "机构学员-{$orgName}";
    }
}
