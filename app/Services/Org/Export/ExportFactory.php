<?php
namespace App\Services\Org\Export;

use InvalidArgumentException;
use App\Models\Org\Export;

/**
 * 导出工厂类
 * 用于创建不同类型的导出处理器
 */
class ExportFactory
{
    /**
     * 根据导出类型获取对应的处理器类
     *
     * @param string $type 导出类型
     * @return string 处理器类名
     * @throws InvalidArgumentException 如果不支持的导出类型
     */
    public static function getHandlerClassByType(string $type): string
    {
        return match ($type) {
            Export::TYPE_HOUR_CERT => HourCertExporter::class,
            Export::TYPE_STUDENT_ARCHIVE => StudentArchiveExporter::class,
            Export::TYPE_HOUR_RECORD => HourRecordExporter::class,
            Export::TYPE_TEST_PAPER => TestPaperExporter::class,
            Export::TYPE_ORG_ENROLLMENT => OrgEnrollmentExporter::class,
            Export::TYPE_ORG_ENROLLMENT_FORM => OrgEnrollmentFormExporter::class,
            Export::TYPE_ORG_DOWNLOAD_PACK => OrgDownloadPackExporter::class,
            default => throw new InvalidArgumentException("不支持的导出类型: {$type}"), // 新增导出类型时添加对应的处理器类
        };
    }

    /**
     * 创建导出处理器
     *
     * @param string $type
     * @param int $orgId
     * @param int $checkedId
     * @param array $extra
     * @return ExporterInterface
     */
    public static function create(string $type, int $orgId, int $checkedId, array $extra): ExporterInterface
    {
        $handlerClass = self::getHandlerClassByType($type);
        return new $handlerClass($orgId, $checkedId, $extra);
    }
}
