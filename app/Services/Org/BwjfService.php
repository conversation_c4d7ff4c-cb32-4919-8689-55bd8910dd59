<?php

namespace App\Services\Org;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

/**
 * 无锡市惠山区安全培训中心 - 开具发票服务， 云票（发票提供商）
 */
class BwjfService
{
    // 接入服务地址
    protected string $accessUrl = 'http://kp.bwmis.cn:10005/security/access';

    // 配置参数
    protected array $config = [
        'companyPriKey' => '302A586342F2470AFF68F71DE77C0E7B88CD7579706CFBB3F727A59A96B889A0',
        'platFormPubKey' => '8F1F87A150E5404B6068CB361A8D4EE2744784F2B9695B00B5876E208BC9EBCCD8F175569AB081EDFD5BC958914E22461B5665D54A6B457D5CEF3CD0A2B7D62F',
        'enterpriseCode' => '2025040373353674',
        'thirdPlatformCode' => '',
        'businessCode' => '',
        'productCode' => '',
        'contentType' => '1',
    ];

    // 开票终端标识
    protected string $kpzdbs = '3200fafdbdfe3407874886e2a81d9c8f1a7ec852320206509327436Q';

    public function sendRequest(string $api, string $businessCode, array $content): array
    {
        $payload = [
            'url' => 'https://www.bwjf.cn' . $api,
            'companyPriKey' => $this->config['companyPriKey'],
            'platFormPubKey' => $this->config['platFormPubKey'],
            'content' => [
                'businessCode' => $businessCode,
                'content' => $content,
            ],
            'accessType' => '1',
            'enterpriseCode' => $this->config['enterpriseCode'],
            'thirdPlatformCode' => $this->config['thirdPlatformCode'],
            'reqMsgId' => (string) Str::uuid(),
            'businessCode' => $businessCode,
            'productCode' => $this->config['productCode'],
            'contentType' => $this->config['contentType'],
        ];

        logger()->debug("向云票平台发生请求参数: ", ['api' => $api, 'payload' => $payload]);

        try {
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($this->accessUrl, $payload);

            // 先获取原始响应体
            $rawResponse = $response->body();
            logger()->debug("向云票平台请求结果: ", ['api' => $api, 'response' => $rawResponse]);

            if ($response->successful()) {
                // 尝试解析JSON响应
                $responseData = $response->json();

                // 如果json()返回null，说明响应不是有效的JSON
                if ($responseData === null && $rawResponse !== 'null') {
                    return [
                        'success' => false,
                        'code' => 'INVALID_JSON',
                        'message' => '响应不是有效的JSON格式: ' . $rawResponse,
                        'data' => null,
                    ];
                }

                return $this->parseApiResponse($responseData);
            } else {
                return [
                    'success' => false,
                    'code' => $response->status(),
                    'message' => $rawResponse,
                    'data' => null,
                ];
            }
        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            logger()->error("网络连接失败", [
                'error' => $e->getMessage(),
                'api' => $api,
            ]);
            return [
                'success' => false,
                'code' => 'CONNECTION_ERROR',
                'message' => '网络连接失败，请检查网络设置或联系管理员: ' . $e->getMessage(),
                'data' => null,
            ];
        } catch (\Exception $e) {
            logger()->error("请求云票平台接口失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'api' => $api,
            ]);
            return [
                'success' => false,
                'code' => 'EXCEPTION',
                'message' => '请求失败: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * 解析API响应数据
     *
     * @param mixed $responseData
     * @return array
     */
    protected function parseApiResponse($responseData): array
    {
        // 如果响应数据为空
        if (!$responseData) {
            return [
                'success' => false,
                'code' => 'EMPTY_RESPONSE',
                'message' => '响应数据为空',
                'data' => null,
            ];
        }

        // 如果响应是字符串（通常是错误信息）
        if (is_string($responseData)) {
            return [
                'success' => false,
                'code' => 'STRING_RESPONSE',
                'message' => $responseData,
                'data' => null,
            ];
        }

        // 如果响应不是数组
        if (!is_array($responseData)) {
            return [
                'success' => false,
                'code' => 'INVALID_TYPE',
                'message' => '响应数据类型不正确: ' . gettype($responseData),
                'data' => $responseData,
            ];
        }

        // 检查响应格式是否符合预期
        if (!isset($responseData['message']['code']) || !isset($responseData['message']['success'])) {
            return [
                'success' => false,
                'code' => 'INVALID_FORMAT',
                'message' => '响应格式不正确，缺少必要字段',
                'data' => $responseData,
            ];
        }

        // 根据云票平台的响应格式解析
        $isSuccess = $responseData['message']['success'] === true && $responseData['message']['code'] === '0';

        return [
            'success' => $isSuccess,
            'code' => $responseData['message']['code'],
            'message' => $responseData['message']['messages'] ?? ($isSuccess ? '成功' : '失败'),
            'data' => $responseData['message']['content'] ?? null,
        ];
    }

    /**
     * 开具发票
     *
     * @param string $name 购买方名称
     * @param string $phone 购买方电话
     * @param int $amount 发票金额（含税）
     * @return array
     */
    public function issueInvoice(string $name, string $phone, int $amount): array
    {
        try {
            // 第一步：开具发票
            $invoiceResult = $this->createInvoice($name, $amount);
            if (!$invoiceResult['success']) {
                return $invoiceResult;
            }

            $invoiceData = $invoiceResult['data'];
            $fphm = $invoiceData['fphm'] ?? null;
            $kprq = $invoiceData['kprq'] ?? null;

            if (!$fphm || !$kprq) {
                return [
                    'success' => false,
                    'code' => 'MISSING_INVOICE_INFO',
                    'message' => '发票开具成功但缺少发票号码或开票日期',
                    'data' => null,
                ];
            }

            // 第二步：查询发票下载地址
            $downloadResult = $this->queryInvoiceDownloadUrl($fphm, $kprq);
            if (!$downloadResult['success']) {
                return $downloadResult;
            }

            // 第三步：推送发票布局
            $pushResult = $this->pushInvoiceLayout($name, $phone, $fphm, $kprq, $amount);
            if (!$pushResult['success']) {
                return $pushResult;
            }

            // 合并所有结果
            $finalData = array_merge(
                $invoiceData,
                $downloadResult['data'] ?? [],
                $pushResult['data'] ?? []
            );

            logger()->info("云票发票开具完成", [
                'name' => $name,
                'phone' => $phone,
                'amount' => $amount,
                'fphm' => $fphm,
                'data' => $finalData,
            ]);

            return [
                'success' => true,
                'code' => '0',
                'message' => '发票开具成功',
                'data' => $finalData,
            ];

        } catch (\Exception $e) {
            logger()->error("发票开具过程中发生异常", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'name' => $name,
                'phone' => $phone,
                'amount' => $amount,
            ]);

            return [
                'success' => false,
                'code' => 'EXCEPTION',
                'message' => '发票开具失败: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * 创建发票（第一步）
     *
     * @param string $name
     * @param int $amount
     * @return array
     */
    protected function createInvoice(string $name, int $amount): array
    {
        $hjje = round($amount / 1.03, 2);
        $hjse = round($amount - $hjje, 2);

        $content = [
            'requestType' => '1',
            'fpqqlsh' => md5(time()),
            'kpzdbs' => $this->kpzdbs,
            'xsfnsrsbh' => '52320206509327436Q',
            'xsfdz' => '无锡市惠山区亿丰商业广场B区19-301',
            'xsflxdh' => '0510-82442470',
            'xsfkhh' => '工商银行惠山支行',
            'xsfyhzh' => '1103026619000651770',
            'fplxdm' => '82',
            'zrrbs' => 'Y',
            'xsfmc' => '无锡市惠山区安全生产管理培训中心',
            'xsfZrrbs' => 'N',
            'gmfmc' => $name,
            'hjje' => (string)$hjje,
            'hjse' => (string)$hjse,
            'jshj' => (string)$amount,
            'hsbz' => '1',
            'fyxmList' => [
                [
                    'xh' => '1',
                    'fphxz' => '0',
                    'xmmc' => '*非学历教育服务*培训',
                    'je' => (string)$hjje,
                    'slv' => '0.03',
                    'se' => (string)$hjse,
                    'tdzsfsdm' => '05',
                    'zzstsgldm' => '01',
                    'ssflbm' => '3070201020000000000',
                ]
            ],
        ];

        $result = $this->sendRequest('/api/billingAll/v1/sd/invoice/sdInvoiceOpening', 'sdInvoiceOpening', $content);
        logger()->debug("云票发票调用第一步结果: ", $result);

        return $result;
    }

    /**
     * 查询发票下载地址（第二步）
     *
     * @param string $fphm
     * @param string $kprq
     * @return array
     */
    protected function queryInvoiceDownloadUrl(string $fphm, string $kprq): array
    {
        $content = [
            'kpzdbs' => $this->kpzdbs,
            'fphm' => $fphm,
            'kprq' => $kprq,
        ];

        $result = $this->sendRequest('/api/billingAll/v1/sd/invoice/sdQueryInvoiceDownloadUrl', 'sdQueryInvoiceDownloadUrl', $content);
        logger()->debug("云票发票调用第二步结果: ", $result);

        return $result;
    }

    /**
     * 推送发票布局（第三步）
     *
     * @param string $name
     * @param string $phone
     * @param string $fphm
     * @param string $kprq
     * @param int $amount
     * @return array
     */
    protected function pushInvoiceLayout(string $name, string $phone, string $fphm, string $kprq, int $amount): array
    {
        $content = [
            'kpzdbs' => $this->kpzdbs,
            'fphm' => $fphm,
            'fplxdm' => '82',
            'gmfmc' => $name,
            'gfkhdh' => $phone,
            'xsfnsrsbh' => '52320206509327436Q',
            'xsfmc' => '无锡市惠山区安全生产管理培训中心',
            'kprq' => $kprq,
            'jshj' => $amount,
        ];

        $result = $this->sendRequest('/api/invoicePlat/v2/allEleInvoice/pushLayout', 'allEleInvoicePushLayout', $content);
        logger()->debug("云票发票调用第三步结果: ", $result);

        return $result;
    }

    /**
     * 格式化发票下载链接
     *
     * @param array $data
     * @return array
     */
    public function formatInvoiceUrls(array $data): array
    {
        $urls = [];

        // 提取各种格式的下载链接
        if (isset($data['ewmUrl'])) {
            $urls['qr_code_url'] = $data['ewmUrl'];
        }

        if (isset($data['pdfUrl'])) {
            $urls['pdf_url'] = $data['pdfUrl'];
        }

        if (isset($data['ofdUrl'])) {
            $urls['ofd_url'] = $data['ofdUrl'];
        }

        if (isset($data['xmlUrl'])) {
            $urls['xml_url'] = $data['xmlUrl'];
        }

        if (isset($data['appletEwmUrl'])) {
            $urls['applet_qr_url'] = $data['appletEwmUrl'];
        }

        return $urls;
    }

    /**
     * 验证发票响应数据完整性
     *
     * @param array $data
     * @return array
     */
    public function validateInvoiceData(array $data): array
    {
        $errors = [];

        // 检查必要的字段
        $requiredFields = ['ewmUrl', 'pdfUrl'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[] = "缺少必要字段: {$field}";
            }
        }

        // 检查URL格式
        $urlFields = ['ewmUrl', 'pdfUrl', 'ofdUrl', 'xmlUrl', 'appletEwmUrl'];
        foreach ($urlFields as $field) {
            if (!empty($data[$field]) && !filter_var($data[$field], FILTER_VALIDATE_URL)) {
                $errors[] = "字段 {$field} 不是有效的URL格式";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }
}
