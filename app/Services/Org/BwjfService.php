<?php

namespace App\Services\Org;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

/**
 * 云票-无锡市惠山区安全培训中心 发票提供商
 */
class BwjfService
{
    // 接入服务地址
    protected string $accessUrl = 'http://kp.bwmis.cn:10005/security/access';

    // 配置参数
    protected array $config = [
        'companyPriKey' => 'BBEEB3B98463C172F4F4A0892BA025DA336AE41DEA69E89FC90EBEFB5ED683F1',
        'platFormPubKey' => 'E062C297219D5384D8AEE3F7C4AB6BEC1529A08102ECE362A27BFE8368545759B012BA2873B46BC61CFFCA4C73009550804225F53932F4223F39D45AA9B26596',
        'enterpriseCode' => '2025040373353674',
        'thirdPlatformCode' => '',
        'businessCode' => '',
        'productCode' => '',
        'contentType' => '1',
    ];

    // 开票终端标识
    protected string $kpzdbz = '3200fafdbdfc3407874838e2a81d9c8f1a7ec852320206509327436Q';

    public function sendRequest(string $api, string $businessCode, array $content): array
    {
        $payload = [
            'url' => 'https://wmw.bwif.cn' . $api,
            'companyPriKey' => $this->config['companyPriKey'],
            'platFormPubKey' => $this->config['platFormPubKey'],
            'content' => [
                'businessCode' => $businessCode,
                'content' => $content,
            ],
            'accessType' => '1',
            'enterpriseCode' => $this->config['enterpriseCode'],
            'thirdPlatformCode' => $this->config['thirdPlatformCode'],
            'reqMsgId' => Str::uuid(),
            'businessCode' => $businessCode,
            'productCode' => $this->config['productCode'],
            'contentType' => $this->config['contentType'],
        ];

        logger()->debug("向云票平台发生请求参数: ", ['api' => $api, 'payload' => $payload]);

        try {
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($this->accessUrl, $payload);

            logger()->debug("向云票平台请求结果: ", ['api' => $api, 'response' => $response->json()]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            } else {
                return [
                    'success' => false,
                    'status' => $response->status(),
                    'message' => $response->body(),
                ];
            }
        } catch (\Exception $e) {
            logger()->error("请求云票平台接口失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return [
                'success' => false,
                'message' => '请求失败: ' . $e->getMessage(),
            ];
        }
    }

    public function issueInvoice(string $name, string $phone, int $amount)
    {
        $hjje = round($amount / 1.03, 2);
        $hjse = $amount - $hjje;
        $api = '/api/billingAll/v1/sd/invoice/sdInvoiceOpening';
        $businessCode = 'sdInvoiceOpening';
        $content = [
            'businessCode' => $businessCode,
            'content' => [
                'requestType' => '1',
                'fpqqlsh' => md5(time()),
                'kpzdbz' => $this->kpzdbz,
                'xsfnrsrbh' => '52320206509327436Q',
                'xsflxdz' => '无锡市惠山区亿丰商业广场B区19-301',
                'xsflxdh' => '0510-82442470',
                'xsfkhh' => '工商银行惠山支行',
                'xsfyhz' => '1103026619000651770',
                'fplxdm' => '85',
                'zrrbs' => '',
                'xsfmc' => '无锡市惠山区安全生产管理培训中心',
                'xsfZrrbs' => 'N',
                'gmdmc' => $name,
                'hjje' => $hjje,
                'hjse' => $hjse,
                'jshj' => $amount,
                'hzbz' => '1',
                'fyxmlist' => [
                    'xh' => '1',
                    'fphxz' => '0',
                    'xmmc' => '*非学历教育服务*培训',
                    'je' => $hjje,
                    'slv' => '0.03',
                    'se' => $hjse,
                    'tdtzsfidm' => '05',
                    'zzstsgldm' => '01',
                    'ssflbm' => '3070201020000000000',
                ],
            ]
        ];
        $sdInvoiceOpening = $this->sendRequest($api, $businessCode, $content);
        logger()->debug("云票发票调用第一步结果: ", $sdInvoiceOpening);

        $sdQueryInvoiceDownloadUrl = $this->sendRequest('/api/billingAll/v1/sd/invoice/sdQueryInvoiceDownloadUrl',
            'sdQueryInvoiceDownloadUrl',
            [
                'businessCode' => 'sdQueryInvoiceDownloadUrl',
                'content' => [
                    'kpzdbs' => $this->kpzdbz,
                    'fphm' => $sdInvoiceOpening['content']['fphm'],
                    'kprq' => $sdInvoiceOpening['content']['kprq'],
                ]
            ]
        );
        logger()->debug("云票发票调用第二步结果: ", $sdQueryInvoiceDownloadUrl);

        $allEleInvoicePushLayout = $this->sendRequest('/api/invoicePlat/v2/allEleInvoice/pushLayout', 'allEleInvoicePushLayout', [
            'businessCode' => 'allEleInvoicePushLayout',
            'content' => [
                'kpzdbs' => $this->kpzdbz,
                'fphm' => $sdInvoiceOpening['content']['fphm'],
                'fplxdm' => '82',
                'gmfmc' => $name,
                'gfkhdh' => $phone,
                'xsfnsrsbh' => '52320206509327436Q',
                'xsfmc' => '无锡市惠山区安全生产管理培训中心',
                'kprq' => $sdInvoiceOpening['content']['kprq'],
                'jshj' => $amount,
            ]
        ]);

        logger()->debug("云票发票调用第三步结果: ", $allEleInvoicePushLayout);

        logger()->debug("云票发票调用完成");
    }
}
