<?php

namespace App\Services\Org\TemplateReplace;

use App\Services\Common\AttachmentService;
use App\Services\Org\Export\ValueItem;
use PhpOffice\PhpWord\TemplateProcessor;

class ReplaceService
{

    /**
     * 单项 ValueItem 替换
     *
     * @param ValueItem $value
     * @param TemplateProcessor $processor
     * @param string $prefix
     * @param string $suffix
     * @return void
     */
    public static function replaceItem(ValueItem $value, TemplateProcessor $processor, string $prefix='', string $suffix='')
    {
        /** @var ReplaceBase $replace */
        $replace = match ($value->type) {
            'text' => TextReplace::class,
            'options' => CheckboxReplace::class,
            'image' => ImageReplace::class,
            'list' => ListReplace::class,
            'date' => DateReplace::class
        };

        $instance = new $replace($value, $processor, $prefix, $suffix);
        $instance->replace();
    }

    /**
     * 开始模板替换
     *
     * @param string $tpl 模板文件路径或 URL
     * @param ValueItem[] $items 初始给定值
     * @return string 输出的本地临时文件路径
     */
    public static function replace(string $tpl, array $items): string
    {
        $templateProcessor = new TemplateProcessor($tpl);

        foreach ($items as $value) {
            self::replaceItem($value, $templateProcessor);
        }

        //清理所有剩余变量
        $variables = $templateProcessor->getVariables();
        foreach ($variables as $var) {
            $templateProcessor->setValue($var, '');
        }

        $fileName = date('YmdHis') . uniqid() . '.docx';
        $outputPath = AttachmentService::tmpPath($fileName);
        $templateProcessor->saveAs($outputPath);

        return $outputPath;
    }

    /**
     * 数据转换
     *
     * @param array $items
     * @return array
     */
    public static function convert(array $items): array
    {
        $data = [];
        foreach ($items as $item) {
            match ($item->type) {
                'text' => $data[$item->name] = $item->getText(),
                'date' => $data[$item->name] = (string)$item,
                'image' => $data[$item->name] = $item->getLocalPath(),
                'options' => $data[$item->name] = $item->getOptions(),
                'list' => $data[$item->name] = self::listConvert($item->value),
            };
        }

        return $data;
    }

    public static function listConvert(array $items): array
    {
        $data = [];
        foreach ($items as $item) {
            if (!isset($item)) {
                return $data;
            }
            foreach ($item as $value) {
                $data[] = self::convert($value);
            }
        }

        return $data;
    }
}
