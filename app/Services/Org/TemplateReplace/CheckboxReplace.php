<?php

namespace App\Services\Org\TemplateReplace;

class CheckboxReplace extends ReplaceBase
{

    public function replace()
    {
        $values = $this->value->getOptions();
        $variables = $this->processor->getVariables();

        $search = $this->getSearchName();

        if (in_array($search, $variables)) {
            $checkboxText = '';
            foreach ($values as $value) {
                $checkboxText .= $value['selected'] ? " ☑{$value['text']}" : " □{$value['text']}";
            }
            $this->processor->setValue($search, $checkboxText);
        } else {
            foreach ($values as $value) {
                $search = !empty($extra) ? $extra['name'] . '.' . $value['text'] . '#' . $extra['index'] : $value['text'];
                $this->processor->setValue($search, $value['selected'] ? " ☑{$value['text']}" : " □{$value['text']}");
            }
        }
    }
}
