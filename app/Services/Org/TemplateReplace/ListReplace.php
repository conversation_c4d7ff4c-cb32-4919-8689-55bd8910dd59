<?php

namespace App\Services\Org\TemplateReplace;

use App\Services\Org\Export\ValueItem;

class ListReplace extends ReplaceBase
{

    protected int $batchNumber = 100;

    protected string $cloneName;

    public function replace()
    {
        $searchName = $this->getSearchName();

        $variables = $this->processor->getVariables();

        //判定是 cloneRow 还是 cloneBlock
        $isBlock = in_array($searchName, $variables) && in_array('/'.$searchName, $variables);

        //if ($isBlock) {
        //    echo "I am block, my search name: $searchName\n";
        //}

        $total = 0;

        //因为 ValueList 是支持懒加载的，所以最好的操作就是从头到尾一条条遍历，不要对整个数据进行 count 等涉及到全部数据的操作，这样可以避免性能损失
        //但是在已经遍历出的 batch 里，可以进行这些操作，因为这个 batch 的实际数据已经拿到
        $batch = [];

        foreach ($this->value as $value) {
            $batch[] = $value;
            $total += 1;
            if (count($batch) == $this->batchNumber) {
                $this->replaceInBatch($batch, $isBlock);
                $batch = [];
            }
        }

        //可能剩下一批不满足循环中的 batch 再单独处理
        $batchLeft = count($batch);

        if ($batchLeft > 0) {
            $this->replaceInBatch($batch, $isBlock);
            $total += $batchLeft;
        }

        if (isset($this->cloneName) && $this->cloneName) {
            if ($isBlock) {
                $this->processor->deleteBlock($this->cloneName);
            } else {
                $this->processor->deleteRow($this->cloneName);
            }
        } else {
            //没有找到 cloneName 一般就是没数据，也可以通过 $total == 0 来判断
            //此时要清理残留的变量，清理残留的包含子字段名的变量
            $variables = $this->processor->getVariables();
            if ($isBlock) {
                //deleteBlock 有问题，可能导致表格后面的内容全被删除了
                //foreach ($variables as $var) {
                //    if ($var == $searchName) {
                //        echo "Delete block by name: $var\n";
                //        $this->processor->deleteBlock($var);
                //        break;
                //    }
                //}
            } else {
                $prefix = $this->prefix.$this->value->name.'.';
                foreach ($variables as $var) {
                    if (preg_match('/^'.preg_quote($prefix).'[^.#]+'.preg_quote($this->suffix).'$/', $var)) {
                        $this->processor->deleteRow($var);
                        break;
                    }
                }
            }
        }
    }

    /**
     * @param array $items
     * @return void
     */
    protected function replaceInBatch(array $items, $isBlock)
    {
        $count = count($items);

        $this->cloneName ??= $this->detectCloneName($items, $isBlock);

        //echo "Detect clone name: $this->cloneName\n";
        //echo "Type: ".($isBlock? 'block' : 'row')."\n";

        //没有对应的变量名就不替换了，说明模板中没有这东西
        if (!$this->cloneName) {
            return;
        }

        if (!$isBlock) {
            $this->processor->cloneRow($this->cloneName, $count+1);
        } else {
            $this->processor->cloneBlock($this->cloneName, $count+1, true, true);
        }

        //echo "Current variables:\n";
        //print_r($this->processor->getVariables());

        foreach ($items as $i => $rows) {
            $prefix = $this->prefix.$this->value->name.'.';
            $suffix = $this->suffix.'#'.($i+1);
            if ($rows instanceof ValueItem) {
                //if ($rows->type == 'list') {
                //    echo "To list 1, prefix: $prefix, suffix: $suffix\n";
                //    //continue;
                //}
                ReplaceService::replaceItem($rows, $this->processor, $prefix, $suffix);
            } else {
                foreach ($rows as $value) {
                    //if ($value->type == 'list') {
                    //    echo "To list 2, prefix: $prefix, suffix: $suffix\n";
                    //    //continue;
                    //}
                    //$rowPrefix = $value->type == 'list' ? $prefix.$value->name.'.' : $prefix;
                    ReplaceService::replaceItem($value, $this->processor, $prefix, $suffix);
                }
            }
        }

        //把最后一行还原为无序号版本
        $variables = $this->processor->getVariables();
        $prefix = $this->prefix.$this->value->name.'.';
        $suffix = $this->suffix.'#'.($count+1);

        foreach ($variables as $var) {
            if (str_starts_with($var, $prefix) && str_ends_with($var, $suffix)) {
                $this->processor->setValue($var, '${'.substr($var, 0, -strlen($suffix)).'}');
            }
        }
    }

    /**
     * 决定基于哪个字段进行 cloneRow
     *
     * @param ValueItem|ValueItem[] $row
     * @param bool $isBlock 对于 block 是克隆上层的 block 名称，而 row 则是以字段名称克隆
     * @return string
     */
    protected function detectCloneName($row, $isBlock)
    {
        $variables = $this->processor->getVariables();

        if ($isBlock) {
            $name = $this->prefix.$this->value->name.$this->suffix;
            //echo "Try detect block: $name\n";
            return in_array($name, $variables) ? $name : '';
        }

        if ($row instanceof ValueItem) {
            $name = $this->prefix.$this->value->name.'.'.$row->name.$this->suffix;
            //echo "Try detect item: $name\n";
            return in_array($name, $variables) ? $name : '';
        } elseif ($row[0] instanceof ValueItem) {
            foreach ($row as $value) {
                $name = $this->prefix.$this->value->name.'.'.$value->name.$this->suffix;
                //echo "Try detect item in [0]: $name\n";
                if (in_array($name, $variables)) {
                    return $name;
                }
            }
        } else {
            foreach ($row[0] as $value) {
                $name = $this->prefix.$this->value->name.'.'.$value->name.$this->suffix;
                //echo "Try detect item in [0][0]: $name\n";
                if (in_array($name, $variables)) {
                    return $name;
                }
            }
        }

        return '';
    }

}
