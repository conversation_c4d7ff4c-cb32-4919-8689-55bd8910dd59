<?php

namespace App\Services\Org\TemplateReplace;

use App\Services\Org\Export\ValueItem;
use PhpOffice\PhpWord\TemplateProcessor;

abstract class ReplaceBase
{

    public function __construct(protected ValueItem $value, protected TemplateProcessor $processor, protected readonly string $prefix='', protected readonly string $suffix='')
    {
    }

    protected function getSearchName()
    {
        return "{$this->prefix}{$this->value->name}{$this->suffix}";
    }

    public abstract function replace();

}
