<?php

namespace App\Services\Org\Admin;

use App\Exceptions\ServiceException;
use App\Models\Org\Admin\Role;
use App\Models\Org\Admin\AdminRole;

class RoleService
{
    public static function create(array $params): Role
    {
        if (Role::query()->where('code', $params['code'])->exists()) {
            throw new ServiceException('编码已存在');
        }

        $role = new Role();
        foreach ($params as $key => $val) {
            if (in_array($key, $role->getFillable())) {
                $role->{$key} = $val;
            }
        }
        $role->save();

        return $role;
    }

    public static function update(int $id, array $params): Role
    {
        /** @var Role $role */
        $role = Role::query()->find($id);

        if (!$role) {
            throw new ServiceException('权限不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $role->getFillable())) {
                $role->{$key} = $val;
            }
        }

        $role->save();

        return $role;
    }

    public static function remove(int $id): void
    {
        /** @var Role $role */
        $role = Role::query()->find($id);

        if (!$role) {
            throw new ServiceException('权限不存在');
        }

        if (AdminRole::query()->where('role_id', $role->id)->exists()) {
            throw new ServiceException('请先移除关联的用户');
        }

        $role->delete();
    }
}
