<?php

namespace App\Services\Org\Admin;

use App\Exceptions\ServiceException;
use App\Models\Org\Admin\Admin;
use App\Models\Org\Admin\AdminRole;
use App\Models\Org\Admin\Role;
use Carbon\Carbon;

class AdminService
{
    /**
     * 账号登录
     * @param string $username
     * @param string $password
     * @param string $ip
     * @return Admin
     */
    public static function login(string $username, string $password, string $ip): Admin
    {
        /** @var Admin $admin */
        $admin = Admin::query()->where('username', $username)->first();

        if (!$admin) {
            throw new ServiceException('输入的账号不存在');
        }

        if (!password_verify($password, $admin->password)) {
            throw new ServiceException('输入的密码不正确');
        }

        if ($admin->status != Admin::STATUS_ENABLED) {
            throw new ServiceException('账号已被禁用');
        }

        $admin->last_logged_at = Carbon::now();
        $admin->last_logged_ip = $ip;
        $admin->last_active_at = Carbon::now();
        $admin->last_active_ip = $ip;
        $admin->save();

        return $admin;
    }

    /**
     * 创建账号
     * @param int $orgId
     * @param string $username
     * @param string $password
     * @param array $append
     * @return Admin
     */
    public static function create(int $orgId, string $username, string $password, array $append = []): Admin
    {
        if (Admin::query()->where('username', $username)->exists()) {
            throw new ServiceException('账号已注册');
        }

        if (empty($append['role_ids'])) {
            throw new ServiceException('请选择权限');
        }

        $admin = new Admin();
        $admin->org_id = $orgId;
        $admin->username = $username;
        $admin->password = self::generatePassword($password);

        foreach ($append as $key => $val) {
            if (in_array($key, $admin->getFillable())) {
                $admin->{$key} = $val;
            }
        }

        $admin->save();

        self::handleRoles($admin, $append, false);

        return $admin;
    }

    /**
     * 修改账号
     * @param int $id
     * @param array $data
     * @return Admin
     * @throws ServiceException
     */
    public static function update(int $id, array $data): Admin
    {
        /** @var Admin $admin */
        $admin = Admin::query()->find($id);

        if (!$admin) {
            throw new ServiceException('账号不存在');
        }

        foreach ($data as $key => $val) {
            if (in_array($key, $admin->getFillable())) {
                $admin->{$key} = $val;
            }
        }
        $admin->save();

        self::handleRoles($admin, $data);

        return $admin;
    }

    /**
     * 修改密码
     * @param int $id
     * @param string $password
     * @return Admin
     * @throws ServiceException
     */
    public static function updatePassword(int $id, string $password): Admin
    {
        /** @var Admin $admin */
        $admin = Admin::query()->find($id);

        if (!$admin) {
            throw new ServiceException('账号不存在');
        }

        $admin->password = self::generatePassword($password);
        $admin->save();

        return $admin;
    }

    public static function generatePassword(string $password): string
    {
        return password_hash($password, PASSWORD_BCRYPT);
    }

    public static function checkStatus(Admin $admin): void
    {
        if ($admin->status == Admin::STATUS_DISABLED) {
            throw new ServiceException('账号不存在或已禁用');
        }
    }

    public static function getRoles(Admin $admin): array
    {
        $roles = [];
        foreach ($admin->roles as $item) {
            if ($item->role) {
                $roles[] = $item->role->code;
            }
        }

        return $roles;
    }

    public static function checkRole(Admin $admin, array $checkRoles = [Role::CODE_ADMIN]): bool
    {
        $check = false;
        $adminRoles = self::getRoles($admin);

        foreach ($adminRoles as $role) {
            if (in_array($role, $checkRoles)) {
                $check = true;
                break;
            }
        }

        return $check;
    }

    protected static function handleRoles(Admin $admin, array $params, bool $isUpdate = true): void
    {
        if (empty($params['role_ids'])) {
            return;
        }

        if ($isUpdate) {
            AdminRole::query()->where('admin_id', $admin->id)->delete();
        }

        $insert = [];
        $now = Carbon::now();
        foreach ($params['role_ids'] as $id) {
            $insert[] = [
                'admin_id' => $admin->id,
                'role_id' => $id,
                'created_at' => $now
            ];
        }

        AdminRole::query()->insert($insert);
    }
}
