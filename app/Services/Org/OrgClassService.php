<?php

namespace App\Services\Org;

use App\Exceptions\ServiceException;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCoursePack;
use App\Models\Org;
use App\Models\Org\Course;
use App\Models\Org\Enrollment;
use App\Models\Org\LearnCapture;
use App\Models\Org\OrgClass;
use App\Models\Train\Test;
use App\Models\Train\Topic;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class OrgClassService
{
    /**
     * 当前考试信息
     *
     * @param User $user
     * @param Enrollment $enrollment
     * @param string $platform 平台 pc/miniProgram
     * @return array
     */
    public static function currentExam(User $user, Enrollment $enrollment, string $platform = 'miniProgram'): array
    {
        $class = $enrollment->classroom;
        if (!$class) {
            return [];
        }

        if (!$class->exam_enabled) {
            return [];
        }

        if ($enrollment->type == Enrollment::TYPE_COURSE_PACK) {
            /** @var ContentCoursePack $coursePack */
            $coursePack = ContentCoursePack::query()
                ->with('topic', fn ($query) => $query->publicFields())
                ->where('content_id', $enrollment->resource_id)
                ->first();

            if (!$coursePack || !$coursePack->topic) {
                return [];
            }

            $topic = $coursePack->topic;
        } elseif ($enrollment->type == Enrollment::TYPE_COURSE) {
            /** @var ContentCourse $course */
            $course = ContentCourse::query()
                ->with('topic', fn ($query) => $query->publicFields())
                ->where('content_id', $enrollment->resource_id)
                ->first();

            if (!$course || !$course->topic) {
                return [];
            }

            $topic = $course->topic;
        } else {
            return [];
        }

        $topic->setHidden(['id', 'course_category_id', 'amount']);

        $examTime = match ($class->exam_condition) {
            OrgClass::EXAM_CONDITION_ANYTIME => -1,
            OrgClass::EXAM_CONDITION_FIXED_TIME => Carbon::parse($class->exam_at)->toDateString(),
            OrgClass::EXAM_CONDITION_AFTER_COURSE => '课程结束后考试',
            default => throw new ServiceException("考试类型有误"),
        };
        $topic = \App\Services\Topic\TopicService::examConfig($topic->id, $enrollment->org_id);
        $examConfig = $topic->exam_config ? $topic->exam_config : Topic::examConfig();
        $topicCount = collect($examConfig)->sum('count');
        $data = [
            'sid' => $enrollment->sid,
            'exam_taken' => $enrollment->exam_taken,
            'score' => $enrollment->exam_score,
            'can_exam' => true,
            'reason' => 'success',
            'exam_limit_count' => $class->exam_limit ? $class->exam_limit_count : -1,
            'exam_time' => $examTime,
            'exam_mode' => $class->exam_mode,
            'limit_time' => $topic->exam_time,
            'pass_score' => $topic->pass_score,
            'topic_count' =>$topicCount,
            'total_score' =>$topic->total_score,
            'topic' => $topic
        ];

        if ($class->exam_mode == OrgClass::EXAM_MODE_PC_ONLY && $platform == 'miniProgram') {
            $data['can_exam'] = false;
            $data['reason'] = '仅支持PC端考试';

            return $data;
        } elseif ($class->exam_mode == OrgClass::EXAM_MODE_MOBILE_ONLY && $platform == 'pc') {
            $data['can_exam'] = false;
            $data['reason'] = '仅支持移动端考试';

            return $data;
        }

        if ($class->exam_condition == OrgClass::EXAM_CONDITION_FIXED_TIME) {
            $now = Carbon::now();
            $starTime = Carbon::parse($class->exam_at)->startOfDay();
            $endTime = Carbon::parse($class->exam_at)->endOfDay();
            if (!$now->between($starTime, $endTime)) {
                $data['can_exam'] = false;
                $data['reason'] = "当前时间不在考试时间内";

                return $data;
            }
        } elseif ($class->exam_condition == OrgClass::EXAM_CONDITION_AFTER_COURSE) {
            $courseHour = Course::query()->where('course_id', $class->resource->content_id)->value('hour');
            $enrollment->resource->orgAppend($enrollment->org_id, $enrollment->id);
            $studyProgress = $enrollment->resource->study_progress;

            if ($studyProgress['study_hour'] < $courseHour) {
                $data['can_exam'] = false;
                $data['reason'] = "当前学时为{$studyProgress['study_hour']}，需修满{$courseHour}学时可考试";

                return $data;
            }
        }

        $tests = Test::query()
            ->where('user_id', $user->id)
            ->where('topic_id', $enrollment->resource->topic_id)
            ->where('org_id', $enrollment->org_id)
            ->where('enroll_id', $enrollment->id)
            ->where('type', Test::TYPE_EXAM)
            ->where('status', Test::STATUS_STOP)
            ->get('passed');
        $testCount = $tests->count();

        if ($enrollment->exam_retake == Enrollment::RETAKE_CAN) {// 可补考
            $data['can_exam'] = true;
            $data['reason'] = "success";

            return $data;
        }

        if ($enrollment->exam_passed) {
            $data['can_exam'] = false;
            $data['reason'] = "您考试已经及格了，无需再考了";
            return $data;
        }

        if ($class->exam_limit) {
            $lastTestCount = $testCount >= $class->exam_limit_count ? 0 : $class->exam_limit_count - $testCount;
            if ($lastTestCount == 0) {
                $data['can_exam'] = false;
                $data['reason'] = "非常遗憾，您已没有考试机会了";

                return $data;
            }
        }

        return $data;
    }

    /**
     * 获取抓拍二维码
     *
     * @param int $userId 用户ID
     * @param int $orgId 机构ID
     * @param int $contentId 课程ID
     * @param int $sectionId 课程节ID
     * @param Collection<ContentCourseChapter> $chapters 课程章集合
     * @return array
     */
    public static function getScene(int $userId, int $orgId, int $contentId, int $sectionId, Collection $chapters): array
    {
        $data = ['scene' => '', 'remain' => 0, 'sectionIds' => []];
        $enrollment = EnrollmentService::getCourseEnrollment($orgId, $userId, $contentId);
        if (!$enrollment) {
            return $data;
        }

        $class = $enrollment->classroom;
        if (!$class || !$class->face_capture_enabled) {
            return $data;
        }

        $sectionCaptureCount = LearnCapture::query()
            ->where('org_id', $enrollment->org_id)
            ->where('enroll_id', $enrollment->id)
            ->where('user_id', $enrollment->user_id)
            ->where('course_id', $contentId)
            ->where('section_id', $sectionId)
            ->count();
        if ($sectionCaptureCount >= $class->face_capture_count) {
            return $data;
        }

        $sectionCount = 0;
        $chapters->each(function ($chapter) use (&$sectionCount) {
            /** @var ContentCourseChapter $chapter */
            $sectionCount += $chapter->sections->count();
        });

        // 所需拍照次数 = 课程节数若大于等于设置拍照次数则为设置拍照次数否则为课程节数
        $faceCaptureCount = min($sectionCount, $class->face_capture_count);

        $sectionIds = LearnCapture::query()
            ->where('org_id', $enrollment->org_id)
            ->where('enroll_id', $enrollment->id)
            ->where('user_id', $enrollment->user_id)
            ->where('course_id', $contentId)
            ->pluck('section_id')
            ->toArray();

        $data['sectionIds'] = $sectionIds;

        $remain = $faceCaptureCount - count($sectionIds);

        if ($remain <= 0) {
            return $data;
        }
        $data['remain'] = $remain;

        if (in_array($sectionId, $sectionIds)) {
            return $data;
        }

        $data['scene'] = CaptureService::makeCode($enrollment->id, $contentId, $sectionId);

        return $data;
    }

    public static function create(int $orgId, array $params): OrgClass
    {
        $class = new OrgClass();
        $class->org_id = $orgId;
        $class->status = OrgClass::STATUS_STARTING;

        foreach ($params as $key => $val) {
            if (in_array($key, $class->getFillable())) {
                $class->{$key} = $val;
            }
        }

        if ($class->start_at && $class->start_at > Carbon::now()) {
            $class->status = OrgClass::STATUS_DEFAULT;
        }

        $class->save();

        Org::query()->where('id', $orgId)->increment('total_classes');

        return $class;
    }

    public static function update(int $id, int $orgId, array $params): OrgClass
    {
        /** @var OrgClass $class */
        $class = OrgClass::query()->where('org_id', $orgId)->find($id);

        if (!$class) {
            throw new ServiceException('班级不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $class->getFillable())) {
                $class->{$key} = $val;
            }
        }

        $class->save();

        return $class;
    }

    public static function remove($id, int $orgId): void
    {
        /** @var OrgClass $class */
        $class = OrgClass::query()->where('org_id', $orgId)->find($id);

        if (!$class) {
            throw new ServiceException('班级不存在');
        }

        if (Enrollment::query()->where('class_id', $class->id)->exists()) {
            throw new ServiceException('班级已分配学员了');
        }

        $class->delete();

        Org::query()->where('id', $orgId)->decrement('total_classes');
    }

    public static function finished($id, int $orgId): OrgClass
    {
        $class = OrgClass::query()
            ->with(['resource'])
            ->where('org_id', $orgId)
            ->find($id);

        if (!$class) {
            throw new ServiceException('班级不存在');
        }

        $class->status = OrgClass::STATUS_FINISHED;
        $class->actual_end_at = Carbon::now();
        $class->save();
        // 获取及格分数
        $passScore = $class->exam_enabled ? TopicService::getPassScore($class) : 0;

        // 处理机构班级结束逻辑
        DB::transaction(function () use ($class, $orgId, $passScore) {
            // 更新机构班级状态
            $class->status = OrgClass::STATUS_FINISHED;
            $class->save();

            // 更新机构学员状态为已完成或未完成
            Enrollment::query()
                ->where('class_id', $class->id)
                ->whereIn('status', [Enrollment::STATUS_LEARNING, Enrollment::STATUS_PENDING])
                ->chunkById(100, function ($enrollments) use ($passScore, $class) {
                    /** @var Enrollment $enrollment */
                    foreach ($enrollments as $enrollment) {
                        if (
                            (!$class->exam_enabled && $enrollment->learn_finished)
                            || ($class->exam_enabled && $enrollment->exam_score >= $passScore)
                        ) {
                            // 考试通过或不需要考试的，都是已完成
                            $enrollment->status = Enrollment::STATUS_COMPLETED;
                        } else {
                            // 考试未通过或未考试，都是未完成
                            $enrollment->status = Enrollment::STATUS_INCOMPLETE;
                        }

                        $enrollment->save();
                    }
                });

            // 更新机构培训数
            Org::query()->where('id', $orgId)->increment('total_trained');
        });

        return $class;
    }

    public static function getHour(OrgClass $class): int
    {
        $hour = 0;

        if ($class->type == Enrollment::TYPE_COURSE) {
            $hour = Course::publicFields()
                ->where('org_id', $class->org_id)
                ->where('course_id', $class->resource_id)
                ->value('hour');
        } elseif ($class->type == Enrollment::TYPE_COURSE_PACK) {
            $hour = CoursePackService::calCoursePackHour($class->org_id, $class->resource_id);
        }

        return $hour;
    }
}
