<?php

namespace App\Services\Org;

use App\Jobs\CourseUpdateJob;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Org\Course;
use App\Services\Cms\ContentCourseService;
use App\Libs\Utils\Helpers;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePackList;
use App\Models\Cms\ContentCourseProgress;
use App\Models\Cms\ContentCourseSection;
use App\Models\Cms\ContentCourseStatus;
use App\Models\Org\EnrollCourse;
use App\Models\Org\Enrollment;
use App\Models\Org\OrgClass;
use App\Services\Cms\ContentCourseProgressService;
use Illuminate\Support\Facades\Log;

class CourseService
{

    /**
     * 添加机构课程
     *
     * @param int $orgId
     * @param array $courseIds
     * @param array $coursesData
     * @return mixed
     */
    public static function add(int $orgId, array $courseIds, array $coursesData = [])
    {
        foreach ($courseIds as $courseId) {
            $model = new Course();
            $model->org_id = $orgId;
            $model->course_id = $courseId;
            $model->price_original = $coursesData[$courseId]['price'] ?? 0;
            $model->price_sell = $coursesData[$courseId]['price'] ?? 0;
            $model->save();  // 只有 save 会触发 created 事件
        }

        return true;
    }

    /**
     * 通过课程包 ID 添加课程
     * @param int $oid
     * @param int $coursePackId
     */
    public static function addByPackId(int $oid, int $coursePackId)
    {
        $courseIds = ContentCoursePackList::query()->where('content_id', $coursePackId)->pluck('course_id')->toArray();
        $inOrgCourseIds = [];
        
        $totalHour = 0;
        $totalDuration = 0;

        $contentCourses = ContentCourse::query()
            ->select(['hour_per_minutes', 'content_id'])
            ->whereIn('content_id', $courseIds)
            ->get()
            ->keyBy('content_id');

        // 机构已存在的课程
        $inOrgCourseIds = Course::query()
            ->where('org_id', $oid)
            ->whereIn('course_id', $courseIds)
            ->pluck('course_id')
            ->toArray();

        foreach ($inOrgCourseIds as $courseId) {
            list($courseHour, $courseDuration) = self::getCourseHourAndDuration($contentCourses->get($courseId), $oid);
            $totalHour += $courseHour;
            $totalDuration += $courseDuration;
        }

        $notInOrgCourseIds = array_diff($courseIds, $inOrgCourseIds);

        // 添加课程
        if (!empty($notInOrgCourseIds)) {
            foreach ($notInOrgCourseIds as $courseId) {
                $contentCourse = $contentCourses->get($courseId);
                $price = $contentCourse->content?->charge_amount ?? 0;

                $model = new Course();
                $model->org_id = $oid;
                $model->course_id = $contentCourse->content_id;
                $model->price_original = $price;
                $model->price_sell = $price;
                $model->status = Course::STATUS_HIDDEN;
                $model->save();

                $totalDuration += $model->duration;
                $totalHour += $contentCourse->studyHour($model->duration);
            }
        }

        return [(int)$totalHour, $totalDuration];
    }

    /**
     * 更新机构课程价格
     *
     * @param int $orgId
     * @param array $coursesData
     * @return void
     */
    public static function updatePrices(int $orgId, array $coursesData)
    {
        foreach ($coursesData as $courseId => $data) {
            Course::query()->where('org_id', $orgId)
                ->where('course_id', $courseId)
                ->update([
                    'price_original' => $data['price']
                ]);
        }
    }

    /**
     * 课程信息
     *
     * @param int $orgId
     * @param int $courseId
     * @return Course|null
     */
    public static function getCourse(int $orgId, int $courseId): ?Course
    {
        return Course::publicFields()
            ->where('org_id', $orgId)
            ->where('course_id', $courseId)
            ->first();
    }

    public static function getOrgCourses(int $orgId): array
    {
        $list = Course::query()
            ->with(['contentCourse', 'contentCourse.content', 'contentCourse.topic' => function ($query) {
                $query->select(['id', 'name']);
            }])
            ->where('org_id', $orgId)
            ->where('status', Course::STATUS_VISIBLE)
            ->get();

        $courses = [];

        /** @var Course $item */
        foreach ($list as $item) {
            $topic = $item->contentCourse->topic;
            if ($topic) {
                $topic->setHidden([]);
            }

            $courses[] = [
                'id' => $item->id,
                'course_id' => $item->course_id,
                'name' => $item->contentCourse->content->title,
                'price_original' => $item->price_original,
                'price_sell' => $item->price_sell,
                'topic' => $topic,
            ];
        }

        return $courses;
    }
    /**
     * 课程章节操作
     *
     * @param int $orgId
     * @param int $courseId
     * @param string $type  // chapter|section
     * @param int $resourceId
     * @param string $operate // open|close
     * @return void
     */
    public static function operate(int $orgId, int $courseId, string $type, int $resourceId, string $operate): void
    {
        $course = Course::query()->where('org_id', $orgId)->where('course_id', $courseId)->first();
        $lessons = json_decode($course->lessons, true) ?? [];

        foreach ($lessons as &$chapter) {
            if ($type == 'chapter' && $chapter['id'] == $resourceId) {
                $chapter['enabled'] = $operate == 'open' ? true : false;
                break;
            }

            if ($type == 'section' && $chapter['sections']) {
                foreach ($chapter['sections'] as &$section) {
                    if ($section['id'] == $resourceId) {
                        $section['enabled'] = $operate == 'open' ? true : false;
                        break;
                    }
                }
            }
        }
        
        $course->lessons = json_encode($lessons);
        $course->save();

        CourseUpdateJob::dispatch($courseId, $orgId);
    }
    /**
     * 更新学员学习时长
     *
     * @param int $orgId
     * @param int $contentId 课程id
     * @param int $userId
     * @param Enrollment $enrollment 学员分配记录
     * @return void
     */
    public static function updateLearnedDuration(int $orgId, int $contentId, int $userId, int $enrollId): void
    {
        $chapterSections = self::getResourceTree($orgId, $contentId);
        if (empty($chapterSections)) {
            return;
        }

        /** @var Collection<ContentCourseProgress> $progresses */
        $progresses = ContentCourseProgressService::getCourseProgress($userId, $contentId, $orgId, $enrollId);
        if ($progresses->isEmpty()) {
            return;
        }

        $validDuration = 0;
        $duration = 0;
        $ids = [];
        // 过滤掉非可见章节记录
        foreach ($chapterSections as $chapter) {
            foreach ($chapter['children'] as $section) {
                /** @var ContentCourseProgress|null $progress */
                $progress = $progresses->get($section['id']);

                if (!$progress) {
                    continue;
                }

                $validDuration += $progress->valid_duration;
                $duration += $progress->duration;

                if ($progress->progs_status_id == 0) {
                    $ids[] = $progress->id;
                }
            }
        }
        
        $contentCourse = ContentCourse::query()->where('content_id', $contentId)->first();
        list($courseHour, $courseDuration) = self::getCourseHourAndDuration($contentCourse, $orgId);
        list($studyHour, $studyPercent) = $contentCourse->studyHourForPercent($validDuration, $courseHour);

        /** 更新课程学习进度 */
        $progsStatus = ContentCourseStatus::query()->firstOrCreate([
            'org_id' => $orgId,
            'user_id' => $userId,
            'content_id' => $contentId,
            'enroll_id' => $enrollId,
        ], [
            'finished' => false
        ]);

        $progsStatus->update([
            'duration' => $duration,
            'valid_duration' => $validDuration,
            'hour' => $studyHour,
            'percent' => $studyPercent,
        ]);

        if (!$progsStatus->finished && $studyHour >= (int)$courseHour) {
            $validDuration ++;

            $progsStatus->finished = true;
            $progsStatus->valid_duration = $validDuration;
            $progsStatus->percent = 100;
            $progsStatus->save();
        }
        
        if (!empty($ids)) {
            ContentCourseProgress::whereIn('id', $ids)->update(['progs_status_id' => $progsStatus->id]);
        }

        if ($enrollId == 0) {
            return;
        }

        $enrollment = Enrollment::find($enrollId);

        /** 更新报名课程状态 */
        /** @var EnrollCourse $enrollCourse */
        $enrollCourse = EnrollCourse::query()->firstOrCreate([
            'org_id' => $orgId,
            'enroll_id' => $enrollment->id,
            'course_id' => $contentId,
        ], [
            'learned_duration' => 0,
            'learn_finished' => false,
        ]);

        $enrollCourse->learned_duration = $validDuration;

        if (!$enrollCourse->learn_finished && $contentCourse->studyHour($validDuration, false) >= (int)$courseHour) {
            $enrollCourse->learn_finished = true;
        }

        $enrollCourse->save();

        /** 更新报名表 */
        if ($enrollment->type == Enrollment::TYPE_COURSE_PACK) {
            // 查询所有子课程
            $coursePackLearnedDuration = EnrollCourse::query()
                ->where('org_id', $orgId)
                ->where('enroll_id', $enrollment->id)
                ->sum('learned_duration');

            $coursePackLearnedFinished = EnrollCourse::query()
                ->where('org_id', $orgId)
                ->where('enroll_id', $enrollment->id)
                ->where('learn_finished', 0)
                ->exists();

            $enrollment->learned_duration = $coursePackLearnedDuration;
            $enrollment->learn_finished = !$coursePackLearnedFinished;
        } elseif ($enrollment->type == Enrollment::TYPE_COURSE) {
            $enrollment->learned_duration = $enrollCourse->learned_duration;
            $enrollment->learn_finished = $enrollCourse->learn_finished;

            // 报名完成: 没有考试的班 或 考试已完成且通过
            if ($enrollment->learn_finished && $enrollment->classroom
                && ($enrollment->classroom->exam_enabled == 0 || $enrollment->exam_passed == 1)) 
            {
                $enrollment->status = Enrollment::STATUS_COMPLETED;
            }
        }

        $enrollment->save();

        if ($enrollment->class_id) {
            $learnFinishedCount = Enrollment::query()
                ->where('class_id', $enrollment->class_id)
                ->where('learn_finished', true)
                ->count();

            OrgClass::query()
                ->where('id', $enrollment->class_id)
                ->update(['total_course_finished' => $learnFinishedCount]);
        }
    }
    /**
     * 只更新Lessons, 不计算学时相关
     */
    public static function updateCourseLessons($operate, $addSection, $addChapter) {

        $courseId = $addSection?->content_id ?? $addChapter?->content_id;
        $courses = Course::query()->where('course_id', $courseId)->get();

        foreach ($courses as $course) { 
            $lessons = json_decode($course->lessons, true) ?? [];

            // 新增章
            if ($addChapter && $operate == 'add') {
                
                $lessons[] = [
                    'id' => $addChapter->id,
                    'hour' => 0,
                    'enabled' => false,
                    'duration' => 0,
                    'sections' => []
                ];

            } else {
                foreach ($lessons as $k => &$chapter) {

                    // 删除章 || 更新章
                    if ($addChapter && $chapter['id'] == $addChapter->id) {
                        if ($operate == 'del') {
                            unset($lessons[$k]);
                        } else {
                            $chapter['enabled'] = $addChapter->status == 1 ? true : false;
                        }
                    }

                    // 删除节 || 更新节 || 添加节
                    if ($addSection && $chapter['id'] == $addSection->chapter_id) {
                        switch ($operate) {
                            case 'add':
                                $chapter['sections'][] = [
                                    'id' => $addSection->id,
                                    'hour' => 0,
                                    'enabled' => false,
                                    'duration' => $addSection->duration,
                                ];
                                break;
                                
                            case 'del':
                            case 'update':
                                foreach ($chapter['sections'] as $key => &$sec) {

                                    if ($sec['id'] == $addSection->id) {

                                        if ($operate == 'del') {
                                            unset($chapter['sections'][$key]);
                                        } else {
                                            $sec['duration'] = $addSection->duration;
                                            $sec['enabled'] = $addSection->status == 1 ? true : false;
                                            if (!$sec['enabled']) {
                                                $sec['hour'] = 0;
                                            }
                                        }
                                        break;
                                    }
                                }
                                break;
                        }
                    }
                }
            }

            $course->lessons = json_encode($lessons);
            $course->save();
        }

        if ($addSection) {
            $flag = $addSection->status != 1;    
        } else {
            $flag = $addChapter->status != 1;
        }

        // 新增或删除未开启的章节, 无需修改其他表的有效课时
        if ($operate == 'add' || ($operate == 'del' && $flag)) {
            return;
        }

        CourseUpdateJob::dispatch($courseId);
    }

    /**
     * 机构课程可用(显示&&开启) 章|节的id集合
     *
     * @param int $orgId 机构ID
     * @param int $courseId 课程ID
     * @return array
     */
    public static function orgCourseChapSecIds(int $orgId, int $courseId): array
    {
        return self::_processCourseLessons($orgId, $courseId, function($lessons, $chapterMap, $sectionMap) {
            $orgChapterIds = [];
            $orgSectionIds = [];
                        
            if (empty($lessons)) {
                return [$orgChapterIds, $orgSectionIds];
            }

            foreach ($lessons as $chapter) {
                if ($chapter['enabled'] && isset($chapterMap[$chapter['id']])) {
                    $orgChapterIds[] = $chapter['id'];
                }

                if (empty($chapter['sections'])) {
                    continue;
                }

                foreach ($chapter['sections'] as $section) {
                    if ($section['enabled'] && isset($sectionMap[$section['id']])) {
                        $orgSectionIds[] = $section['id'];
                    }
                }
            }

            return [$orgChapterIds, $orgSectionIds];
        });
    }

    /**
     * 获取指定机构课程的章节资源树
     *
     * @param int $orgId
     * @param int $courseId
     * @param bool $wrapSn 是否附加序号
     * @return Collection<ContentCourseChapter> 获取到的章节只包含机构选中的 sections 关联
     */
    public static function getResourceTree(int $orgId, int $courseId, bool $wrapSn=false)
    {
        return self::getCoursesResourceTree($orgId, [$courseId], $wrapSn)->get($courseId);
    }
    
    /**
     * 获取指定机构或者平台课程的章节资源树
     *
     * @param int $orgId
     * @param array $courseIds
     * @param bool $wrapSn 是否附加序号
     * @return 
     */
    public static function getCoursesResourceTree(int $orgId, array $courseIds, bool $wrapSn = false)
    {
        $result = collect();

        foreach ($courseIds as $courseId) {
            $tree = [];

            if ($orgId > 0) {

                $tree = self::children($orgId, $courseId, true);

            } else {
                $contentCourse = ContentCourse::where('content_id', $courseId)->with([
                    'chapters' => function ($query) {
                        $query->where('status', ContentCourseChapter::STATUS_SHOW)->orderByRaw('sort desc,id asc');;
                    },
                    'chapters.sections' => function ($query) {
                        $query->where('status', ContentCourseSection::STATUS_SHOW)->orderByDesc('sort');
                    },
                    'topic'
                ])->first();

                if (!$contentCourse) {
                    continue;
                }

                $chapters = [];

                foreach ($contentCourse->chapters as $chapter) {
                    $sections = [];
                    $totalSectionDuration = 0;

                    foreach ($chapter->sections as $section) {
                        $sections[] = [
                            'id' => $section->id,
                            'enabled' => true,
                            'hour' => $contentCourse->studyHour($section->actual_duration),
                            'duration' => $section->actual_duration ?? 0
                        ];
                        $totalSectionDuration += $section->actual_duration;
                    }

                    $totalChapterHour = $contentCourse->studyHour($totalSectionDuration);

                    $chapters[] = [
                        'id' => $chapter->id,
                        'enabled' => true,
                        'hour' => (string)$totalChapterHour,
                        'duration' => (string)$totalSectionDuration,
                        'sections' => $sections,
                        'children' => $sections // 暂时兼容, 后面再统一改为 children
                    ];
                }

                $tree = $chapters;
            }

            $wrapSn && $tree = self::wrapSn($tree);

            $result->put($courseId, $tree);
        }

        return $result;
    }

    /**
     * 获取指定机构课程的可见章节资源树
     *
     * @param int $orgId
     * @param int $courseId
     * @param bool $needEnable 是否仅获取已开启的
     * @return array
     */
    public static function children($orgId, $courseId, $needEnable = false): array
    {
        return self::_processCourseLessons($orgId, $courseId, function ($lessons, $chapterMap, $sectionMap) use ($courseId, $needEnable) { 
            $chapters = [];

            if (empty($lessons)) {
                return $chapters;
            }

            foreach ($lessons as $chapter) {
                if (!isset($chapterMap[$chapter['id']])) {
                    continue;
                }

                if ($needEnable && $chapter['enabled'] == false) {
                    continue;
                }

                $chapterData = [
                    'id' => $chapter['id'],
                    'index' => strval($chapter['id']),
                    'course_id' => $courseId,
                    'type' => 'chapter',
                    'name' => $chapterMap[$chapter['id']],
                    'status' => $chapter['enabled'],
                    'enabled' => $chapter['enabled'],
                    'hour' => $chapter['hour'],
                    'duration' => $chapter['duration'],
                    'sections' => [],
                    'children' => []    // 兼容前端
                ];

                if ($chapter['sections']) {
                    foreach ($chapter['sections'] as $section) {
                        if (!isset($sectionMap[$section['id']])) {
                            continue;
                        }

                        if ($needEnable && $section['enabled'] == false) {
                            continue;
                        }

                        $sectionData = [
                            'id' => $section['id'],
                            'index' => $chapter['id'] . ":" . $section['id'],
                            'course_id' => $courseId,
                            'type' => 'section',
                            'name' => $sectionMap[$section['id']],
                            'status' => $section['enabled'],
                            'enabled' => $section['enabled'],
                            'hour' => $section['hour'],
                            'duration' => $section['duration'],
                        ];

                        $chapterData['sections'][] = $sectionData;
                        $chapterData['children'][] = $sectionData;
                    }
                }

                $chapters[] = $chapterData;
            }

            return $chapters;
        });
    }
    
    /**
     * 处理课程章节数据
     *
     * @param int $orgId 机构ID
     * @param int $courseId 课程ID
     * @param callable $processor 处理回调函数
     * @return mixed 回调函数的返回值
     */
    private static function _processCourseLessons(int $orgId, int $courseId, callable $processor)
    {
        list($chapterMap, $sectionMap) = ContentCourseService::getShowResourceIds($courseId);
        $course = Course::query()->where('org_id', $orgId)->where('course_id', $courseId)->first();
        $lessons = isset($course->lessons) ? json_decode($course->lessons, true) : [];

        return $processor($lessons, $chapterMap, $sectionMap);
    }

    /**
     * 为章节列表包裹上序号
     *
     * 章和节会被自动附加上 sn 字段代表序号的表达文本
     *
     * @param array $chapters
     * @return void
     */
    public static function wrapSn($chapters): array
    {
        $autoChapterSn = 0;

        foreach ($chapters as &$chapter) {
            $chapter['sn'] = '第'.Helpers::numberToChinese(++$autoChapterSn).'章'; //章自动序号
            $autoSectionSn = 0;

            if (empty($chapter['sections'])) {
                continue;
            }

            foreach ($chapter['sections'] as &$section) {
                $section['sn'] = sprintf('%02d', ++$autoSectionSn); //节自动序号
            }
        }

        return $chapters;
    }

    /**
     * 获取课程学习时长信息
     *
     * 根据课程对象和机构ID获取课程的课时和时长。
     * 如果提供了有效的机构ID，则从机构课程中获取时长信息；
     * 否则直接使用传入课程对象的时长信息。
     *
     * @param ContentCourse $course 课程对象，包含课程基本信息
     * @param int $oid 机构ID
     * @return array 返回包含两个元素的数组：[课时(默认1位小数), 时长]
     */
    public static function getCourseHourAndDuration(ContentCourse $course, int $oid, $isDecimal = true): array
    {
        if ($oid > 0) {
            $orgCourse = self::getCourse($oid, $course->content_id);
            return [$course->studyHour($orgCourse->duration, $isDecimal), $orgCourse->duration];
        }

        return [$course->studyHour($course->duration, $isDecimal), $course->duration];
    }
}