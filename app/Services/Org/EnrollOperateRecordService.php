<?php

namespace App\Services\Org;

use App\Models\Org\EnrollOperateRecord;

/**
 * 报名流转记录
 */
class EnrollOperateRecordService
{
    public function create(int $enrollId, int $type, int $adminId = 0, string $remark = '')
    {
        try {
            EnrollOperateRecord::query()->create([
                'enroll_id' => $enrollId,
                'type' => $type,
                'admin_id' => $adminId,
                'remark' => $remark,
            ]);
        } catch (\Throwable $e) {
            logger()->error("创建报名操作记录失败", [
                'enroll_id' => $enrollId,
                'type' => $type,
                'admin_id' => $adminId,
                'remark' => $remark,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
