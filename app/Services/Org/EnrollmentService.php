<?php

namespace App\Services\Org;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Imports\Org\EnrollmentImport;
use App\Libs\Baidu\AipAPI;
use App\Models\Cms\Category;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePack;
use App\Models\Org;
use App\Models\Org\Course;
use App\Models\Org\CoursePack;
use App\Models\Org\Enrollment;
use App\Models\Org\OrgClass;
use App\Models\Org\Student;
use App\Models\Org\Topic;
use App\Models\Train\Test;
use App\Models\User\UserOwnContent;
use App\Models\User\UserOwnTopic;
use App\Services\Common\AttachmentService;
use App\Services\HttpClientService;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use phpDocumentor\Reflection\Types\Self_;
use Throwable;

class EnrollmentService
{
    /**
     * 课程类型报名表
     *
     * @param int $orgId 机构ID
     * @param int $userId 用户ID
     * @param int $contentId 课程ID
     * @return Enrollment|null
     */
    public static function getCourseEnrollment(int $orgId, int $userId, int $contentId): ?Enrollment
    {
        /** @var Enrollment $enrollment */
        $enrollment = Enrollment::query()
            ->where('org_id', $orgId)
            ->where('user_id', $userId)
            ->where('type', Enrollment::TYPE_COURSE)
            ->where('resource_id', $contentId)
            ->orderByDesc('id')
            ->first();

        return $enrollment;
    }

    /**
     * 创建报名信息
     * @param int $orgId
     * @param int $userId
     * @param int $studentId
     * @param int $classId 班级id
     * @param string $subjectType 科目
     * @param int $subjectId 科目id
     * @return Enrollment
     * @throws Throwable
     */
    public static function create(int $orgId, int $userId, int $studentId, int $classId = 0, string $subjectType = Enrollment::TYPE_COURSE, int $subjectId = 0): Enrollment
    {
        $enrollment = null;

        if ($classId > 0) {
            // 每个学员分配指定班级的报名信息，只能存在一条
            /** @var Enrollment $enrollment */
            $enrollment = Enrollment::query()
                ->where('org_id', $orgId)
                ->where('student_id', $studentId)
                ->where('class_id', $classId)
                ->where('resource_id', '>', 0)
                ->first();

            $class = OrgClass::query()->where('id', $classId)->first();
            $subjectId = $class?->resource_id ?? $subjectId;
            $subjectType = $class?->type ?? $subjectType;
        }

        // 创建报名信息
        if (!$enrollment) {
            $enrollment = self::createEnroll($orgId, $userId, $studentId);

            // 分班，开课
            if ($classId > 0 || $subjectId > 0) {
                try {
                    self::batchAssign($orgId, [$enrollment->id], $classId, $subjectType, $subjectId);
                } catch (Throwable $e) {
                    Log::channel('task')->info("enrollment assign subject fail", [
                        'id' => $enrollment->id,
                        'class_id' => $classId,
                        'type' => $subjectType,
                        'resource_id' => $subjectId,
                        'message' => $e->getMessage()
                    ]);

                    // 删除异常报名信息
                    $enrollment->delete();
                    throw $e;
                }
            }

            Org::query()->where('id', $orgId)->increment('total_students');
        }

        return $enrollment;
    }

    public static function createEnroll($orgId, $userId, $studentId, $type = Enrollment::TYPE_COURSE, $resourceId = 0): Enrollment
    {
        $enrollment = new Enrollment();
        $enrollment->org_id = $orgId;
        $enrollment->user_id = $userId;
        $enrollment->student_id = $studentId;
        $enrollment->type = $type;
        $enrollment->status = Enrollment::STATUS_PENDING;
        $enrollment->resource_id = $resourceId;
        $enrollment->save();

        Student::query()->where('id', $enrollment->student_id)->update(['latest_enroll_at' => now()]);

        return $enrollment;
    }

    public static function remove(int $id, int $orgId): void
    {
        /** @var Enrollment $enrollment */
        $enrollment = Enrollment::query()->where('org_id', $orgId)->find($id);

        if (!$enrollment) {
            throw new ServiceException('报名信息不存在');
        }

        if ($enrollment->status != Enrollment::STATUS_PENDING) {
            throw new ServiceException('学习已开始');
        }

        $enrollment->delete();

        Org::query()->where('id', $orgId)->decrement('total_students');
    }

    public static function examRetake(int $id, int $orgId): Enrollment
    {
        /** @var Enrollment $enrollment */
        $enrollment = Enrollment::query()->where('org_id', $orgId)->find($id);

        if (!$enrollment) {
            throw new ServiceException('报名信息不存在');
        }

        if ($enrollment->exam_passed) {
            throw new ServiceException('考试已通过');
        }

        $enrollment->exam_retake = 1;
        $enrollment->save();

        return $enrollment;
    }

    /**
     * 批量分配/更换操作
     * @param int $orgId 机构ID
     * @param array $enrollmentIds 选择的报名信息
     * @param int $classId 班级ID
     * @param string $subjectType 科目类型 topic: 题库, course: 课程, course_pack: 课程包
     * @param int $subjectId 科目id
     * @param string $operate 操作 assign:分配，changeClass|changeSubject:更改
     * @return void
     * @throws Throwable
     */
    public static function batchAssign(int $orgId, array $enrollmentIds, int $classId = 0, string $subjectType = '', int $subjectId = 0, string $operate = 'assign'): void
    {
        if (!in_array($operate, ['assign', 'changeClass', 'changeSubject'])) {
            throw new ServiceException('操作类型有误');
        }

        $info = self::getEnrollmentInfos($orgId, $enrollmentIds);

        $enrollentStatus = Enrollment::STATUS_LEARNING;

        // 分配班级
        if ($classId > 0) {
            $class = self::batchAssignClass($info, $classId, $operate);

            if ($class?->status == OrgClass::STATUS_DEFAULT) {
                $enrollentStatus = Enrollment::STATUS_PENDING;
            }
        }

        // 分配科目
        if ($subjectType && $subjectId > 0) {
            $days = 7;

            /** @var Enrollment $enrollment */
            foreach ($info['enrollments'] as $enrollment) {
                switch ($operate) {
                    case 'assign':
                        if ($enrollment->resource_id > 0) {
                            throw new ServiceException("学员【{$enrollment->student->name}】已分配科目");
                        }
                        break;
                    case 'changeClass':
                    case 'changeSubject':
                        if ($enrollment->resource_id <= 0) {
                            throw new ServiceException("学员【{$enrollment->student->name}】未分配科目");
                        }

                        if (!$enrollment->started_at || Carbon::parse($enrollment->started_at)->diffInDays(Carbon::now(), false) > $days) {
                            throw new ServiceException("学员【{$enrollment->student->name}】的学习时间已超过{$days}天");
                        }
                        break;
                }
            }

            switch ($subjectType) {
                case 'topic':
                    self::batchAssignTopic($info, $subjectId, $operate, $enrollentStatus);
                    break;
                case 'course':
                    self::batchAssignCourse($info, $subjectId, $operate, $enrollentStatus);
                    break;
                case 'course_pack':
                    self::batchAssignCoursePack($info, $subjectId, $operate, $enrollentStatus);
                    break;
                default:
                    throw new ServiceException('类型有误');
            }
        }
    }

    /**
     * 批量分配/更改班级
     * @param array $info
     * @param int $classId 班级ID
     * @param string $type 类型 assign:分配，changeClass|changeSubject:更改
     * @return void
     */
    protected static function batchAssignClass(array $info, int $classId, string $type = 'assign'): OrgClass
    {
        /** @var OrgClass $class */
        $class = OrgClass::query()->where('org_id', $info['org_id'])->find($classId);

        if (!$class) {
            throw new ServiceException('班级不存在');
        }

        if ($class->status == OrgClass::STATUS_FINISHED) {
            throw new ServiceException('班级已结束');
        }

        if (count($info['enrollment_topic_ids']) > 1) {
            throw new ServiceException('学员科目存在多个（题库）');
        }

        if (count($info['enrollment_course_ids']) > 1) {
            throw new ServiceException('学员科目存在多个（课程）');
        }

        if (count($info['enrollment_course_pack_ids']) > 1) {
            throw new ServiceException('学员科目存在多个（课程包）');
        }

        switch ($type) {
            case 'assign': // 分配班级
                if (!empty($info['enrollment_class_ids'])) {
                    throw new ServiceException('选中的学员部分已分配班级');
                }

                Enrollment::query()->where('org_id', $info['org_id'])->whereIn('id', $info['enrollment_ids'])->update(['class_id' => $classId]);

                // 更新分配班级的学员数量
                $class->total_enrollments = Enrollment::query()->where('class_id', $classId)->count();
                $class->save();
                break;

            case 'changeClass': // 修改班级
            case 'changeSubject':
                Enrollment::query()->where('org_id', $info['org_id'])->whereIn('id', $info['enrollment_ids'])->update(['class_id' => $classId]);

                // 更新修改班级的学员数量
                foreach ($info['enrollment_class_ids'] as $enrollmentClassId) {
                    OrgClass::query()->where('id', $enrollmentClassId)->update([
                        'total_enrollments' => Enrollment::query()->where('class_id', $enrollmentClassId)->count()
                    ]);
                }
                break;
        }

        return $class;
    }

    /**
     * 批量分配/更改课程
     * @param array $info
     * @param int $courseId 课程ID
     * @param string $type 类型 assign:分配，changeClass|changeSubject
     * @return void
     * @throws Throwable
     */
    protected static function batchAssignCourse(array $info, int $courseId, string $type = 'assign', string $enrollentStatus): void
    {
        /** @var Course $course */
        $course = Course::query()
            ->with(['contentCourse', 'contentCourse.content'])
            ->where('org_id', $info['org_id'])
            ->where('course_id', $courseId)
            ->first();

        if (!$course || !$course->contentCourse) {
            throw new ServiceException('课程不存在');
        }

        if ($course->price_original <= 0) {
            throw new ServiceException('课程未设置价格');
        }

        if (count($info['enrollment_class_ids']) > 1) {
            throw new ServiceException('选中的学员存在多个班级');
        }

        $startAt = Carbon::now();
        $expireAt = Carbon::now()->addMonths(UserOwnContent::ORG_EXPIRED_MOUTH_DEFAULT);

        try {
            DB::beginTransaction();

            $enrollmentIds = [];

            /** @var Enrollment $enrollment */
            foreach ($info['enrollments'] as $enrollment) {
                if (in_array($type, ['changeClass', 'changeSubject'])) {
                    // 更换的科目与现有的科目一致
                    if ($enrollment->type == Enrollment::TYPE_COURSE && $enrollment->resource_id == $courseId) {
                        continue;
                    }

                    self::removeEnrollmentOldSubject($enrollment);
                }

                $enrollmentIds[] = $enrollment->id;

                // 分配科目，收费
                BalanceService::consume(
                    $info['org_id'],
                    $course->price_original,
                    $enrollment->id,
                    self::generateBalanceRemark($enrollment, $course->contentCourse->content->title)
                );

                // 开通课程及关联题库
                $own = new UserOwnContent();
                $own->user_id = $enrollment->user_id;
                $own->content_id = $courseId;
                $own->expired_at = $expireAt;
                $own->org_id = $info['org_id'];
                $own->enroll_id = $enrollment->id;
                $own->classify = Category::CLASSIFY_COURSE;
                $own->save();

                if ($course->contentCourse && $course->contentCourse->topic_id > 0) {
                    UserOwnTopic::add($enrollment->user_id, $course->contentCourse->topic_id, $expireAt, $info['org_id'], $enrollment->id);
                }

                Student::query()->where('id', $enrollment->student_id)->update(['latest_start_at' => $startAt]);
            }

            Enrollment::query()->where('org_id', $info['org_id'])->whereIn('id', $enrollmentIds)->update([
                'type' => Enrollment::TYPE_COURSE,
                'resource_id' => $courseId,
                'status' => $enrollentStatus,
                'started_at' => $startAt,
                'expired_at' => $expireAt,
            ]);

            EnrollCourseService::batchSave($info['org_id'], $enrollmentIds, Enrollment::TYPE_COURSE, $courseId);

            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();

            throw new ServiceException("扣费失败，原因：{$e->getMessage()}", previous: $e);
        }
    }

    /**
     * 批量分配/更改课程包
     * @param array $info
     * @param int $coursePackId 课程包ID
     * @param string $type 类型 assign:分配，changeClass|changeSubject
     * @return void
     * @throws Throwable
     */
    protected static function batchAssignCoursePack(array $info, int $coursePackId, string $type = 'assign', string $enrollentStatus): void
    {
        /** @var CoursePack $coursePack */
        $coursePack = CoursePack::query()
            ->with(['contentCoursePack', 'contentCoursePack.content'])
            ->where('org_id', $info['org_id'])
            ->where('course_pack_id', $coursePackId)
            ->first();

        if (!$coursePack || !$coursePack->contentCoursePack) {
            throw new ServiceException('课程不存在');
        }

        if ($coursePack->price_original <= 0) {
            throw new ServiceException('课程未设置价格');
        }

        if (count($info['enrollment_class_ids']) > 1) {
            throw new ServiceException('选中的学员存在多个班级');
        }

        $startAt = Carbon::now();
        $expireAt = Carbon::now()->addMonths(9);

        try {
            DB::beginTransaction();

            $enrollmentIds = [];

            /** @var Enrollment $enrollment */
            foreach ($info['enrollments'] as $enrollment) {
                if (in_array($type, ['changeClass', 'changeSubject'])) {
                    // 更换的科目与现有的科目一致
                    if ($enrollment->type == Enrollment::TYPE_COURSE_PACK && $enrollment->resource_id == $coursePackId) {
                        continue;
                    }

                    self::removeEnrollmentOldSubject($enrollment);
                }

                $enrollmentIds[] = $enrollment->id;

                // 分配科目，收费
                BalanceService::consume(
                    $info['org_id'],
                    $coursePack->price_original,
                    $enrollment->id,
                    self::generateBalanceRemark($enrollment, $coursePack->contentCoursePack->content->title, Enrollment::TYPE_COURSE_PACK)
                );

                // 开通课程及关联题库
                $own = new UserOwnContent();
                $own->user_id = $enrollment->user_id;
                $own->content_id = $coursePackId;
                $own->expired_at = $expireAt;
                $own->org_id = $info['org_id'];
                $own->enroll_id = $enrollment->id;
                $own->classify = Category::CLASSIFY_COURSE_PACK;
                $own->save();

                if ($coursePack->contentCoursePack && $coursePack->contentCoursePack->topic_id > 0) {
                    UserOwnTopic::add($enrollment->user_id, $coursePack->contentCoursePack->topic_id, $expireAt, $info['org_id'], $enrollment->id);
                }

                Student::query()->where('id', $enrollment->student_id)->update(['latest_start_at' => $startAt]);
            }

            Enrollment::query()->where('org_id', $info['org_id'])->whereIn('id', $enrollmentIds)->update([
                'type' => Enrollment::TYPE_COURSE_PACK,
                'resource_id' => $coursePackId,
                'status' => $enrollentStatus,
                'started_at' => $startAt,
                'expired_at' => $expireAt,
            ]);

            EnrollCourseService::batchSave($info['org_id'], $enrollmentIds, Enrollment::TYPE_COURSE_PACK, $coursePackId);

            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();

            throw new ServiceException("扣费失败，原因：{$e->getMessage()}", previous: $e);
        }
    }

    /**
     * 批量分配/更改题库
     * @param array $info
     * @param int $topicId 题库ID
     * @param string $type 类型 assign:分配，changeClass|changeSubject
     * @return void
     * @throws Throwable
     */
    public static function batchAssignTopic(array $info, int $topicId, string $type = 'assign', string $enrollentStatus): void
    {
        /** @var Topic $topic */
        $topic = Topic::query()
            ->with(['topic'])
            ->where('org_id', $info['org_id'])
            ->where('topic_id', $topicId)
            ->first();

        if (!$topic || !$topic->topic) {
            throw new ServiceException('题库不存在');
        }

        if ($topic->price_original_30 <= 0) {
            throw new ServiceException('题库未设置价格');
        }

        if (count($info['enrollment_class_ids']) > 1) {
            throw new ServiceException('选中的学员存在多个班级');
        }

        $startAt = Carbon::now();
        $expireAt = Carbon::now()->addMonth();

        try {
            DB::beginTransaction();

            $enrollmentIds = [];

            /** @var Enrollment $enrollment */
            foreach ($info['enrollments'] as $enrollment) {
                if (in_array($type, ['changeClass', 'changeSubject'])) {
                    // 更换的科目与现有的科目一致
                    if ($enrollment->type == Enrollment::TYPE_TOPIC && $enrollment->resource_id == $topicId) {
                        continue;
                    }

                    self::removeEnrollmentOldSubject($enrollment);
                }

                $enrollmentIds[] = $enrollment->id;

                // 分配科目，收费
                BalanceService::consume(
                    $info['org_id'],
                    $topic->price_original_30,
                    $enrollment->id,
                    self::generateBalanceRemark($enrollment, $topic->topic->name, Enrollment::TYPE_TOPIC)
                );

                // 开通题库
                UserOwnTopic::add($enrollment->user_id, $topicId, $expireAt, $info['org_id'], $enrollment->id);

                Student::query()->where('id', $enrollment->student_id)->update(['latest_start_at' => $startAt]);
            }

            Enrollment::query()->where('org_id', $info['org_id'])->whereIn('id', $enrollmentIds)->update([
                'type' => Enrollment::TYPE_TOPIC,
                'resource_id' => $topicId,
                'status' => $enrollentStatus,
                'started_at' => $startAt,
                'expired_at' => $expireAt,
            ]);

            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();

            throw new ServiceException("扣费失败，原因：{$e->getMessage()}", previous: $e);
        }
    }

    /**
     * 批量移出班级
     * @param int $orgId
     * @param array $enrollmentIds 学员id
     * @return void
     */
    public static function batchRemoveClass(int $orgId, array $enrollmentIds): void
    {
        $info = self::getEnrollmentInfos($orgId, $enrollmentIds);

        /** @var Enrollment $enrollment */
        foreach ($info['enrollments'] as $enrollment) {
            // 只处理分配班级的学员
            if ($enrollment->class_id > 0) {
                Enrollment::query()->where('org_id', $orgId)->where('id', $enrollment->id)->update(['class_id' => 0]);

                OrgClass::query()->where('id', $enrollment->class_id)->update([
                    'total_enrollments' => Enrollment::query()->where('class_id', $enrollment->class_id)->count()
                ]);
            }
        }
    }

    /**
     * 批量移出科目
     * @param int $orgId
     * @param array $enrollmentIds
     * @return void
     * @throws Throwable
     */
    public static function batchRemoveSubject(int $orgId, array $enrollmentIds): void
    {
        $info = self::getEnrollmentInfos($orgId, $enrollmentIds);

        try {
            DB::beginTransaction();

            /** @var Enrollment $enrollment */
            foreach ($info['enrollments'] as $enrollment) {
                // 没选科目的，直接跳过
                if ($enrollment->resource_id <= 0) {
                    continue;
                }

                self::removeEnrollmentOldSubject($enrollment);
            }

            Enrollment::query()->where('org_id', $orgId)->whereIn('id', $enrollmentIds)->update([
                'type' => Enrollment::TYPE_COURSE,
                'resource_id' => 0,
                'status' => Enrollment::STATUS_PENDING,
                'started_at' => null,
                'expired_at' => null,
            ]);

            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();

            throw new ServiceException("移出科目失败，原因：{$e->getMessage()}", previous: $e);
        }
    }

    /**
     * 移除学员历史科目
     * @param Enrollment $enrollment
     * @return void
     * @throws Throwable
     */
    protected static function removeEnrollmentOldSubject(Enrollment $enrollment): void
    {
        switch ($enrollment->type) {
            case Enrollment::TYPE_TOPIC:
                UserOwnTopic::query()->where('enroll_id', $enrollment->id)->delete();
                break;

            case Enrollment::TYPE_COURSE:
            case Enrollment::TYPE_COURSE_PACK:
                EnrollCourseService::remove($enrollment);

                UserOwnContent::query()->where('enroll_id', $enrollment->id)->delete();
                break;
        }

        // 移出科目退款
        try {
            BalanceService::refund(
                $enrollment->org_id,
                $enrollment->id,
                self::generateBalanceRemark($enrollment, $enrollment->resource->name, $enrollment->type, 'refund')
            );
        } catch (Throwable $e) {
            Log::channel('task')->warning("移出科目退款失败：{$e->getMessage()}", ['enrollment_id' => $enrollment->id]);
            throw new ServiceException('学员自行购买的科目无法移出');
        }
    }

    protected static function generateBalanceRemark(Enrollment $enrollment, string $subjectName, string $subjectType = 'course', string $type = 'consume'): string
    {
        $subjectLabel = Enrollment::$typeDict[$subjectType];
        $typeLabel = $type == 'consume' ? '开通' : '关闭';

        return "{$enrollment->student->name} {$typeLabel}“{$subjectName}”$subjectLabel";
    }


    protected static function getEnrollmentInfos(int $orgId, array $enrollmentIds): array
    {
        $enrollments = Enrollment::query()
            ->with(['classroom', 'resource', 'student'])
            ->where('org_id', $orgId)
            ->whereIn('id', $enrollmentIds)
            ->get();

        if ($enrollments->count() != count($enrollmentIds)) {
            Log::channel('task')->info("enrollment select fail", ['org_id' => $orgId, 'enrollment_ids' => $enrollmentIds]);
            throw new ServiceException('请重新选择学员');
        }

        /** @var Enrollment $enrollment */
        foreach ($enrollments as $enrollment) {
            if (in_array($enrollment->status, [Enrollment::STATUS_COMPLETED, Enrollment::STATUS_EXPIRED])) {
                throw new ServiceException("学员【{$enrollment->student->name}】学业已完成");
            }

            if (!$enrollment->user_id) {
                throw new ServiceException("学员【{$enrollment->student->name}】未绑定用户");
            }

            $enrollment->resource = $enrollment->toResource();
        }

        // 学员已选择的课程、课程包、题库、班级
        $enrollmentTopicIds = [];
        $enrollmentCourseIds = [];
        $enrollmentCoursePackIds = [];
        $enrollmentClassIds = [];
        $classTopicIds = [];
        $classCourseIds = [];
        $classCoursePackIds = [];

        /** @var Enrollment $enrollment */
        foreach ($enrollments as $enrollment) {
            // 学员课程、题库
            if ($enrollment->type && $enrollment->resource_id > 0) {
                if ($enrollment->type == Enrollment::TYPE_COURSE) {
                    $enrollmentCourseIds[] = $enrollment->resource_id;
                } elseif ($enrollment->type == Enrollment::TYPE_TOPIC) {
                    $enrollmentTopicIds[] = $enrollment->resource_id;
                } elseif ($enrollment->type == Enrollment::TYPE_COURSE_PACK) {
                    $enrollmentCoursePackIds[] = $enrollment->resource_id;
                }
            }

            // 学员班级
            if ($enrollment->class_id > 0) {
                $enrollmentClassIds[] = $enrollment->class_id;

                $class = $enrollment->classroom;

                if ($class && $class->type && $class->resource_id > 0) {
                    if ($class->type == OrgClass::TYPE_COURSE) {
                        $classCourseIds[] = $class->resource_id;
                    } elseif ($class->type == OrgClass::TYPE_TOPIC) {
                        $classTopicIds[] = $class->resource_id;
                    } elseif ($class->type == OrgClass::TYPE_COURSE_PACK) {
                        $classCoursePackIds[] = $enrollment->resource_id;
                    }
                }
            }
        }

        !empty($enrollmentTopicIds) && $enrollmentTopicIds = array_unique($enrollmentTopicIds);
        !empty($enrollmentCourseIds) && $enrollmentCourseIds = array_unique($enrollmentCourseIds);
        !empty($enrollmentCoursePackIds) && $enrollmentCoursePackIds = array_unique($enrollmentCoursePackIds);
        !empty($enrollmentClassIds) && $enrollmentClassIds = array_unique($enrollmentClassIds);
        !empty($classCourseIds) && $classCourseIds = array_unique($classCourseIds);
        !empty($classTopicIds) && $classTopicIds = array_unique($classTopicIds);
        !empty($classCoursePackIds) && $classCoursePackIds = array_unique($classCoursePackIds);

        return [
            'org_id' => $orgId,
            'enrollment_ids' => $enrollmentIds,
            'enrollments' => $enrollments,
            'enrollment_class_ids' => $enrollmentClassIds,
            'enrollment_course_ids' => $enrollmentCourseIds,
            'enrollment_course_pack_ids' => $enrollmentCoursePackIds,
            'enrollment_topic_ids' => $enrollmentTopicIds,
            'class_course_ids' => $classCourseIds,
            'class_course_pack_ids' => $classCoursePackIds,
            'class_topic_ids' => $classTopicIds,
        ];
    }

    /**
     * 获取搜索查询
     *
     * @param array $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function getSearchQuery(array $search)
    {
        $query = Enrollment::query();

        if (isset($search['id']) && $search['id']) {
            $query->where('id', $search['id']);
        }

        if (isset($search['class_id']) && $search['class_id']) {
            $query->where('class_id', $search['class_id']);
        }

        if (isset($search['status']) && $search['status']) {
            $query->where('status', $search['status']);
        }

        if (isset($search['type']) && isset($search['resource_id']) && $search['type'] && $search['resource_id']) {
            $query->where('type', $search['type']);
            $query->where('resource_id', $search['resource_id']);
        }

        if (isset($search['org_id']) && $search['org_id']) {
            $query->where('org_id', $search['org_id']);
        }

        if (isset($search['enrollment_ids']) && $search['enrollment_ids']) {
            $query->whereIn('id', $search['enrollment_ids']);
        }

        return $query;
    }

    /**
     * 学员学习信息
     *
     * @param Enrollment $enrollment
     * @return array
     */
    public static function userStudyInfo(Enrollment $enrollment): array
    {
        /** @var Test $test */
        $test = Test::query()
            ->where('enroll_id', $enrollment->id)
            ->where('type', Test::TYPE_EXAM)
            ->orderByDesc('score')
            ->first();

        $hour = EnrollCourseService::getHour($enrollment);
        $studyHour = EnrollCourseService::getStudyHour($enrollment);
        $enrollment->resource?->orgAppend($enrollment->org_id, $enrollment->id);
        $studyHour > $hour && $studyHour = number_format($hour, 1, '.', '');
        $score = $test ? $test->score : '';
        $testLevel = $test ? $test->test_level : '';

        if ($studyHour >= $hour) {
            if ($test) {
                $finish = $testLevel == '及格' ? '已完成' : '未完成';
            } else {
                $finish = '已完成';
            }
        } else {
            $finish = '未完成';
        }

        return [$hour, $studyHour, $score, $testLevel, $finish];
    }

    /**
     * 获取指定扩展字段的值
     *
     * @param Enrollment $enrollment
     * @param string $name
     * @return string
     */
    public static function getExtraValue(Enrollment $enrollment, string $name): string
    {
        $filter = array_filter($enrollment->student->extra, fn($item) => $item['name'] === $name);

        return !empty($filter) ? array_values($filter)[0]['value'] : '无';
    }

    /**
     * 导入学员信息
     *
     * @param int $orgId 机构ID
     * @param UploadedFile $file Excel文件
     * @return array 导入结果
     */
    public static function importEnrollment(int $orgId, UploadedFile $file): array
    {
        $import = new EnrollmentImport($orgId);

        // 导入数据
        $import->import($file);

        // 返回导入结果
        return $import->getResults();
    }

    /**
     * 导入学员照片
     *
     * @param int $orgId 机构ID
     * @param UploadedFile $file 照片文件
     * @return array 导入结果
     * ['success' => 成功导入的学员数量, 'failed' => 导入失败的学员数量, 'errors' => 导入失败的原因]
     */
    public static function importPhoto(int $orgId, UploadedFile $file): array
    {
         // 创建临时目录解压文件
         $tempPath = AttachmentService::tmpPath(uniqid('photo_import_'));

         if (!file_exists($tempPath)) {
             mkdir($tempPath, 0755, true);
         }

         // 解压文件
         $zip = new \ZipArchive();
         $result = [
             'success' => 0,
             'failed' => 0,
             'errors' => []
         ];

         if ($zip->open($file->getRealPath()) === true) {

             $zip->extractTo($tempPath);
             $zip->close();

             // 遍历解压后的图片文件（直接在临时目录中查找所有图片，不考虑子文件夹）
             $files = [];
             $imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];

             foreach ($imageExtensions as $ext) {
                 $files = array_merge($files, glob($tempPath . '/*.' . $ext));
             }

             // 如果没有找到图片，尝试在可能的子文件夹中查找
             if (empty($files)) {
                 $subfolders = glob($tempPath . '/*', GLOB_ONLYDIR);

                 foreach ($subfolders as $subfolder) {
                     foreach ($imageExtensions as $ext) {
                         $subfiles = glob($subfolder . '/*.' . $ext);
                         $files = array_merge($files, $subfiles);
                     }
                 }
             }

             foreach ($files as $photoPath) {
                 // 获取文件名（不含扩展名）和扩展名
                 $pathInfo = pathinfo($photoPath);
                 $fileName = $pathInfo['filename'];
                 $fileExt = $pathInfo['extension'];

                 // 使用文件名（身份证号）查找学员
                 $student = Student::where('org_id', $orgId)
                     ->where('id_card_number', $fileName)
                     ->orderByDesc('created_at')
                     ->first();

                 if (!$student) {
                     $result['failed']++;
                     $result['errors'][] = "身份证号为 {$fileName} 的学员不存在";
                     continue;
                 }

                 if (!empty($student->photo)) {
                     $result['failed']++;
                     $result['errors'][] = "身份证号为 {$fileName} 已存在照片，跳过上传";
                     continue;
                 }

                 // 上传图片
                 try {
                     // 上传临时文件到七牛云
                     $diskName = config('heguibao.storage.priv');
                     $uploadedFile = AttachmentService::storeFromLocal(
                         $photoPath,
                         $diskName,
                         'photo',
                         BusinessType::Enrollment,
                         $student->id,
                         md5($fileName) . '.' . $fileExt
                     );

                     // 更新学员照片信息
                     $student->photo = $uploadedFile->path;
                     $student->save();

                     $result['success']++;
                 } catch (\Exception $e) {
                     $result['failed']++;
                     $result['errors'][] = "学员 {$fileName} 照片上传失败: " . $e->getMessage();
                 }
             }
         }

         return $result;
    }


    /**
     * 人脸比对与身份证照对比
     *
     * @param string $basePhotoUrl 被对比的照片URL
     * @param string $photoUrl 学员照片URL
     * @param string $cacheKey 缓存键
     * @param string $type 对比类型 CERT:身份证对比，LIVE:活体对比
     * @return array
     */
    public static function faceMatch(string $basePhotoUrl, string $photoUrl, string $cacheKey = '', string $type = 'CERT'): array
    {
        if (!empty($cacheKey)) {
            $cache = Cache::get($cacheKey);

            if ($cache) {
                return $cache;
            }
        }

        if (!in_array($type, ['LIVE', 'CERT'])) {
            $type = 'LIVE';
        }

        $httpService = new HttpClientService();
        $baiduAip = new AipAPI($httpService);
        $result = $baiduAip->faceMatchV4($basePhotoUrl, $photoUrl, $type);

        if (!empty($cacheKey)) {
            Cache::put($cacheKey, $result, 10 * 60);
        }

        return $result;
    }
}
