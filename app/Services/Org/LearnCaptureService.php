<?php

namespace App\Services\Org;

use App\Models\Org\Enrollment;
use App\Models\Org\LearnCapture;
use Carbon\Carbon;

class LearnCaptureService
{
    /**
     * 抓拍头像列表
     *
     * @param Enrollment $enroll
     * @return array
     */
    public static function capturePhotos(Enrollment $enroll): array
    {
        $capturePictures = [];

        LearnCapture::query()
            ->select(['section_id', 'photo'])
            ->where('org_id', $enroll->org_id)
            ->where('enroll_id', $enroll->id)
            ->where('user_id', $enroll->user_id)
            ->where('course_id', $enroll->resource_id)
            ->get()
            ->each(function (LearnCapture $capture) use (&$capturePictures) {
                $capturePictures[$capture->section_id][] = [
                    'photo' => $capture->photo,
                    'photo_url' => $capture->photo_url,
                ];
            });

        return $capturePictures;
    }

    /**
     * 获取某个人多课程抓拍头像列表
     *
     * @param int $enrollId
     * @param array $courseIds
     * @return array
     */
    public static function coursesCapturePhotos(int $enrollId, array $courseIds): array
    {
        $capturePictures = [];

        LearnCapture::query()
            ->select(['section_id', 'photo'])
            ->where('enroll_id', $enrollId)
            ->whereIn('course_id', $courseIds)
            ->get()
            ->each(function (LearnCapture $capture) use (&$capturePictures) {
                $capturePictures[$capture->section_id][] = [
                    'photo' => $capture->photo,
                    'photo_url' => $capture->photo_url,
                ];
            });

        return $capturePictures;
    }


    /**
     * 当前小节抓拍次数
     *
     * @param Enrollment $enrollment
     * @param int $contentId
     * @param int $sectionId
     * @return int
     */
    public static function captureCount(Enrollment $enrollment, int $contentId, int $sectionId): int
    {
        return LearnCapture::query()
            ->where('org_id', $enrollment->org_id)
            ->where('enroll_id', $enrollment->id)
            ->where('user_id', $enrollment->user_id)
            ->where('course_id', $contentId)
            ->where('section_id', $sectionId)
            ->count();
    }

    /**
     * 进入小节详情获取拍照二维码
     *
     * @param Enrollment $enrollment
     * @param int $contentId
     * @param int $sectionId
     * @return string
     */
    public static function getSceneByDetail(Enrollment $enrollment, int $contentId, int $sectionId): string
    {
        $class = $enrollment->classroom;
        if (!$class || !$class->face_capture_enabled) {
            return '';
        }

        $captureCount = self::captureCount($enrollment, $contentId, $sectionId);
        if ($captureCount >= $class->face_capture_count) {
            return '';
        }

        $lastCaptureTime = LearnCapture::query()
            ->where('org_id', $enrollment->org_id)
            ->where('enroll_id', $enrollment->id)
            ->where('user_id', $enrollment->user_id)
            ->where('course_id', $contentId)
            ->where('section_id', $sectionId)
            ->orderBy('created_at', 'desc')
            ->value('created_at');

        if ($lastCaptureTime) {
            if (Carbon::parse($lastCaptureTime)->addMinutes(5)->gt(now())) {
                return '';
            } else {
                return CaptureService::makeCode($enrollment->id, $contentId, $sectionId);
            }
        } else {
            return CaptureService::makeCode($enrollment->id, $contentId, $sectionId);
        }
    }

    /**
     * 更新视频进度获取拍照二维码
     *
     * @param Enrollment $enrollment
     * @param int $contentId
     * @param int $sectionId
     * @return string
     */
    public static function getSceneByProgress(Enrollment $enrollment, int $contentId, int $sectionId): string
    {
        $class = $enrollment->classroom;
        if (!$class || !$class->face_capture_enabled) {
            return '';
        }

        $captureCount = self::captureCount($enrollment, $contentId, $sectionId);
        if ($captureCount >= $class->face_capture_count) {
            return '';
        }

        $lastCaptureTime = LearnCapture::query()
            ->where('org_id', $enrollment->org_id)
            ->where('enroll_id', $enrollment->id)
            ->where('user_id', $enrollment->user_id)
            ->where('course_id', $contentId)
            ->where('section_id', $sectionId)
            ->orderBy('created_at', 'desc')
            ->value('created_at');

        if ($lastCaptureTime) {
            $minutes = config('org.capture.interval');
            if (Carbon::parse($lastCaptureTime)->addMinutes($minutes)->lt(now())) {
                return CaptureService::makeCode($enrollment->id, $contentId, $sectionId);
            } else {
                return '';
            }
        } else {
            return CaptureService::makeCode($enrollment->id, $contentId, $sectionId);
        }
    }
}
