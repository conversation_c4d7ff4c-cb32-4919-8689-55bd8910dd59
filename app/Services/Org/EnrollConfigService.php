<?php

namespace App\Services\Org;

use App\Exceptions\ServiceException;
use App\Models\Org\EnrollConfig;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * 机构报名配置服务
 */
class EnrollConfigService
{
    /**
     * 保存报名配置
     * @param array $params
     * @return EnrollConfig
     */
    public function store(array $params): EnrollConfig
    {
        try {
            if (empty($params['org_id'])) {
                throw new \InvalidArgumentException('org_id is required');
            }
            if (empty($params['title'])) {
                throw new \InvalidArgumentException('title is required');
            }
            if (empty($params['amount'])) {
                throw new \InvalidArgumentException('amount is required');
            }
            $id = $params['id'] ?? 0;
            $config = $id > 0 ? EnrollConfig::query()->find($id) : new EnrollConfig();
            $config->org_id = $params['org_id'];
            $config->title = $params['title'];
            $config->amount = $params['amount'];
            $config->sort = $params['sort'] ?? 0;
            $config->save();
            return $config;

        } catch (\Throwable $e) {
            logger()->error("保存报名配置失败", [
                'params' => $params,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("保存报名配置失败");
        }
    }

    /**
     * 根据机构ID获取报名配置列表
     * @param int $orgId
     * @return Builder[]|Collection
     */
    public function getListByOrgId(int $orgId): Collection|array
    {
        try {
            if (empty($orgId)) {
                throw new \InvalidArgumentException('org_id is required');
            }
            return EnrollConfig::query()
                ->where('org_id', $orgId)
                ->orderByDesc('sort')
                ->get();
        } catch (\Throwable $e) {
            logger()->error("获取报名配置列表失败", [
                'org_id' => $orgId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return Collection::make([]);
        }
    }

    /**
     * 根据ID获取报名配置
     * @param int $id
     * @return EnrollConfig
     */
    public function getEnrollConfigById(int $id): EnrollConfig
    {
        try {
            if (empty($id)) {
                throw new \InvalidArgumentException('id is required');
            }
            return EnrollConfig::find($id);
        } catch (\Throwable $e) {
            logger()->error("获取报名配置失败", [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取报名配置失败");
        }
    }
}
