<?php

namespace App\Services\Org;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Libs\Baidu\AipAPI;
use App\Models\Org;
use App\Models\Org\EnrollmentForm;
use App\Models\Org\Student;
use App\Services\Admin\AttachmentRelationService;
use App\Services\Common\AttachmentService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Throwable;

class StudentService
{
    /**
     * 检查学员是否存在
     * @param int $orgId
     * @param int $userId
     * @param string $name
     * @param string $phone
     * @param string $idCardNumber
     * @param string $idCardFront
     * @param string $idCardBack
     * @param array $extras
     * @return Student
     */
    public static function check(int $orgId, int $userId, string $name, string $phone, string $idCardNumber, string $idCardFront = '', string $idCardBack = '', array $extras = []): Student
    {
        /** @var Student $student */
        $student = Student::query()
            ->where('org_id', $orgId)
            ->where('user_id', $userId)
            ->first();

        if (!$student) {
            $student = self::create($orgId, [
                'user_id' => $userId,
                'name' => $name,
                'phone' => $phone,
                'id_card_number' => $idCardNumber,
                'id_card_front' => $idCardFront,
                'id_card_back' => $idCardBack,
                'extra' => $extras,
            ]);
        }

        return $student;
    }

    /**
     * 创建学员
     * @param int $orgId
     * @param array $params 参数参考 check() 里面的调用
     * @return Student
     */
    public static function create(int $orgId, array $params): Student
    {
        if (Student::query()->where('org_id', $orgId)->where('id_card_number', $params['id_card_number'])->exists()) {
            throw new ServiceException('该身份证已绑定其他手机号码');
        }

        $student = new Student();
        $student->org_id = $orgId;
        $student->id_card_front = '';
        $student->id_card_back = '';
        $student->id_card_number = '';
        $student->photo = '';

        foreach ($params as $key => $val) {
            if (in_array($key, $student->getFillable()) && !in_array($key, ['id_card_front', 'id_card_back', 'photo', 'extra'])) {
                $student->{$key} = $val;
            }
        }

        $student->save();

        $config = self::getAttachmentConfig();

        // 附件处理
        AttachmentRelationService::saveAttachment($student, $params, $config['group_dir'], $config['target_type'], true, 'id_card_front', 'id_card_front');
        AttachmentRelationService::saveAttachment($student, $params, $config['group_dir'], $config['target_type'], true, 'id_card_back', 'id_card_back');
        AttachmentRelationService::saveAttachment($student, $params, $config['group_dir'], $config['target_type'], true, 'photo', 'photo');

        if (!empty($params['extra'])) {
            $student = self::handleExtras($student, $params['extra']);
        }

        return $student;
    }

    /**
     * 修改学员信息
     * @param int $id
     * @param int $orgId
     * @param array $params
     * @return Student
     */
    public static function update(int $id, int $orgId, array $params): Student
    {
        /** @var Student $student */
        $student = Student::query()->where('org_id', $orgId)->find($id);

        if (!$student) {
            throw new ServiceException('学员信息不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $student->getFillable()) && !in_array($key, ['id_card_front', 'id_card_back', 'photo', 'extra'])) {
                $student->{$key} = $val;
            }
        }

        $student->save();

        $config = self::getAttachmentConfig();

        // 附件处理
        AttachmentRelationService::saveAttachment($student, $params, $config['group_dir'], $config['target_type'], false, 'id_card_front', 'id_card_front');
        AttachmentRelationService::saveAttachment($student, $params, $config['group_dir'], $config['target_type'], false, 'id_card_back', 'id_card_back');
        AttachmentRelationService::saveAttachment($student, $params, $config['group_dir'], $config['target_type'], false, 'photo', 'photo');

        if (!empty($params['extras'])) {

            $student = self::handleExtras($student, $params['extras']);
        }

        return $student;
    }

    /**
     * 机构现有的学员，分配班级、科目
     * @param int $orgId
     * @param array $studentIds
     * @param int $classId
     * @param string $subjectType
     * @param int $subjectId
     * @return void
     * @throws Throwable
     */
    public static function batchAssign(int $orgId, array $studentIds, int $classId = 0, string $subjectType = '', int $subjectId = 0): void
    {
        $students = Student::query()->where('org_id', $orgId)->whereIn('id', $studentIds)->get();

        /** @var Student $student */
        foreach ($students as $student) {
            EnrollmentService::create($student->org_id, $student->user_id, $student->id, $classId, $subjectType, $subjectId);
        }
    }

    /**
     * 处理学员扩展信息
     * @param Student $student
     * @param array $extras
     * @return Student
     */
    protected static function handleExtras(Student $student, array $extras = []): Student
    {
        if (empty($extras)) {
            return $student;
        }

        /** @var EnrollmentForm $form */
        $form = EnrollmentForm::query()->where('org_id', $student->org_id)->first();

        $fields = array_column($form->fields, null, 'id');
        $extra = !empty($student->extra) ? array_column($student->extra, null, 'id') : [];

        $config = self::getAttachmentConfig();

        foreach ($extras as $id => $value) {
            $type = $fields[$id]['type'];
            $data = ['id' => $id, 'type' => $type, 'name' => $fields[$id]['name'], 'value' => $value, 'display_text' => ''];
            switch ($type) {
                case EnrollmentForm::TYPE_TEXT:
                case EnrollmentForm::TYPE_TEXTAREA:
                case EnrollmentForm::TYPE_SELECT:
                case EnrollmentForm::TYPE_RADIO:
                case EnrollmentForm::TYPE_SIGN:
                case EnrollmentForm::TYPE_CHECKBOX:
                case EnrollmentForm::TYPE_DATE:
                    isset($extra[$id]) ? $extra[$id] = $data : $extra[] = $data;
                    break;
                case EnrollmentForm::TYPE_WORK_UNIT:
                    isset($extra[$id]) ? $extra[$id] = $data : $extra[] = $data;
                    $student->work_unit = $value;
                    break;
                case EnrollmentForm::TYPE_REGION:
                    $data['value'] = $value['area_code'];
                    $data['display_text'] = implode(' ', $value['area_text']);
                    isset($extra[$id]) ? $extra[$id] = $data : $extra[] = $data;
                    break;
                case EnrollmentForm::TYPE_CASCADE:
                    $data['value'] = explode('->', $value);
                    $data['display_text'] = $value;
                    isset($extra[$id]) ? $extra[$id] = $data : $extra[] = $data;
                    break;
                case EnrollmentForm::TYPE_PHOTO:
                    if (empty($student->photo) || $student->photo != $value) {
                        if ($value) {
                            $idCardUrl = $student->getIdCardFrontUrlAttribute();
                            $photoUrl = AttachmentService::url($value);
                            $cacheKey = 'baidu_face_' . md5($student->id_card_front . ':' . $value);
                            $match = EnrollmentService::faceMatch($idCardUrl, $photoUrl, $cacheKey);

                            if (!$match['pass']) {
                                $filedName = $fields[$id]['name'];
                                throw new ServiceException("您的{$filedName}与身份证信息不一致，请重新上传与身份证一致的{$filedName}，确保照片清晰、无遮挡");
                            }

                            // 如果已有照片且不同，先删除旧照片关联
                            if (!empty($student->photo)) {
                                AttachmentService::removeRelations($config['target_type'], $student->id, $student->photo);
                            }

                            $file = AttachmentService::store($value, $config['group_dir'], $config['target_type'], $student->id);
                            $student->photo = $file->path;
                        } else {
                            // 如果有旧照片，需要删除关联
                            if (!empty($student->photo)) {
                                AttachmentService::removeRelations($config['target_type'], $student->id, $student->photo);
                            }
                            $student->photo = '';
                        }
                    }
                    break;
                case EnrollmentForm::TYPE_PIC:
                case EnrollmentForm::TYPE_FILE:
                    // 根据上传后返回的key获取文件数据
                    $files = isset($extra[$id]) ? $extra[$id]['value'] : [];
                    foreach ($value as $item) {
                        if (str_contains($item, ',delete')) {
                            $path = str_replace(',delete', '', $item);
                            $file = collect($files)->where('path', $path)->first();
                            if ($file) {
                                // 删除文件关联
                                AttachmentService::removeRelations(BusinessType::OrgStudent, $student->id, $file['path']);
                                // 删除data相关数据
                                $files = array_filter($files, fn($file) => $file['path'] != $path);
                            }
                        } else {
                            // 存储上传后的临时文件
                            $file = AttachmentService::store($item, 'student', BusinessType::OrgStudent, $student->id);
                            $files[] = [
                                'path' => $file->path,
                                'file_id' => $file->id,
                                'mime' => $file->mime,
                                'filename' => $file->filename,
                                'filesize' => $file->filesize
                            ];
                        }
                    }
                    $data['value'] = array_values($files);
                    isset($extra[$id]) ? $extra[$id] = $data : $extra[] = $data;
                    break;
            }
        }

        // 删除所有空值字段
        foreach ($extra as $key => $value) {
            if (empty($value)) {
                unset($extra[$key]);
            }
        }
        $student->extra = array_values($extra);
        $student->save();

        return $student;
    }

    protected static function getAttachmentConfig(): array
    {
        return [
            'group_dir' => 'student',
            'target_type' => BusinessType::OrgStudent
        ];
    }

    /**
     * 导入学员身份证照片
     *
     * @param int $orgId 机构ID
     * @param UploadedFile $file 照片文件
     * @return array 导入结果
     * ['success' => 成功导入的学员数量, 'failed' => 导入失败的学员数量, 'errors' => 导入失败的原因]
     */
    public static function importIdCardPhoto(int $orgId, UploadedFile $file, AipAPI $baiDuOcr): array
    {
        // 创建临时目录解压文件
        $tempPath = AttachmentService::tmpPath(uniqid('photo_import_'));

        if (!file_exists($tempPath)) {
            mkdir($tempPath, 0755, true);
        }

        // 解压文件
        $zip = new \ZipArchive();
        $result = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];

        if ($zip->open($file->getRealPath()) === true) {

            $zip->extractTo($tempPath);
            $zip->close();

            // 遍历解压后的图片文件（直接在临时目录中查找所有图片，不考虑子文件夹）
            $files = [];
            $imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];

            foreach ($imageExtensions as $ext) {
                $files = array_merge($files, glob($tempPath . '/*.' . $ext));
            }

            // 如果没有找到图片，尝试在可能的子文件夹中查找
            if (empty($files)) {
                $subfolders = glob($tempPath . '/*', GLOB_ONLYDIR);

                foreach ($subfolders as $subfolder) {
                    foreach ($imageExtensions as $ext) {
                        $subfiles = glob($subfolder . '/*.' . $ext);
                        $files = array_merge($files, $subfiles);
                    }
                }
            }

            $diskName = config('heguibao.storage.priv');

            foreach ($files as $photoPath) {
                // 获取文件名（不含扩展名）和扩展名
                $pathInfo = pathinfo($photoPath);
                $fileName = $pathInfo['filename'];
                $fileExt = $pathInfo['extension'];

                $isFront = false;
                if (!str_contains($fileName, '-1') && !str_contains($fileName, '-2')) {
                    $result['failed']++;
                    $result['errors'][] = "图片名为 {$fileName} 的图片命名格式错误";
                    continue;
                }

                if (str_contains($fileName, '-1')) {
                    $isFront = true;
                    $number = str_replace('-1', '', $fileName);
                } else {
                    $number = str_replace('-2', '', $fileName);
                }

                // 使用文件名（身份证号）查找学员
                $student = Student::where('org_id', $orgId)
                    ->where('id_card_number', $number)
                    ->orderByDesc('created_at')
                    ->first();

                if (!$student) {
                    $result['failed']++;
                    $result['errors'][] = "图片名为 {$fileName} 的学员不存在";
                    continue;
                }

                if ($isFront && !empty($student->id_card_front)) {
                    $result['failed']++;
                    $result['errors'][] = "图片名为 {$fileName} 的学员身份证正面图已存在，跳过上传";
                    continue;
                }

                if (!$isFront && !empty($student->id_card_back)) {
                    $result['failed']++;
                    $result['errors'][] = "图片名为 {$fileName} 的学员身份证反面图已存在，跳过上传";
                    continue;
                }

                // 生成唯一的文件名
                $uniqueFilename = $student->id . '_' . ($isFront ? 'front' : 'back') . '.' . $fileExt;

                // 上传图片
                try {
                    $uploadedFile = AttachmentService::storeFromLocal(
                        $photoPath,
                        $diskName,
                        'photo',
                        BusinessType::OrgStudent,
                        $student->id,
                        $uniqueFilename
                    );

                    if ($isFront) {
                        $url = AttachmentService::url($diskName, $uploadedFile->path);
                        $cardInfo = $baiDuOcr->idCardFront($url);

                        if ($cardInfo['id_card_number'] != $student->id_card_number || $cardInfo['name'] != $student->name) {
                            throw new ServiceException("身份证图片信息与学员信息不符, 请重新上传");
                        }
                    }

                    // 更新学员照片信息
                    if ($isFront) {
                        $student->id_card_front = $uploadedFile->path;
                    } else {
                        $student->id_card_back = $uploadedFile->path;
                    }
                    $student->save();

                    $result['success']++;
                } catch (\Exception $e) {
                    $result['failed']++;
                    $result['errors'][] = "图片名为 {$fileName} 身份证上传失败: " . $e->getMessage();
                }
            }
        }

        return $result;
    }

    public static function remove($id, int $orgId): void
    {
        /** @var Student $student */
        $student = Student::query()->where('org_id', $orgId)->find($id);

        if (!$student) {
            throw new ServiceException('学员不存在');
        }

        if  ($student->enrollments()->count() > 0) {
            throw new ServiceException('学员已报名，不能删除');
        }

        $student->delete();

        Org::query()->where('id', $orgId)->decrement('total_students');
    }
}
