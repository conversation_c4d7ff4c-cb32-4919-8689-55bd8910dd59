<?php

namespace App\Services\Org;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Org\Template;
use App\Services\Admin\AttachmentRelationService;

class TemplateService
{
    public static function create(int $orgId, array $params): Template
    {
        $template = new Template();
        $template->org_id = $orgId;
        $template->tpl_path = '';

        foreach ($params as $key => $val) {
            if (in_array($key, $template->getFillable()) && $key != 'tpl_path') {
                $template->{$key} = $val;
            }
        }

        $template->save();

        AttachmentRelationService::saveAttachment($template, $params, 'OrgTemplate', BusinessType::OrgTemplate, true, 'tpl_path', 'tpl_path');

        return $template;
    }

    public static function update(int $id, int $orgId, array $params): Template
    {
        /** @var Template $template */
        $template = Template::query()->where('org_id', $orgId)->find($id);

        if (!$template) {
            throw new ServiceException('模版不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $template->getFillable()) && $key != 'tpl_path') {
                $template->{$key} = $val;
            }
        }

        $template->save();

        AttachmentRelationService::saveAttachment($template, $params, 'OrgTemplate', BusinessType::OrgTemplate, false, 'tpl_path', 'tpl_path');

        return $template;
    }

    public static function remove($id, int $orgId): void
    {
        /** @var Template $template */
        $template = Template::query()->where('org_id', $orgId)->find($id);

        if (!$template) {
            throw new ServiceException('模版不存在');
        }

        $template->delete();

        AttachmentRelationService::removeAttachment(BusinessType::OrgTemplate, [$id]);
    }

    public static function setDefault($id, int $orgId): Template
    {
        /** @var Template $template */
        $template = Template::query()->where('org_id', $orgId)->find($id);

        if (!$template) {
            throw new ServiceException('模版不存在');
        }

        // 去除同类型默认
        Template::query()
            ->where('org_id', $orgId)
            ->where('type', $template->type)
            ->where('is_default', 1)
            ->update(['is_default' => 0]);

        $template->is_default = 1;
        $template->save();

        return $template;
    }

    /**
     * 获取默认模版
     *
     * @param int $orgId 机构ID
     * @param string $type 模板类型
     * @return Template|null
     */
    public static function getDefault(int $orgId, string $type): ?Template
    {
        /** @var Template $template */
        $template = Template::query()
            ->where('org_id', $orgId)
            ->where('type', $type)
            ->where('is_default', true)
            ->first();
        if (!$template) {
            return null;
        }

        return $template;
    }
}
