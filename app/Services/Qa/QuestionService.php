<?php

namespace App\Services\Qa;

use App\Core\Enums\BusinessType;
use App\Models\Qa\Question;
use App\Services\User\CreditService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class QuestionService
{
    /**
     * 同步es数据
     *
     * @param int $questionId 问题ID
     * @return Question|null
     */
    public static function getSyncEsData(int $questionId): Question|null
    {
        /** @var Question $question */
        $question = Question::query()
            ->select(['id', 'user_id', 'answer_count', 'status', 'title', 'content', 'created_at', 'updated_at'])
            ->with('user')
            ->where('id', $questionId)
            ->first();
        if (!$question) {
            return null;
        }

        return $question;
    }

    /**
     * 更新阅读计数
     *
     * @param Question $question
     * @param string $token 相同的 Token 在一个小时内重复访问不算阅读计数
     */
    public static function updateViews(Question $question, $token)
    {
        $key = 'question:views:'.$question->id.':'.$token;

        if (Cache::get($key)) {
            return;
        }

        //更新阅读计数
        $question->increment('views');
        $question->searchable();

        Cache::put($key, [time(), $question->views], 3600);
    }


    /**
     * @param $uid
     * @param $questionId
     * 发奖励
     * @return void
     */
    public static function questionCredit($uid, $questionId)
    {
        $config = config('heguibao.question');
        $date = now()->toDateString();
        $cacheKey = "question_credit:{$uid}_{$date}";
        $cacheValue = Cache::get(md5($cacheKey)) ?? 0;
        Log::info("发动态奖励数量{$uid}",[$cacheValue]);
        if ($cacheValue && $cacheValue >= $config['limit_per_day']){
            Log::error("用户id_{$uid}发动态数量超过限制了");
            return;
        }
        $credit = $config['reward_credit'];

        CreditService::recharge($uid, $credit, BusinessType::Question, $questionId, '分享优质内容奖励积分');
        $endOfDay = Carbon::now()->endOfDay();
        $ttl = Carbon::now()->diffInSeconds($endOfDay);
        Cache::put(md5($cacheKey), $cacheValue + 1, $ttl);
    }
}
