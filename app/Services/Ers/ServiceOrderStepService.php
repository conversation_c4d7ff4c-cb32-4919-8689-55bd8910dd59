<?php

namespace App\Services\Ers;


use App\Models\Ers\FlowStep;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;

class ServiceOrderStepService
{
    /**
     * 获取流程数据
     *
     * @param ServiceOrder $order 工单实例
     * @return array
     */
    public static function stepList(ServiceOrder $order): array
    {
        $steps = FlowStep::publicFields()
            ->with('orderStep', function ($query) {
                $query->select(['id', 'step_id', 'status']);
            })
            ->whereHas('orderStep', function ($query) use ($order) {
                $query->where('order_id', $order->id);
            })
            ->where('flow_id', $order->flow_id)
            ->orderBy('step')
            ->get();

        if ($steps->isEmpty()) {
            return [];
        }

        $flows = [];
        /** @var FlowStep $step */
        foreach ($steps as $step) {
            $flows[] = [
                'id' => $step->id,
                'name' => $step->name,
                'module' => $step->module,
                'status' => $step->orderStep->status
            ];
        }

        return $flows;
    }

    /**
     * 工单下一步骤
     *
     * @param ServiceOrderStep $step
     * @return ServiceOrderStep|null
     */
    public static function nextStep(ServiceOrderStep $step): ?ServiceOrderStep
    {
        /** @var ServiceOrderStep $orderStep */
        $orderStep =  ServiceOrderStep::query()
            ->where('order_id', $step->order_id)
            ->where('status', ServiceOrderStep::STATUS_PENDING)
            ->where('id', '>', $step->id)
            ->orderBy('id')
            ->first();

        if (!$orderStep) {
            return null;
        }

        return $orderStep;
    }

    /**
     * 向前追溯工单的步骤
     *
     * @param ServiceOrderStep $currentStep 当前步骤
     * @param string $traceModule 要向前追溯的步骤模块
     */
    public static function traceBackStep(ServiceOrderStep $currentStep, string $traceModule): ?ServiceOrderStep
    {
        return ServiceOrderStep::query()
            ->where('order_id', $currentStep->order_id)
            ->where('id', '<', $currentStep->id)
            ->where('module', $traceModule)
            ->where('status', '!=', ServiceOrderStep::STATUS_PENDING)
            ->first();
    }

    /**
     * 推进工单步骤
     *
     * 每当一个步骤状态变更或完成时，将流程往下一个步骤推进，如果所有流程都已完成，则完成整个工单。
     *
     * @param ServiceOrder $order 工单
     * @param ServiceOrderStep $step 当前步骤
     * @return ServiceOrderStep|null 返回下一个步骤，如果没有下一个步骤，则返回 null （代表工单已经完成）
     */
    public static function forward(ServiceOrder $order, ServiceOrderStep $step)
    {
        //已完成的状态推进至下一步步骤
        if ($step->status == ServiceOrderStep::STATUS_FINISH) {
            $nextStep = self::nextStep($step);
            if ($nextStep) {
                $module = ModuleService::getModule($nextStep->module);
                $module::initOrderStep($order, $nextStep);
                $step = $nextStep;
            } else {
                //没有下一步的时候，自动完成工单
                $step = null;
            }
        }

        //根据步骤的状态，确定工单的状态
        if ($step) {
            //如果这里的 match 出现了异常，说明下一步工单直接完成或者未到，这是不正常的，所以这里不作 default 处理
            $orderStatus = match ($step->status) {
                ServiceOrderStep::STATUS_USER_PENDING => ServiceOrder::STATUS_USER_PENDING,
                ServiceOrderStep::STATUS_ADMIN_PENDING => ServiceOrder::STATUS_ADMIN_PENDING
            };
        } else {
            $orderStatus = ServiceOrder::STATUS_FINISH;
        }

        if ($order->status != $orderStatus) {
            $order->status = $orderStatus;
            $orderStatus == ServiceOrder::STATUS_FINISH && $order->finished_at = now();
            $order->save();
        }

        return $step;
    }

}
