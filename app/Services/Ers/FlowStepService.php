<?php

namespace App\Services\Ers;

use App\Models\Ers\FlowStep;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\Project;
use App\Services\Ers\Modules\FormModule;
use Illuminate\Support\Collection;

class FlowStepService
{
    /**
     * 获取表单项行业和企业类别ID
     *
     * @param Project $project 项目
     * @param Collection $steps 流程集合
     * @return array
     */
    public static function getFormIndustry(Project $project, Collection $steps): array
    {
        $industryId = $enterpriseId = 0;
        $steps->each(function (FlowStep $step) use (&$industryId, &$enterpriseId, $project) {
            if ($step->module == FormModule::configure()->t) {
                /** @var FormProjectForm $form */
                $form = FormProjectForm::query()
                    ->select(['industry_id', 'enterprise_id'])
                    ->where('project_id', $project->id)
                    ->where('flow_id', $project->flow_id)
                    ->where('step_id', $step->id)
                    ->first();
                if ($form) {
                    $industryId = $form->industry_id;
                    $enterpriseId = $form->industry_id;
                }
            }
        });

        return [$industryId, $enterpriseId];
    }
}
