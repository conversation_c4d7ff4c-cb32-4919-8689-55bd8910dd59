<?php

namespace App\Services\Ers\Modules;

use App\Exceptions\ServiceException;
use App\Libs\Sms\SmsTemplate;
use App\Libs\Wechat\MpTemplate;
use App\Models\Admin\UserRole;
use App\Models\Ers\FormOrderData;
use App\Models\Ers\FormOrderForm;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\FormProjectInput;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Models\WechatNoticeRecord;
use App\Services\Ers\ServiceOrderService;
use App\Services\SmsService;
use App\Services\WechatService;
use Carbon\Carbon;

/**
 * 表单资料收集模块
 */
class FormModule implements ModuleInterface
{

    public static function configure(): ModuleConfigure
    {
        return new ModuleConfigure('form', '表单模块', '提交资料', FormOrderForm::class);
    }

    public static function convertOrderStepData(ServiceOrder $order, ServiceOrderStep $step): array
    {
        if ($step->data) {
            return $step->data->data->map(fn($row) => self::formatInput($row->input, $row))->toArray();
        } else {
            return self::projectInputs($order->project_id, $step->step_id, $order->project->is_bind_category, $order->industry_id, $order->enterprise_id);
        }
    }

    /**
     * 获取项目的输入框结构
     *
     * @param int $projectId
     * @param int $stepId
     * @param bool $isBindCategory
     * @param int $industryId
     * @param int $enterpriseId
     * @return array
     */
    public static function projectInputs($projectId, $stepId, $isBindCategory, $industryId=0, $enterpriseId=0)
    {
        $query = FormProjectForm::query()
            ->where('project_id', $projectId)
            ->where('step_id', $stepId);

        //要求绑定行业与企业类别的
        if ($isBindCategory) {
            $query->where('industry_id', $industryId)
                ->where('enterprise_id', $enterpriseId);
        }

        /** @var FormProjectForm $form */
        $form = $query->with('inputs')->first();

        if (!$form) {
            throw new ServiceException("项目表单不存在");
        }

        return $form->inputs->map(fn($row) => self::formatInput($row))->toArray();
    }

    /**
     * 格式化表单项的输出
     *
     * @param FormProjectInput $input
     * @param FormOrderData|null $data
     * @return array
     */
    private static function formatInput(FormProjectInput $input, ?FormOrderData $data=null)
    {
        $format = [
            'id' => $input->id,
            'title' => $input->title,
            'type' => $input->type
        ];

        if ($input->type != 'group') {
            if ($data !== null) {
                $value = match ($input->type) {
                    'text', 'textarea', 'select' => $data->data['value'] ?? null,
                    'checkbox' => $data->data['values'] ?? [],
                    'image', 'file' => $data->data ? FormOrderData::fileUrls($data->data['files']) : []
                };
            } else {
                $value = null;
            }

            $format += [
                'value' => $value,
                'is_required' => $input->is_required,
                'desc' => $input->desc,
                'options' => $input->options,
                'status' => $data?->status ?? FormOrderData::STATUS_UN_SUBMIT,
                'reject_reason' => $data?->status == FormOrderData::STATUS_REJECT ? $data->reject_reason : ''
            ];
        }

        return $format;
    }

    public static function initOrderStep(ServiceOrder $order, ServiceOrderStep $step)
    {
        if ($step->status == ServiceOrderStep::STATUS_PENDING) {
            $step->status = ServiceOrderStep::STATUS_USER_PENDING;
            $step->save();
        }
    }

    public static function subFlows(ServiceOrder $order, ServiceOrderStep $step)
    {
        return [
            new SubFlow(
                '提交资料',
                SubFlow::status($step, SubFlow::WHO_USER),
                $order->user,
                'form:submit',
                $step->last_user_handled_at
            ),
            new SubFlow(
                '审核资料',
                SubFlow::status($step, SubFlow::WHO_USER, SubFlow::WHO_ADMIN),
                $order->operator,
                'form:audit',
                $step->last_admin_handled_at
            )
        ];
    }

    public static function messageNotice(ServiceOrder $order, ServiceOrderStep $step)
    {
        switch ($step->status) {
            case ServiceOrderStep::STATUS_USER_PENDING:
                self::materialModifyNotice($order, $step);
                break;
            case ServiceOrderStep::STATUS_ADMIN_PENDING:
                $reSubmit = $step->data->data->filter(fn($row) => !empty($row->reject_reason))->first();
                if ($reSubmit) {// 审核拒绝后提交
                    ServiceOrderService::sendFlowMessage($order->admin_id);
                } else {// 初次提交
                    self::sendSubmitMessage();
                }
                break;
        }
    }

    /**
     * 表单提交短信
     *
     * @return void
     */
    public static function sendSubmitMessage(): void
    {
        UserRole::query()
            ->with(['role', 'admin' => fn ($q) => $q->select(['id', 'phone', 'real_name'])])
            ->whereHas('role', fn ($q) => $q->where('code', 'R_ERS_ORDER_DISPATCH'))
            ->get()
            ->each(function ($role) {
                if ($role->admin) {
                    $name = $role->admin->real_name;
                    $phone = $role->admin->phone;
                    if (!empty($name) && !empty($phone)) {
                        $content = "尊敬的{$name}，您有新的工单待处理。请尽快登录系统查看并处理，确保客户服务的及时性。感谢您的努力与支持！";
                        SmsService::send($role->admin->phone, SmsTemplate::SERVICE_ORDER, [$name], $content);
                    }
                }
            });
    }

    /**
     * 资料待修改小程序订阅消息通知
     *
     * @param ServiceOrder $order
     * @param ServiceOrderStep $step
     * @return void
     */
    public static function materialModifyNotice(ServiceOrder $order, ServiceOrderStep $step): void
    {
        $data = [
            'character_string1' => ['value' => $order->sid],
            'phrase7' => ['value' => ServiceOrderStep::statusLabel($step->status)],
            'phrase8' => ['value' => '资料待修改'],
            'thing3' => ['value' => '资料审核不通过，请修改后再提交'],
            'date2' => ['value' => Carbon::now()->toDateTimeString()]
        ];

        $templateId = config('easywechat.mp.template_ids')[MpTemplate::SERVICE_ORDER];

        $messageSample = MpTemplate::messageSample($data, MpTemplate::SERVICE_ORDER);

        $page = "pages/services/detail?sid=$order->sid&project_id=$order->project_id&title={$order->project->title}";

        WechatService::commonNotice($order->user_id,  $templateId, $data, $messageSample, WechatNoticeRecord::TYPE_MATERIAL_MODIFY, $page);
    }
}
