<?php

namespace App\Services\Ers\Modules;

use Illuminate\Database\Eloquent\Model;

/**
 * 模块基础配置
 */
class ModuleConfigure
{

    /**
     * @param string $t 模块的标识
     * @param string $name 模块名
     * @param string $defaultNameFlow 模块在流程中默认的名称
     * @param class-string<Model> $orderStepDataModel 该模块在工单步骤中关联的数据模型
     */
    public function __construct(
        public readonly string $t,
        public readonly string $name,
        public readonly string $defaultNameFlow,
        public readonly string $orderStepDataModel
    )
    {}

}
