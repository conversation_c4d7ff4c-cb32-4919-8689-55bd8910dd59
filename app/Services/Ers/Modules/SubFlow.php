<?php

namespace App\Services\Ers\Modules;

use App\Models\Admin\Admin;
use App\Models\Ers\ServiceOrderStep;
use App\Models\User;
use Illuminate\Support\Carbon;

/**
 * 子流程
 */
class SubFlow
{

    const STATUS_N = 0;
    const STATUS_PENDING = 1;
    const STATUS_FINISHED = 2;

    const WHO_USER = 0; //用户
    const WHO_ADMIN = 1; //管理

    /** @var ?array{who: int, name: string, avatar: string} */
    public readonly ?array $people;

    /**
     * @param string $desc 描述
     * @param int $status 该流程当前状态
     * @param User|Admin|null $people 关注人
     * @param string $action 动作标记，格式 "模块:动作"，如 "form:audit"
     * @param ?Carbon $at 动作发生时间
     */
    public function __construct(
        public readonly string $desc,
        public readonly int $status,
        User|Admin|null $people,
        public readonly string $action,
        public readonly ?Carbon $at = null
    )
    {
        if ($people instanceof User) {
            $this->people = [
                'who' => self::WHO_USER,
                'name' => $people->nickname,
                'avatar' => $people->avatar
            ];
        } elseif ($people instanceof Admin) {
            $this->people = [
                'who' => self::WHO_ADMIN,
                'name' => $people->real_name,
                'avatar' => ''
            ];
        } else {
            //还未分配后台管理时
            $this->people = null;
        }
    }


    /**
     * 确定当前步骤的状态
     *
     * @param ServiceOrderStep $step
     * @param int $startBy 该模块是由哪一方开始的
     * @param ?int $thisBy 当前子流程是谁操作，不指定则与 $startBy 相同
     * @return int
     */
    public static function status(ServiceOrderStep $step, $startBy, $thisBy=null)
    {
        if ($thisBy === null) {
            $thisBy = $startBy;
        }

        if ($step->status == ServiceOrderStep::STATUS_PENDING) {
            return self::STATUS_N;
        } elseif ($step->status == ServiceOrderStep::STATUS_FINISH ) {
            return self::STATUS_FINISHED;
        } else {
            $pendingStatus = match ($thisBy) {
                self::WHO_USER => ServiceOrderStep::STATUS_USER_PENDING,
                self::WHO_ADMIN => ServiceOrderStep::STATUS_ADMIN_PENDING
            };

            return $pendingStatus == $step->status ? self::STATUS_PENDING : ($startBy == $thisBy ? self::STATUS_FINISHED : self::STATUS_N);
        }

        //return match ($step->status) {
        //    ServiceOrderStep::STATUS_PENDING => self::STATUS_N,
        //    ServiceOrderStep::STATUS_FINISH => self::STATUS_FINISHED,
        //    default => match ($startBy) {
        //        self::WHO_USER => match ($currentBy) {
        //            self::WHO_USER => match ($step->status) {
        //                ServiceOrderStep::STATUS_USER_PENDING => self::STATUS_PENDING,
        //                default => self::STATUS_FINISHED
        //            }
        //        },
        //        //self::WHO_USER => match ($step->status) {
        //        //    ServiceOrderStep::STATUS_USER_PENDING => self::STATUS_PENDING,
        //        //    default => self::STATUS_FINISHED
        //        //},
        //        self::WHO_ADMIN => match ($step->status) {
        //            ServiceOrderStep::STATUS_ADMIN_PENDING => self::STATUS_PENDING,
        //            default => self::STATUS_FINISHED
        //        }
        //    }
        //};
    }

}
