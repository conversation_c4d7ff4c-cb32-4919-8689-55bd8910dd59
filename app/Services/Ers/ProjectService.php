<?php

namespace App\Services\Ers;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\FormProjectInput;
use App\Models\Ers\Project;
use App\Models\Ers\ServiceOrder;
use App\Services\Admin\AttachmentRelationService;
use App\Services\Common\AttachmentService;
use Illuminate\Support\Facades\Log;
use Throwable;

class ProjectService
{
    protected static string $attachmentGroupDir = 'ers';

    /**
     * 项目列表
     *
     * @return array
     */
    public static function getProjectList(): array
    {
        return Project::publicFields()
            ->where('status', Project::STATUS_NORMAL)
            ->orderBy('sort')
            ->get()
            ->toArray();
    }

    /**
     * 项目
     *
     * @param int $id
     * @return ?Project
     */
    public static function getProject(int $id): ?Project
    {
        return Project::publicFields()
            ->where('id', $id)
            ->where('status', Project::STATUS_NORMAL)
            ->first();
    }

    public static function create(array $params): Project
    {
        $model = new Project();

        foreach ($params as $key => $val) {
            if (in_array($key, $model->getFillable()) && $key != 'icon') {
                $model->{$key} = $val;
            }
        }

        $model->save();

        // 处理图标
        AttachmentRelationService::saveAttachment($model, $params, self::$attachmentGroupDir, BusinessType::ErsProject, true, 'icon', 'icon');

        self::handleAttachments($model, $params);

        return $model;
    }

    public static function update(int $id, array $params): Project
    {
        /** @var Project $model */
        $model = Project::query()->find($id);

        if (!$model) {
            throw new ServiceException('对象不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $model->getFillable()) && $key != 'icon') {
                $model->{$key} = $val;
            }
        }

        $model->save();

        // 处理图标
        AttachmentRelationService::saveAttachment($model, $params, self::$attachmentGroupDir, BusinessType::ErsProject, false, 'icon', 'icon');

        self::handleAttachments($model, $params);

        return $model;
    }

    public static function remove(int $id): void
    {
        /** @var Project $model */
        $model = Project::query()->find($id);

        if (!$model) {
            throw new ServiceException('对象不存在');
        }

        if (ServiceOrder::query()->where('project_id', $id)->exists()) {
            throw new ServiceException('项目已使用');
        }

        if (FormProjectInput::query()->where('project_id', $id)->exists()) {
            throw new ServiceException('请先删除关联配置');
        }

        $model->delete();

        FormProjectForm::query()->where('project_id', $id)->delete();
        FormProjectInput::query()->where('project_id', $id)->forceDelete();

        // 删除附件
        AttachmentRelationService::removeAttachment(BusinessType::ErsProject, $model->id);
    }

    protected static function handleAttachments(Project $project, array $params): void
    {
        if (empty($params['upload_files'])) {
            return;
        }

        $targetType = BusinessType::ErsProject;

        $uploadData = [];
        foreach ($params['upload_files'] as $file) {
            if (!$file['type_id']) {
                try {
                    $uploadData[$file['url']] = AttachmentService::store($file['filepath'], self::$attachmentGroupDir, $targetType, $project->id);
                } catch (Throwable) {
                    Log::error("服务项目{$project->id}保存附件失败: {$file['filepath']}");
                }
            }
        }

        if (empty($uploadData)) {
            return;
        }

        foreach ($uploadData as $url => $attachment) {
            $project->intro = str_replace($url, $attachment->url, $project->intro);
            $project->save();
        }
    }
}
