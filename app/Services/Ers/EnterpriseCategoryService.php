<?php

namespace App\Services\Ers;

use App\Exceptions\ServiceException;
use App\Models\Ers\EnterpriseCategory;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\Industry;

class EnterpriseCategoryService
{
    public static function create(array $params): EnterpriseCategory
    {
        if (!Industry::query()->where('id', $params['industry_id'])->exists()) {
            throw new ServiceException('行业不存在');
        }

        $model = new EnterpriseCategory();

        foreach ($params as $key => $val) {
            if (in_array($key, $model->getFillable())) {
                $model->{$key} = $val;
            }
        }

        $model->sort = EnterpriseCategory::query()->count();
        $model->save();

        return $model;
    }

    public static function update(int $id, array $params): EnterpriseCategory
    {
        /** @var EnterpriseCategory $model */
        $model = EnterpriseCategory::query()->find($id);

        if (!$model) {
            throw new ServiceException('对象不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $model->getFillable())) {
                $model->{$key} = $val;
            }
        }

        $model->save();

        return $model;
    }

    public static function move(int $fromId, int $toId): void
    {
        /** @var EnterpriseCategory $from */
        $from = EnterpriseCategory::query()->find($fromId);

        /** @var EnterpriseCategory $to */
        $to = EnterpriseCategory::query()->find($toId);

        if (!$from || !$to) {
            throw new ServiceException('参数错误');
        }

        $fromSort = $from->sort;
        $toSort = $to->sort;

        $from->sort = $toSort;
        $from->save();

        $to->sort = $fromSort;
        $to->save();
    }

    public static function remove(int $id): void
    {
        /** @var EnterpriseCategory $model */
        $model = EnterpriseCategory::query()->find($id);

        if (!$model) {
            throw new ServiceException('对象不存在');
        }

        if (FormProjectForm::query()->where('enterprise_id', $id)->exists()) {
            throw new ServiceException('已应用到表单设置中，无法直接删除');
        }

        $model->delete();
    }
}
