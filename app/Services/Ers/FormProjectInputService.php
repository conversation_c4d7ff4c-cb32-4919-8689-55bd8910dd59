<?php

namespace App\Services\Ers;

use App\Exceptions\ServiceException;
use App\Models\Ers\FormLibrary;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\FormProjectInput;

class FormProjectInputService
{
    public static function create(FormProjectForm $form, array $params): FormProjectInput
    {
        $params['project_id'] = $form->project_id;
        $params['flow_id'] = $form->flow_id;
        $params['step_id'] = $form->step_id;
        $params['project_form_id'] = $form->id;

        // 选择组件库
        if (isset($params['form_library_id']) && $params['form_library_id']) {
            /** @var FormLibrary $library */
            $library = FormLibrary::query()->find($params['form_library_id']);

            if (!$library) {
                throw new ServiceException('选择的组件不存在');
            }
        }

        $model = new FormProjectInput();

        foreach ($params as $key => $val) {
            if (in_array($key, $model->getFillable())) {
                $model->{$key} = $val;
            }
        }

        $model->save();

        return $model;
    }

    public static function update(int $id, array $params): FormProjectInput
    {
        /** @var FormProjectInput $model */
        $model = FormProjectInput::query()->find($id);

        if (!$model) {
            throw new ServiceException('对象不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $model->getFillable())) {
                $model->{$key} = $val;
            }
        }

        $model->save();

        return $model;
    }

    public static function remove(int $id): void
    {
        /** @var FormProjectInput $model */
        $model = FormProjectInput::query()->find($id);

        if (!$model) {
            throw new ServiceException('对象不存在');
        }

        $model->delete();
    }

    /**
     * 项目表单项验证
     *
     * @param array $postInputs 提交表单项
     * @param FormProjectForm $projectForm 项目表单实例
     * @return void
     */
    public static function validateInputs(array $postInputs, FormProjectForm $projectForm): void
    {
        $postInputIds = array_keys($postInputs);
        $inputIds = $projectForm->inputs->pluck('id')->toArray();
        if (array_diff($postInputIds, $inputIds)) {
            throw new ServiceException("表单项ID异常");
        }

        foreach ($postInputs as $inputId => $value) {
            /** @var FormProjectInput $currentInput */
            $currentInput = $projectForm->inputs->filter(fn($input) => $input->id == $inputId)->first();
            if ($currentInput->type == FormProjectInput::TYPE_GROUP) {
                continue;
            }

            if ($currentInput->type == FormProjectInput::TYPE_TEXT || FormProjectInput::TYPE_TEXTAREA) {
                $options = $currentInput->options;
                $min = $options['min'] ?? null;
                $max = $options['max'] ?? null;

                if ($min && $max) {
                    if (mb_strlen($value) < $min || mb_strlen($value) > $max) {
                        throw new ServiceException("{$currentInput->title}长度不能小于{$min}位且不能大于{$max}位");
                    }
                }
            }
        }
    }
}
