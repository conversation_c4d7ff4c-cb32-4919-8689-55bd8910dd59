<?php

namespace App\Services\Ers;

use App\Libs\Sms\SmsTemplate;
use App\Models\Admin\Admin;
use App\Models\Ers\FlowStep;
use App\Models\Ers\Project;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Services\SmsService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class ServiceOrderService
{
    /**
     * 工单进度
     *
     * @param int $userId
     * @return array
     */
    public static function orderProgress(int $userId): array
    {
        $statusArr = ServiceOrder::query()
            ->select([DB::raw('count(status) as status_count'), 'status'])
            ->where('user_id', $userId)
            ->groupBy('status')
            ->get()->makeHidden('sid')
            ->toArray();

        if (empty($statusArr)) {
            $list = [
                ['status' => ServiceOrder::STATUS_USER_PENDING, 'status_count' => 0],
                ['status' => ServiceOrder::STATUS_ADMIN_PENDING, 'status_count' => 0],
                ['status' => ServiceOrder::STATUS_FINISH, 'status_count' => 0]
            ];
        } else {
            $statusList = array_column($statusArr, 'status');
            $allStatusList = [ServiceOrder::STATUS_USER_PENDING, ServiceOrder::STATUS_ADMIN_PENDING, ServiceOrder::STATUS_FINISH];
            $diff = array_merge(array_diff($statusList, $allStatusList), array_diff($allStatusList, $statusList));
            $fillStatus = [];
            foreach ($diff as $status) {
                $fillStatus[] = ['status' => $status, 'status_count' => 0];
            }
            $list = array_merge($statusArr, $fillStatus);
        }

        return $list;
    }

    /**
     * 获取工单的完整步骤及数据
     *
     * @param ServiceOrder $order
     * @param bool $withData 是否包含数据
     * @param bool $withSubFlows 是否包含子流程
     * @return array 工单步骤数据列表
     */
    public static function getFlows(ServiceOrder $order, $withData=false, $withSubFlows=false)
    {
        $relations = ['steps', 'steps.step'];

        if ($withData) {
            $relations[] = 'steps.data';
        }

        $order = $order->load($relations);
        $steps = $order->steps->sortBy('step.step');

        $flows = [];

        if ($order->admin_id) {
            $operator = [
                'name' => $order->operator->real_name,
                'avatar' => ''
            ];
        } else {
            $operator = null;
        }

        $user = [
            'avatar' => $order->user->avatar,
            'name' => $order->user->nickname
        ];

        foreach ($steps as $step) {
            $moduleClass = ModuleService::getModule($step->module);
            $configure = $moduleClass::configure();
            $data = [
                'id' => $step->step_id,
                'name' => $step->step->name ?? $configure->defaultNameFlow,
                'module' => $step->module,
                'status' => $step->status,
                'people' => match ($step->status) {
                    ServiceOrderStep::STATUS_USER_PENDING => $user,
                    ServiceOrderStep::STATUS_ADMIN_PENDING => $operator,
                    ServiceOrderStep::STATUS_FINISH => $step->finished_by == ServiceOrderStep::FINISHED_BY_USER ? $user : $operator,
                    default => null
                }
            ];

            if ($withData) {
                $data['data'] = $moduleClass::convertOrderStepData($order, $step);
            }

            if ($withSubFlows) {
                $data['sub_flows'] = $moduleClass::subFlows($order, $step);
            }

            $flows[] = $data;
        }

        return $flows;
    }

    /**
     * 获取工单详情
     *
     * @param string $sid 工单SID
     * @param int $userId 用户ID
     * @return ServiceOrder|null
     */
    public static function getOrder(string $sid, int $userId): ServiceOrder|null
    {
        return ServiceOrder::whereSid($sid)
            ->with('steps')
            ->where('user_id', $userId)
            ->first();
    }

    /**
     * 创建工单
     *
     * @param int $userId 用户ID
     * @param Project $project 项目实例
     * @param int $industryId 行业ID
     * @param int $enterpriseId 企业类别ID
     * @param Collection<FlowStep> $steps 服务流程
     * @return ServiceOrder
     */
    public static function createOrder(int $userId, Project $project, int $industryId, int $enterpriseId, Collection $steps): ServiceOrder
    {
        $order = new ServiceOrder();
        $order->user_id = $userId;
        $order->project_id = $project->id;
        $order->flow_id = $project->flow_id;
        $order->status = ServiceOrder::STATUS_USER_PENDING;
        $order->industry_id = $industryId;
        $order->enterprise_id = $enterpriseId;
        $order->save();

        // 流程
        /** @var FlowStep $step */
        foreach ($steps as $step) {
            $orderStep = new ServiceOrderStep();
            $orderStep->order_id = $order->id;
            $orderStep->flow_id = $order->flow_id;
            $orderStep->step_id = $step->id;
            $orderStep->status = $step->step == 0 ? ServiceOrderStep::STATUS_USER_PENDING : ServiceOrderStep::STATUS_PENDING;
            $orderStep->module = $step->module;
            $orderStep->data_id = 0;
            $orderStep->save();
        }

        return $order;
    }

    /**
     * 工单分配短信
     *
     * @param Admin $admin admin实例
     * @return void
     */
    public static function sendAssignMessage(Admin $admin): void
    {
        $name = $admin->real_name;
        $content = "尊敬的{$name}，您有新的工单待处理。请尽快登录系统查看并处理，确保客户服务的及时性。感谢您的努力与支持！";
        SmsService::send($admin->phone, SmsTemplate::SERVICE_ORDER, [$name], $content);
    }

    /**
     * 工单流转短信
     *
     * @param int $adminId
     * @return void
     */
    public static function sendFlowMessage(int $adminId): void
    {
        /** @var Admin $admin */
        $admin = Admin::query()->find($adminId);
        if (!$admin) {
            return;
        }

        $name = $admin->real_name;
        $content = "尊敬的{$name}，您负责的工单状态已更新。请尽快登录系统查看并处理，确保客户服务的及时性。感谢您的努力与支持！";
        SmsService::send($admin->phone, SmsTemplate::SERVICE_ORDER_FLOW, [$name], $content);
    }
}
