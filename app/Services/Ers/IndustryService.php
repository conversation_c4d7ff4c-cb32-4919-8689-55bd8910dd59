<?php

namespace App\Services\Ers;

use App\Exceptions\ServiceException;
use App\Models\Ers\EnterpriseCategory;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\Industry;

class IndustryService
{
    public static function create(array $params): Industry
    {
        if (Industry::query()->where('name', $params['name'])->exists()) {
            throw new ServiceException('名称已存在');
        }

        $model = new Industry();

        foreach ($params as $key => $val) {
            if (in_array($key, $model->getFillable())) {
                $model->{$key} = $val;
            }
        }

        $model->sort = Industry::query()->count();
        $model->save();

        return $model;
    }

    public static function update(int $id, array $params): Industry
    {
        /** @var Industry $model */
        $model = Industry::query()->find($id);

        if (!$model) {
            throw new ServiceException('对象不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $model->getFillable())) {
                $model->{$key} = $val;
            }
        }

        $model->save();

        return $model;
    }

    public static function move(int $fromId, int $toId): void
    {
        /** @var Industry $from */
        $from = Industry::query()->find($fromId);

        /** @var Industry $to */
        $to = Industry::query()->find($toId);

        if (!$from || !$to) {
            throw new ServiceException('参数错误');
        }

        $fromSort = $from->sort;
        $toSort = $to->sort;

        $from->sort = $toSort;
        $from->save();

        $to->sort = $fromSort;
        $to->save();
    }

    public static function remove(int $id): void
    {
        /** @var Industry $model */
        $model = Industry::query()->find($id);

        if (!$model) {
            throw new ServiceException('对象不存在');
        }

        if (EnterpriseCategory::query()->where('industry_id', $id)->exists()) {
            throw new ServiceException('已设置企业分类，无法直接删除');
        }

        if (FormProjectForm::query()->where('industry_id', $id)->exists()) {
            throw new ServiceException('已应用到表单设置中，无法直接删除');
        }

        $model->delete();
    }
}
