<?php

namespace App\Services\Ers;

use App\Core\Enums\BusinessType;
use App\Models\Ers\FormOrderData;
use App\Models\Ers\FormOrderForm;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\FormProjectInput;
use App\Services\Common\AttachmentService;

class FormOrderDataService
{
    /**
     * 创建/更新工单表单项数据
     *
     * @param array $postInputs 提交表单项
     * @param FormProjectForm $projectForm 项目表单实例
     * @param FormOrderForm $orderForm 工单表单实例
     * @return void
     */
    public static function createdOrUpdatedFormOrderData(array $postInputs, FormProjectForm $projectForm, FormOrderForm $orderForm): void
    {
        foreach ($postInputs as $inputId => $value) {
            /** @var FormProjectInput $currentInput */
            $currentInput = $projectForm->inputs->where('id', $inputId)->first();
            if ($currentInput->type == FormProjectInput::TYPE_GROUP) {
                continue;
            }

            // 创建/更新表单项数据
            $orderData = $orderForm->data->where('project_input_id', $currentInput->id)->first();
            if (!$orderData) {
                $orderData = new FormOrderData();
                $orderData->order_id = $orderForm->order_id;
                $orderData->order_form_id = $orderForm->id;
                $orderData->project_input_id = $currentInput->id;
                $orderData->data = [];
                $orderData->save();
            }

            $beforeData = $orderData->data;

            switch ($currentInput->type) {
                case FormProjectInput::TYPE_TEXT:
                case FormProjectInput::TYPE_TEXTAREA:
                case FormProjectInput::TYPE_SELECT:
                    $orderData->data = ['value' => $value];
                    break;
                case FormProjectInput::TYPE_CHECKBOX:
                    $orderData->data = ['values' => $value];
                    break;
                case FormProjectInput::TYPE_IMAGE:
                case FormProjectInput::TYPE_FILE:
                    $orderData->data = $orderData->data ?: ['files' => []];
                    $files = $orderData->data['files'];
                    // 根据上传后返回的key获取文件数据
                    foreach ($value as $item) {
                        if (str_contains($item, ',delete')) {
                            $path = str_replace(',delete', '', $item);
                            $file = collect($files)->where('path', $path)->first();
                            if ($file) {
                                // 删除文件关联
                                AttachmentService::removeRelations(BusinessType::ErsOrder, $orderData->id, $file['path']);
                                // 删除data相关数据
                                $files = array_filter($files, fn($file) => $file['path'] != $path);
                            }
                        } else {
                            // 存储上传后的临时文件
                            $file = AttachmentService::store($item, 'form_order', BusinessType::ErsOrder, $orderData->id);
                            $files[] = [
                                'path' => $file->path,
                                'file_id' => $file->id,
                                'mime' => $file->mime,
                                'filename' => $file->filename,
                                'filesize' => $file->filesize
                            ];
                        }
                    }
                    $orderData->data = ['files' => $files];
                    break;
            }

            if ($orderData->status != FormOrderData::STATUS_SUCCESS || $beforeData != $orderData->data) {
                $orderData->status = FormOrderData::STATUS_PENDING;
            }

            $orderData->save();
        }
    }
}
