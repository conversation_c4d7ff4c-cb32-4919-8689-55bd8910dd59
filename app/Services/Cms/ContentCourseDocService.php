<?php

namespace App\Services\Cms;

use App\Core\Enums\BusinessType;
use App\Models\Cms\ContentCourseDoc;
use App\Services\Common\AttachmentService;

class ContentCourseDocService
{
    /**
     * 创建课程资料
     *
     * @param int $contentId 内容ID
     * @param string $filename 资料名称
     * @param string $filepath 文件key
     * @param int $sort 排序
     * @return ContentCourseDoc
     */
    public static function create(int $contentId, string $filename, string $filepath, int $sort): ContentCourseDoc
    {
        $doc = new ContentCourseDoc();
        $doc->content_id = $contentId;
        $doc->filename = $filename;
        $doc->sort = $sort;

        $file = AttachmentService::store($filepath, 'content', BusinessType::CmsCourseDoc, $contentId);

        $doc->filepath = $file->path;
        $doc->save();

        return $doc;
    }

    /**
     * 更新课程资料
     *
     * @param ContentCourseDoc $doc 课程资料模型实例
     * @param string $filename 资料名称
     * @param string $filepath 资料文件路径
     * @param int $sort 排序
     * @return ContentCourseDoc
     */
    public static function update(ContentCourseDoc $doc, string $filename, string $filepath, int $sort): ContentCourseDoc
    {
        if ($filepath != $doc->filepath) {
            AttachmentService::removeRelations(BusinessType::CmsCourseDoc, $doc->content_id, $doc->filepath);
            $file = AttachmentService::store($filepath, 'content', BusinessType::CmsCourseDoc, $doc->content_id);
            $doc->filepath = $file->path;
        }

        $doc->filename = $filename;
        $doc->sort = $sort;
        $doc->save();

        return $doc;
    }

    /**
     * 删除课程资料
     *
     * @param ContentCourseDoc $doc
     * @return void
     */
    public static function delete(ContentCourseDoc $doc): void
    {
        AttachmentService::removeRelations(BusinessType::CmsCourseDoc, $doc->content_id, $doc->filepath);
        $doc->delete();
    }
}
