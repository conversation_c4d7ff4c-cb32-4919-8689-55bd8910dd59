<?php

namespace App\Services\Cms;

use App\Models\Stat\DailyOverview;
use Carbon\Carbon;
use Elasticsearch\Client as Elastic;
use Illuminate\Support\Facades\DB;

class SearchService
{
    /** @var string ES游标过期时间 */
    public static string $esScroll = '30m';

    /**
     * ES搜索数据
     *
     * @param string $index 索引名称
     * @param array $query 查询语句
     * @param array $sort 排序
     * @param int $listRows 每页条数
     * @param string $scrollId 游标ID
     * @return array
     */
    public static function getEsData(string $index, array $query = [], array $sort = [], int $listRows = 20, string $scrollId = ''): array
    {
        $elastic = app(Elastic::class);

        if (!empty($scrollId)) {
            $params = [
                'scroll' => self::$esScroll,
                'scroll_id' => $scrollId
            ];
            $elasticData = $elastic->scroll($params);
        } else {
            $params = [
                'index' => $index,
                'body' => [
                    'query' => $query,
                    'sort' => $sort,
                    'size' => $listRows
                ],
                'scroll' => self::$esScroll
            ];
            $elasticData = $elastic->search($params);
        }

        return $elasticData;
    }

    /**
     * ES搜索数据source
     *
     * @param array $elasticData
     * @return array
     */
    public static function getEsSource(array $elasticData): array
    {
        $sources = [];
        foreach ($elasticData['hits']['hits'] as $item) {
            $sources[] = $item['_source'];
        }

        return $sources;
    }
}
