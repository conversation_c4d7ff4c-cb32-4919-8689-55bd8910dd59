<?php

namespace App\Services\Cms;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Cms\Category;
use App\Models\Cms\Content;
use App\Services\Admin\AttachmentRelationService;
use Illuminate\Support\Facades\DB;

class CategoryService
{
    /**
     * 获取分类ID集合
     *
     * @param array $where
     * @return array
     */
    public static function getCategoryIds(array $where): array
    {
        $build = Category::query();
        $where = [['visible', '=', Category::VISIBLE], $where];

        return $build->where($where)->pluck('id')->toArray();
    }

    /**
     * 通过分类名获取分类
     *
     * @param string $name 分类名
     * @return Category|null
     */
    public static function getCategoryByName(string $name): Category|null
    {
        /** @var Category $category */
        $category = Category::publicfields()
            ->where('name', $name)
            ->where('visible', Category::VISIBLE)
            ->first();
        if (!$category) {
            return null;
        }

        return $category;
    }

    /**
     * 通过分类名获取分类
     *
     * @param string $sid 分类名
     * @return Category|null
     */
    public static function getCategoryBySid(string $sid): Category|null
    {
        /** @var Category $category */
        $category = Category::whereSid($sid)
            ->publicfields()
            ->where('visible', Category::VISIBLE)
            ->first();
        if (!$category) {
            return null;
        }

        return $category;
    }

    /**
     * 通过sid获取path
     *
     * @param string $sid 分类sid
     * @return string|null
     */
    public static function getPathBySid(string $sid): string|null
    {
        return Category::whereSid($sid)
            ->where('visible', Category::VISIBLE)
            ->value('path');
    }

    /**
     * 创建分类
     * @param string $name
     * @param int $pid
     * @param array $append
     * @return Category
     * @throws ServiceException
     */
    public static function create(string $name, int $pid = 0, array $append = []): Category
    {
        $path = '';

        if ($pid > 0) {
            /** @var Category $parentCategory */
            $parentCategory = Category::query()->find($pid);
            if (!$parentCategory) {
                throw new ServiceException('父分类不存在');
            }

            $path = $parentCategory->path;
        }

        $category = new Category();
        $category->name = $name;
        $category->pid = $pid;

        foreach ($append as $key => $val) {
            if (in_array($key, $category->getFillable()) && $key != 'logo') {
                $category->{$key} = $val;
            }
        }

        $category->save();

        // 更新path
        $category->path = $path . $category->id . '-';
        $category->save();

        // 处理图标
        AttachmentRelationService::saveAttachment($category, $append, 'category', BusinessType::Category, true, 'logo', 'logo');

        return $category;
    }

    /**
     * 修改分类
     * @param int $id
     * @param array $data
     * @return Category
     * @throws ServiceException
     */
    public static function update(int $id, array $data): Category
    {
        /** @var Category $category */
        $category = Category::query()->find($id);

        if (!$category) {
            throw new ServiceException('分类不存在');
        }

        if (isset($data['pid']) && $category->id == $data['pid']) {
            throw new ServiceException('父类不能选择自己');
        }

        // 移动分类
        if (isset($data['pid']) && $category->pid != $data['pid']) {
            self::move($id, $data['pid']);
        }

        foreach ($data as $key => $val) {
            if (in_array($key, $category->getFillable()) && $key != 'logo') {
                $category->{$key} = $val;
            }
        }

        $category->save();

        // 处理图标
        AttachmentRelationService::saveAttachment($category, $data, 'category', BusinessType::Category, false,  'logo', 'logo');

        return $category;
    }

    /**
     * 移动分类
     * @param int $id
     * @param int $pid
     * @return Category
     * @throws ServiceException
     */
    public static function move(int $id, int $pid): Category
    {
        /** @var Category $category */
        $category = Category::query()->find($id);

        if (!$category) {
            throw new ServiceException('分类不存在');
        }

        if ($category->pid == $pid) {
            return $category;
        }

        /** @var Category $parentCategory */
        $parentCategory = Category::query()->find($pid);

        if (!$parentCategory) {
            throw new ServiceException('父分类不存在');
        }

        if (str_contains($category->path, $parentCategory->pid)) {
            throw new ServiceException('不能移动到子分类下');
        }

        $oldPath = $category->path;
        $oldParentPath = str_replace("$category->id-", '', $category->path);

        // 更新pid
        $category->pid = $pid;
        $category->save();

        // 更新path
        Category::query()->where('path', 'like', $oldPath . '%')->update(['path' => DB::raw("REPLACE(`path`, '$oldParentPath', '$parentCategory->path')")]);

        return $category;
    }

    /**
     * 删除分类
     * @param int $id
     * @return void
     * @throws ServiceException
     */
    public static function remove(int $id): void
    {
        /** @var Category $category */
        $category = Category::query()->find($id);

        if (!$category) {
            throw new ServiceException('分类不存在');
        }

        if (Category::query()->where('pid', $id)->exists()) {
            throw new ServiceException('分类下存在子分类，不能删除');
        }

        if (Content::query()->where('category_id', $category->id)->exists()) {
            throw new ServiceException('分类下存在内容，不能删除');
        }

        // 删除附件
        AttachmentRelationService::removeAttachment(BusinessType::Category, $category->id);

        $category->delete();
    }

    /**
     * 根据类型获取分类
     * @param string $classify
     * @param int|null $visible
     * @return array
     */
    public static function getCategoriesByClassify(string $classify = 'material', ?int $visible = null): array
    {
        $query = Category::query()->where('classify', $classify);

        if ($visible !== null) {
            $query->where('visible', $visible);
        }

        return $query->orderByRaw('sort desc, id asc')->get()->setHidden([])->toArray();
    }
}
