<?php

namespace App\Services\Cms;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Cms\Category;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourseSection;
use App\Models\Cms\Contract\BelongsToSetInterface;
use App\Models\Org\Course;
use App\Models\User;
use App\Services\Admin\AttachmentRelationService;
use App\Services\User\CreditService;
use App\Services\User\OwnContentService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ReflectionClass;
use Throwable;

class ContentService
{
    /**
     * 创建内容
     * @param string $title
     * @param int $categoryId
     * @param int $type
     * @param array $params
     * @return Content
     * @throws ServiceException
     */
    public static function create(string $title, int $categoryId, int $type, array $params = []): Content
    {
        /** @var Category $category */
        $category = Category::query()->find($categoryId);

        if (!$category) {
            throw new ServiceException('分类不存在');
        }

        $content = new Content();
        $content->category_id = $category->id;
        $content->type = $type;
        $content->title = $title;
        $content->intro = $params['intro'] ?? '';
        $content->source = $params['source'] ?? '';
        $content->status = Content::STATUS_DRAFT;
        $content->views_add = $params['views_add'];
        $content->admin_id = $params['admin_id'];

        self::handleViewLimit($content, $params);
        self::handleRecommend($content, $params);

        $content->save();

        // 处理关联数据
        self::handleRelations($content, $params);

        // 处理封面图
        AttachmentRelationService::saveAttachment($content, $params, 'content', BusinessType::Content);

        return $content;
    }


    /**
     * 更新内容
     * @param int $id
     * @param array $params
     * @return Content
     * @throws ServiceException
     */
    public static function update(int $id, array $params): Content
    {
        /** @var Content $content */
        $content = Content::query()->with(['category', 'relationList'])->find($id);

        if (!$content) {
            throw new ServiceException('内存不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $content->getFillable()) && $key != 'cover') {
                $content->{$key} = $val;
            }
        }

        self::handleViewLimit($content, $params);
        self::handleRecommend($content, $params);

        $content->save();

        // 处理关联数据
        self::handleRelations($content, $params);

        // 处理封面图
        AttachmentRelationService::saveAttachment($content, $params, 'content', BusinessType::Content, false);

        return $content;
    }

    /**
     * 同步es数据
     *
     * @param int $contentId 内容ID
     * @return Content|null
     */
    public static function getSyncEsData(int $contentId): Content|null
    {
        /** @var Content $content */
        $content = Content::query()
            ->select(['id', 'category_id', 'title', 'type', 'intro', 'status', 'release_at', 'created_at'])
            ->with('document', function ($query) {
                $query->select(['content_id', 'format']);
            })
            ->where('id', $contentId)
            ->first();
        if (!$content) {
            return null;
        }

        return $content;
    }

    /**
     * 内容列表
     *
     * @param int|null $userId
     * @return Builder
     */
    public static function getContentListBuilder(int|null $userId = null): Builder
    {
        $builder = Content::publicFields()
            ->withOwnState($userId ?? 0)
            ->with([
                'resource' => fn ($query) => $query->publicFields(),
                'category' => fn ($query) => $query->publicFields()
            ])
            ->where('status', Content::STATUS_NORMAL);

        if (config('heguibao.hidden_videos')) {
            $builder->whereIn('type', [Content::TYPE_DOC, Content::TYPE_RICH_TEXT]);
        }

        return $builder;
    }

    /**
     * 内容是否可观看
     *
     * @param int|null $userId
     * @param Content $content
     * @param int $enrollId 机构学员学习 ID
     * @return bool
     */
    public static function canView(int|null $userId, Content $content, int $enrollId = 0): bool
    {
        if (!$userId) {
            return false;
        }

        if ($content->view_limit == Content::VIEW_LIMIT_FREE) {
            return true;
        }

        $resourceClass = $content->getResourceClass();

        //是否属于集合，当某一个类型的内容可能被另外一个内容集合包含时，拥有该集合即代表拥有此内容
        $ref = new ReflectionClass($resourceClass);
        if ($ref->implementsInterface(BelongsToSetInterface::class)) {
            /** @var class-string<BelongsToSetInterface> $resourceClass */
            $setIds = $resourceClass::belongsToSet($content);
            if ($setIds) {
                return OwnContentService::checkPurchase($userId, [$content->id, ...$setIds], false, $enrollId);
            }
        }

        return OwnContentService::checkPurchase($userId, [$content->id], enrollId: $enrollId);
    }

    /**
     * 通过积分购买内容
     *
     * @param int $userId 用户ID
     * @param Content $content 内容模型实例
     * @return Content
     * @throws ServiceException
     * @throws Throwable
     */
    public static function buyContentByCredit(int $userId, Content $content): Content
    {
        if (!in_array($content->view_limit, [Content::VIEW_LIMIT_CREDIT, Content::VIEW_LIMIT_CREDIT_AMOUNT])) {
            throw new ServiceException("购买类型错误");
        }

        if ($content->charge_credit <= 0) {
            throw new ServiceException("资料积分异常");
        }

        try {
            DB::beginTransaction();

            // 扣除用户积分，增加用户积分变更日志
            CreditService::consume($userId, $content->charge_credit, $content->getBusinessType(), $content->id, "购买资料”{$content->title}”");
            // 增加用户资料
            OwnContentService::create($userId, $content->id, $content->getClassify());

            DB::commit();
            $content->forUser($userId);

            return $content->append(['attitude', 'download', 'favorite']);
        } catch (Throwable $e) {
            DB::rollBack();
            Log::error("购买资料异常，原因：" . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 内容发布
     * @param int $id
     * @return Content
     * @throws ServiceException
     */
    public static function release(int $id): Content
    {
        /** @var Content $content */
        $content = Content::query()->find($id);

        if (!$content) {
            throw new ServiceException('内容不存在');
        }

        if ($content->status == Content::STATUS_HIDE) {
            throw new ServiceException('内容已隐藏');
        }

        if ($content->status == Content::STATUS_NORMAL) {
            return $content;
        }

        // 课程节数处理
        if ($content->type == Content::TYPE_COURSE) {
            ContentCourseService::updateSectionCount($content->id);
        }

        $content->status = Content::STATUS_NORMAL;
        $content->status_desc = '';
        $content->release_at = Carbon::now();

        $content->save();

        return $content;
    }

    /**
     * 内容处理中
     * @param int $id 内容id
     * @param string $remark 备注信息
     * @param bool $check 是否需要检查内容状态
     * @return Content
     */
    public static function processing(int $id, string $remark = '', bool $check = false): Content
    {
        /** @var Content $content */
        $content = Content::query()->find($id);

        if (!$content) {
            throw new ServiceException('内容不存在');
        }

        $isUpdate = true;

        // 是否需要检查内容状态，主要解决课程问题，新增章节不影响内容显示
        if ($check && $content->status == Content::STATUS_NORMAL) {
            $isUpdate = false;
        }

        if ($isUpdate) {
            $content->status = Content::STATUS_PROCESSING;
            $content->status_desc = $remark;
            $content->save();
        }

        return $content;
    }

    /**
     * 批量删除内容
     * @param array $ids
     * @param bool $force
     * @return void
     * @throws ServiceException
     */
    public static function batchDelete(array $ids, bool $force = false): void
    {
        if ($force) {
            $contents = Content::withTrashed()->whereIn('id', $ids)->get();

            $contents->each(function (Content $content) {
                // 关联的资源删除，如：课程，文档等
                $content->resource?->delete();
                $content->forceDelete();
            });

            // 用户下载记录
            User\UserContentDownload::query()->whereIn('content_id', $ids)->delete();
            // 用户关联的资料
            User\UserOwnContent::query()->whereIn('content_id', $ids)->delete();
            // 用户点赞记录
            User\UserAttitude::query()->whereIn('business_type', [BusinessType::Content, BusinessType::CmsCourse, BusinessType::CmsNews, BusinessType::CmsMaterial])->whereIn('business_id', $ids)->delete();

            // 机构课程关联删除
            Course::query()->whereIn('course_id', $ids)->delete();

            // 内容关联的附件
            AttachmentRelationService::removeAttachment(BusinessType::Content, $ids);
        } else {
            if (User\UserOwnContent::query()->whereIn('content_id', $ids)->count() > 0) {
                throw new ServiceException('已有用户购买');
            }

            if (ContentCourseSection::query()->whereIn('ref_video_id', $ids)->count() > 0) {
                throw new ServiceException('已关联到课程');
            }

            Content::query()->whereIn('id', $ids)->delete();
        }
    }

    protected static function handleViewLimit(Content $content, array $params): void
    {
        if (!isset($params['view_limit'])) {
            return;
        }

        $chargeCredit = $params['charge_credit'] ?? 0;
        $chargeAmount = $params['charge_amount'] ?? '0.00';

        // 价格类型
        if ($params['view_limit'] == Content::VIEW_LIMIT_CREDIT) {
            $chargeAmount = '0.00';

            if ($chargeCredit <= 0) {
                throw new ServiceException('请输入积分数量');
            }
        } elseif ($params['view_limit'] == Content::VIEW_LIMIT_AMOUNT) {
            $chargeCredit = 0;

            if ($chargeAmount <= 0) {
                throw new ServiceException('请输入金额');
            }
        } else if ($params['view_limit'] == Content::VIEW_LIMIT_FREE) {
            $chargeCredit = 0;
            $chargeAmount = '0.00';
        }

        $content->view_limit = $params['view_limit'];
        $content->charge_credit = $chargeCredit;
        $content->charge_amount = $chargeAmount;
    }

    protected static function handleRecommend(Content $content, array $params): void
    {
        if (isset($params['recommend'])) {
            $content->recommend_at = $params['recommend'] ? Carbon::now() : null;
        }
    }

    protected static function handleDisplay(Content $content, array $params): void
    {
        if (isset($params['status'])) {
            if ($params['status'] == Content::STATUS_HIDE && $content->status != Content::STATUS_NORMAL) {
                throw new ServiceException('只有发布成功的内容才能隐藏');
            }

            $content->status = $params['status'];
        }
    }

    protected static function handleRelations(Content $content, array $params): void
    {
        if (isset($params['relation_ids'])) {
            ContentRelationService::create($content, $params['relation_ids']);
        }
    }

    /**
     * 通过内容ID获取内容
     *
     * @param string $contentId 内容ID
     * @return Content|null
     */
    public static function getContentById(string $contentId): ?Content
    {
        return Content::whereSid($contentId)
            ->publicFields()->with('resource', function ($query) {
                $query->detail();
            })
            ->where('status', Content::STATUS_NORMAL)
            ->first();
    }
}
