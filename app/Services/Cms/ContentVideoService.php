<?php

namespace App\Services\Cms;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Libs\AsyncTasks\ContentVideoTranscodeTask;
use App\Models\Cms\ContentVideo;
use App\Services\Common\AsyncTaskService;
use App\Services\Common\AttachmentService;

class ContentVideoService
{
    /**
     * 保存视频
     * @param int $id
     * @param array $params
     * @param bool $isSend
     * @return ContentVideo
     * @throws ServiceException
     */
    public static function update(int $id, array $params, bool $isSend = false): ContentVideo
    {
        $groupDir = 'content';
        $targetType = BusinessType::Content;

        /** @var ContentVideo $video */
        $video = ContentVideo::query()->where('content_id', $id)->first();

        if (!$video) {
            $video = new ContentVideo();
            $video->content_id = $id;
        }

        if (isset($params['filepath']) && $video->filepath != $params['filepath']) {
            if ($video->filepath) {
                AttachmentService::removeRelations($targetType, $video->content_id, $video->filepath);
            }

            $attachment = AttachmentService::store($params['filepath'], $groupDir, $targetType, $video->content_id);

            $video->filepath = $attachment->path;
            $video->filesize = $attachment->filesize;
            $video->save();
        }

        // 发布，视频需要转码
        if ($isSend && !$video->duration) {
            $task = new ContentVideoTranscodeTask($video->content_id);
            AsyncTaskService::start($task, BusinessType::Content, $video->content_id);

            ContentService::processing($video->content_id, '视频转码中');
        }

        return $video;
    }
}
