<?php

namespace App\Services\Cms;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Admin\Role;
use App\Models\Cms\Content;
use App\Models\Cms\Special;
use App\Models\Cms\SpecialContent;
use App\Services\Admin\AttachmentRelationService;
use App\Services\Admin\RoleService;
use Carbon\Carbon;

class SpecialService
{
    public static function create(int $adminId, array $params = []): Special
    {
        self::checkContentIds($adminId, $params['content_ids']);

        $special = new Special();

        foreach ($params as $key => $val) {
            if (in_array($key, $special->getFillable()) && $key != 'cover') {
                $special->{$key} = $val;
            }
        }

        self::handleRecommend($special, $params);

        $special->admin_id = $adminId;
        $special->save();

        // 处理关联内容
        self::handleSpecialContents($special, $params);

        // 处理封面
        AttachmentRelationService::saveAttachment($special, $params, 'special', BusinessType::Special);

        return $special;
    }

    public static function update(int $id, array $params): Special
    {
        /** @var Special $special */
        $special = Special::query()->find($id);

        if (!$special) {
            throw new ServiceException('专题不存在');
        }

        self::checkContentIds($special->admin_id, $params['content_ids']);

        foreach ($params as $key => $val) {
            if (in_array($key, $special->getFillable()) && $key != 'cover') {
                $special->{$key} = $val;
            }
        }

        self::handleRecommend($special, $params);

        $special->save();

        // 处理关联内容
        self::handleSpecialContents($special, $params);

        // 处理图标
        AttachmentRelationService::saveAttachment($special, $params, 'special', BusinessType::Special, false);

        return $special;
    }

    public static function recommend(int $id, array $params): Special
    {
        /** @var Special $special */
        $special = Special::query()->find($id);

        if (!$special) {
            throw new ServiceException('专题不存在');
        }

        self::handleRecommend($special, $params);

        $special->save();

        return $special;
    }

    public static function remove(int $id): void
    {
        /** @var Special $special */
        $special = Special::query()->find($id);

        if (!$special) {
            throw new ServiceException('专题不存在');
        }

        // 删除附件
        AttachmentRelationService::removeAttachment(BusinessType::Special, $special->id);

        $special->delete();

        SpecialContent::query()->where('special_id', $id)->delete();
    }

    protected static function handleRecommend(Special $special, array $params): void
    {
        if (isset($params['recommend'])) {
            $special->recommend_at = $params['recommend'] ? Carbon::now() : null;
        }
    }

    protected static function handleSpecialContents(Special $special, array $params): void
    {
        if (!isset($params['content_ids'])) return;

        $relations = SpecialContent::query()->where('special_id', $special->id)->get();

        $oldContentIds = $relations->pluck('content_id')->toArray();
        $removeContentIds = array_diff($oldContentIds, $params['content_ids']);
        $createContentIds = array_diff($params['content_ids'], $oldContentIds);

        $orders = array_flip($params['content_ids']);

        // 删除关联内容
        if (!empty($removeContentIds)) {
            SpecialContent::query()->where('special_id', $special->id)->whereIn('content_id', $removeContentIds)->delete();
        }

        // 检查关联内容是否都存在
        if (!empty($createContentIds)) {
            $createContentCount = Content::query()->whereIn('id', $createContentIds)->count();

            if ($createContentCount != count($createContentIds)) {
                throw new ServiceException('选择的内容不存在或已删除');
            }

            foreach ($createContentIds as $contentId) {
                $model = new SpecialContent();
                $model->special_id = $special->id;
                $model->content_id = $contentId;
                $model->order = $orders[$contentId];
                $model->save();
            }
        }

        // 判定是否要更新原有的排序
        $existsContentIds = array_intersect($params['content_ids'], $oldContentIds);

        if ($existsContentIds) {
            $relationsByKey = $relations->keyBy('content_id');
            foreach ($existsContentIds as $contentId) {
                $model = $relationsByKey->get($contentId);
                $order = $orders[$contentId];
                if ($model->order != $order) {
                    $model->order = $order;
                    $model->save();
                }
            }
        }
    }

    protected static function checkContentIds(int $adminId, array $contentIds): void
    {
        $contentAdminIds = Content::query()->whereIn('id', $contentIds)->groupBy('admin_id')->pluck('admin_id')->toArray();

        if (count($contentAdminIds) != 1) {
            throw new ServiceException('专题只能设置同一用户的内容');
        }

        if (!RoleService::checkPermission($adminId, Role::PERMISSION_MANAGE_EDITOR) && !in_array($adminId, $contentAdminIds)) {
            throw new ServiceException('专题只能设置自己的内容');
        }
    }
}
