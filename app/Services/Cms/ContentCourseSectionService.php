<?php

namespace App\Services\Cms;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCourseSection;
use App\Services\Admin\AttachmentRelationService;
use App\Services\Common\AttachmentService;

class ContentCourseSectionService
{
    /**
     * 创建
     * @param int $chapterId
     * @param string $name
     * @param int $refVideoId
     * @param string $filepath
     * @param int $sort
     * @return ContentCourseSection
     */
    public static function create(int $chapterId, string $name, int $refVideoId, string $filepath, int $sort = 0): ContentCourseSection
    {
        /** @var ContentCourseChapter $chapter */
        $chapter = ContentCourseChapter::query()->find($chapterId);

        if (!$chapter) {
            throw new ServiceException('该章不存在');
        }

        $section = new ContentCourseSection();
        $section->content_id = $chapter->content_id;
        $section->chapter_id = $chapterId;
        $section->ref_video_id = $refVideoId;
        $section->name = $name;
        $section->status = ContentCourseSection::STATUS_PADDING;
        $section->sort = $sort;

        // 保存视频
        if ($filepath) {
            self::saveAttachment($section, $filepath);
        }

        $section->save();

        return $section;
    }

    /**
     * 修改
     * @param int $id
     * @param array $params
     * @return ContentCourseSection
     * @throws ServiceException
     */
    public static function update(int $id, array $params): ContentCourseSection
    {
        /** @var ContentCourseSection $section */
        $section = ContentCourseSection::query()->find($id);

        if (!$section) {
            throw new ServiceException('该节不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $section->getFillable()) && $key != 'filepath') {
                $section->{$key} = $val;
            }
        }

        // 保存视频
        if (isset($params['filepath']) && $params['filepath'] && $section->filepath != $params['filepath']) {
            self::saveAttachment($section, $params['filepath']);
        }

        $section->save();

        return $section;
    }

    /**
     * 删除
     * @param int $id
     * @return void
     * @throws ServiceException
     */
    public static function remove(int $id): void
    {
        /** @var ContentCourseSection $section */
        $section = ContentCourseSection::query()->find($id);

        if (!$section) {
            throw new ServiceException('该节不存在');
        }

        AttachmentRelationService::removeAttachment(BusinessType::Content, $section->content_id, $section->filepath);

        $section->delete();
    }

    protected static function saveAttachment(ContentCourseSection $section, string $filepath): void
    {
        $groupDir = 'content';
        $targetType = BusinessType::Content;

        if ($section->filepath) {
            AttachmentService::removeRelations($targetType, $section->content_id, $section->filepath);
        }

        $attachment = AttachmentService::store($filepath, $groupDir, $targetType, $section->content_id);

        // 重置附件时长，外部方法保存
        $section->filepath = $attachment->path;
        $section->filesize = $attachment->filesize;
        $section->extend = null;
        $section->duration = 0;
        $section->status = ContentCourseSection::STATUS_PADDING;
    }

    /**
     * 设置小节播放地址为空
     *
     * @param ContentCourseSection $section
     * @return ContentCourseSection
     */
    public static function setEmptySrc(ContentCourseSection $section): ContentCourseSection
    {
        $section->video_src = "";
        $section->filepath = "";
        $section->filepath_src = "";
        $section->extend = [];

        return $section;
    }

    /**
     * 获取课程总时长
     *
     * @param int $contentId
     * @return int
     */
    public static function getCourseDuration(int $contentId): int
    {
        $duration = 0;

        $sections = ContentCourseSection::query()
            ->select(['duration', 'ref_video_id'])
            ->with('video')
            ->where('content_id', $contentId)
            ->where('status', ContentCourseSection::STATUS_SHOW)
            ->get();
        if (empty($sections)) {
            return $duration;
        }

        /** @var ContentCourseSection $section */
        foreach ($sections as $section) {
            $duration += $section->actual_duration;
        }

        return $duration;
    }
}
