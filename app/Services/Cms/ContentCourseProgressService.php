<?php

namespace App\Services\Cms;

use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePackList;
use App\Models\Cms\ContentCourseProgress;
use App\Models\Cms\ContentCourseSection;
use App\Models\Cms\ContentCourseStatus;
use App\Models\User;
use App\Models\User\UserOwnContent;
use App\Services\Org\CoursePackService;
use App\Services\Org\CourseService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\PreconditionFailedHttpException;

class ContentCourseProgressService
{
    /**
     * 获取课程进度
     *
     * @param int|null $userId 用户ID
     * @param int $contentId 内容ID
     * @param int $orgId 机构ID
     * @param int $enrollId 报名ID
     * @return ContentCourseProgress|null
     */
    public static function progress(int|null $userId, int $contentId, int $orgId = 0, int $enrollId = 0): ContentCourseProgress|null
    {
        if (!$userId) {
            return null;
        }

        /** @var ContentCourseProgress $process */
        $process = ContentCourseProgress::query()
            ->where('user_id', $userId)
            ->where('content_id', $contentId)
            ->where('org_id', $orgId)
            ->where('enroll_id', $enrollId)
            ->orderByDesc('updated_at')
            ->first();
        if (!$process) {
            return null;
        }
        $process->append('section_sid');

        return $process;
    }


    /**
     * 用户学习进度
     *
     * @param Collection $progresses
     * @return array
     */
    public static function getUserProgresses(Collection $progresses): array
    {
        $userProgresses = [];
        /** @var ContentCourseProgress $progress */
        foreach ($progresses as $progress) {
            $sectionDuration = $progress->section->video ? $progress->section->video->duration : $progress->section->duration;
            $studyDuration = $progress->finished ? $sectionDuration : ($progress->duration <= $sectionDuration ? $progress->duration : $progress->pos);
            if (isset($userProgresses[$progress->user_id])) {
                $userProgresses[$progress->user_id]['study_duration'] += $studyDuration;
            } else {
                $userProgresses[$progress->user_id]['study_duration'] = $studyDuration;
            }
        }

        return $userProgresses;
    }

    public static function progressPercent(int $studyDuration, int $sectionDuration): string
    {
        return bcmul(bcdiv($studyDuration, $sectionDuration, 4), 100, 2) . '%';
    }

    public static function getProgressBuilder(int $contentId, array $userIds): Builder
    {
        return ContentCourseProgress::query()
            ->select('user_id')
            ->where('content_id', $contentId)
            ->whereIn('user_id', $userIds);
    }

    /**
     * 设置课程学习进度
     *
     * @param UserOwnContent $record
     * @param array $userProgresses
     * @param int $courseDuration
     * @return UserOwnContent
     */
    public static function setProgress(UserOwnContent $record, array $userProgresses, int $courseDuration): UserOwnContent
    {
        if (isset($userProgresses[$record->user_id])) {
            if ($userProgresses[$record->user_id]['study_duration'] == $courseDuration) {
                $record->progress_status = 3;
                $record->progress ='100%';
            } else {
                $record->progress_status = 2;
                $record->progress = ContentCourseProgressService::progressPercent($userProgresses[$record->user_id]['study_duration'], $courseDuration);
            }
        } else {
            $record->progress_status = 1;
            $record->progress = '0%';
        }

        return $record;
    }

    /**
     * 获取指定多个课程的学习进度
     * @param int $userId
     * @param array $contentIds
     * @param int $orgId
     * @param int $enrollId
     * @return array
     */
    public static function multipleCourseProgresses($userId, $contentIds, $orgId = 0, $enrollId = 0): array
    {
        $percents = ContentCourseStatus::query()
            ->where('user_id', $userId)
            ->whereIn('content_id', $contentIds)
            ->where('org_id', $orgId)
            ->where('enroll_id', $enrollId)
            ->get()
            ->keyBy('content_id')
            ->map(function ($item) {
                return $item->valid_duration;
            })
            ->toArray();
            
        foreach ($contentIds as $contentId) {
            if (!isset($percents[$contentId])) {
                $percents[$contentId] = 0;
            } else {
                $contentCourse = ContentCourse::query()->where('content_id', $contentId)->first();
                list($courseHour, $courseDuration) = CourseService::getCourseHourAndDuration($contentCourse, $orgId);
                list($studyHour, $studyPercent) = $contentCourse->studyHourForPercent($percents[$contentId], $courseHour);
                
                $percents[$contentId] = $studyPercent;
            }
        }

        return $percents;
    }

    /**
     * 用户机构课程学习进度
     *
     * @param int $userId 用户ID
     * @param int $orgId 机构ID
     * @param int $enrollId
     * @param int $contentId 课程ID
     * @return array
     */
    public static function orgCourseProgress(int $userId, int $orgId, int $enrollId, int $contentId): array
    {
        $status = ContentCourseStatus::query()
            ->where('user_id', $userId)
            ->where('content_id', $contentId)
            ->where('org_id', $orgId)
            ->where('enroll_id', $enrollId)
            ->first();
        
        if ($status === null) {
            return [ 'study_hour' => 0, 'study_percent' => 0];
        }
        
        return ['study_hour' => $status->hour, 'study_percent' => $status->percent];
    }

    /**
     * 计算课程包学习进度
     *
     * @param int $userId
     * @param int $orgId
     * @param int $enrollId
     * @param int $coursePackId
     * @return array
     */
    public static function orgCoursePackProgress(int $userId, int $orgId, int $enrollId, int $coursePackId, float $courseHour)
    {
        if ($courseHour == 0) {
            return ['study_hour' => 0, 'study_percent' => 0];
        }

        $courseIds = ContentCoursePackList::query()
            ->where('content_id', $coursePackId)
            ->pluck('course_id')
            ->toArray();

        // 根据课程配置计算学时
        $contentCourses = ContentCourse::query()
            ->select(['hour_per_minutes', 'content_id'])
            ->whereIn('content_id', $courseIds)
            ->get()
            ->keyBy('content_id');

        $map = ContentCourseStatus::query()
            ->where('user_id', $userId)
            ->whereIn('content_id', $courseIds)
            ->where('org_id', $orgId)
            ->where('enroll_id', $enrollId)
            ->get()
            ->keyBy('content_id')
            ->map(function ($item) {
                return $item->valid_duration;
            })
            ->toArray();

        $studyHour = 0;
        $studyDuration = 0;

        // 重新计算学时是因为表中记录的精度问题
        foreach ($courseIds as $contentId) {
            $studyHour += $contentCourses->get($contentId)->studyHour($map[$contentId] ?? 0, false);
            $studyDuration += $map[$contentId] ?? 0;
        }
        $coursePackDuration = CoursePackService::calCoursePackDuration($orgId, $coursePackId);

        $studyHour > $courseHour && $studyHour = (float)"$courseHour.0";
        $studyPercent = number_format($studyDuration / $coursePackDuration * 100, 1, '.', '');

        return ['study_hour' => $studyHour, 'study_percent' => $studyPercent];
    }

    /**
     * 获取当前学习章节
     *
     * @param int $userId
     * @param int $contentId
     * @param int $enrollId
     * @return ContentCourseSection|null
     */
    public static function currentSection(int $userId, int $contentId, int $enrollId = 0): ?ContentCourseSection
    {
        /** @var ContentCourseProgress $progress */
        $progress = ContentCourseProgress::query()
            ->with('section', fn ($query) => $query->publicFields())
            ->where('user_id', $userId)
            ->where('content_id', $contentId)
            ->where('enroll_id', $enrollId)
            ->orderByDesc('updated_at')
            ->first();

        return $progress?->section;
    }

    /**
     * 避免跨端播放
     *
     * @param User $user 用户模型实例
     * @return void
     */
    public static function avoidCrossPlay(User $user): void
    {
        $token = $user->currentAccessToken()->token;

        $cacheKey = "learn_progress:$user->id";
        $cacheData = Cache::get($cacheKey);

        try {
            if ($cacheData) {
                $currentToken = $cacheData['current_token'];// 缓存中当前登录token
                $kickedTokens = $cacheData['kicked_tokens'];// 缓存中被剔除的token集合

                if (in_array($token, $kickedTokens)) {
                    Log::info("用户{$user->id}已在其他客户端播放，当前登录token：{$token}在缓存中被剔除的tokens集合中", $cacheData);
                    $cacheData['kicked_tokens'] = array_values(array_filter($kickedTokens, fn ($kickedToken) => $kickedToken != $token));

                    throw new PreconditionFailedHttpException("已在其他客户端播放！");
                } elseif ($currentToken != $token) {
                    Log::info("用户{$user->id}播放视频，当前登录token：{$token}非缓存中的当前token：$currentToken", $cacheData);

                    // 更新缓存中current_token为当前登录的token
                    $cacheData['current_token'] = $token;
                    // 将缓存中的原current_token增加到剔除tokens集合中
                    $cacheData['kicked_tokens'][] = $currentToken;
                }
            } else {
                $cacheData = ['current_token' => $token, 'kicked_tokens' => []];
            }
        } finally {
            Cache::put($cacheKey, $cacheData, 20);
        }
    }

    /**
     * 获取用户学习进度
     *
     * @param int $userId 用户ID
     * @param int $contentId 内容ID
     * @param int $orgId 机构ID
     * @param int $enrollId 报名ID
     * @return Collection
     */
    public static function getCourseProgress(int $userId, int $contentId, int $orgId = 0, int $enrollId = 0): Collection
    {
        return ContentCourseProgress::query()
            ->where('user_id', $userId)
            ->where('content_id', $contentId)
            ->where('org_id', $orgId)
            ->where('enroll_id', $enrollId)
            ->get()
            ->keyBy('section_id');
    }

    /**
     * 获取用户多课程学习进度
     *
     * @param int $userId 用户ID
     * @param int[] $contentIds 课程ID数组
     * @param int $orgId 机构ID
     * @param int $enrollId 报名ID
     * @return \Illuminate\Support\Collection
     */
    public static function getCoursesProgress(int $userId, array $contentIds, int $orgId = 0, int $enrollId = 0)
    {
        return ContentCourseProgress::query()
            ->where('user_id', $userId)
            ->where('org_id', $orgId)
            ->where('enroll_id', $enrollId)
            ->whereIn('content_id', $contentIds)
            ->get()
            ->keyBy('section_id');
    }
}
