<?php

namespace App\Services\Topic;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Train\Subject;
use App\Models\Train\SubjectOption;
use App\Services\Common\AttachmentService;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Psr\Log\LoggerInterface;
use Throwable;

class ImportService
{
    protected LoggerInterface $logger;

    public function __construct()
    {
        $this->logger = Log::channel('task');
    }

    public function import(int $topicId, string $filepath): array
    {
        if (!file_exists($filepath)) {
            throw new ServiceException('导入文件不存在');
        }

        if (str_ends_with($filepath, '.xlsx')) {
            $data = $this->getExcelData($filepath);
        } else {
            $data = $this->getWordData($filepath);
        }

        $this->saveData($topicId, $data);

        return $data;
    }

    public function saveData(int $topicId, array $data): void
    {
        if (empty($data)) {
            return;
        }

        $now = now();

        foreach ($data as $item) {
            $subject = new Subject();
            $subject->topic_id = $topicId;
            $subject->type = $item['type'];
            $subject->intro = '';
            $subject->judge_correct = $item['judge_correct'];
            $subject->answer = '';
            $subject->analysis = '';

            // 检查是否有图片处理
            $hasImage = false;
            if ($this->hasImage($item['question']) || $this->hasImage($item['answer']) || $this->hasImage($item['analysis'])) {
                $hasImage = true;
            }

            if ($hasImage) {
                $subject->save();

                $subject->intro = $this->dataToHtml($item['question'], $subject->id, BusinessType::Subject);
                $subject->answer = $this->dataToHtml($item['answer'], $subject->id, BusinessType::Subject);
                $subject->analysis = $this->dataToHtml($item['analysis'], $subject->id, BusinessType::Subject);
            } else {
                $subject->intro = $this->dataToHtml($item['question']);
                $subject->answer = $this->dataToHtml($item['answer']);
                $subject->analysis = $this->dataToHtml($item['analysis']);
            }

            $subject->save();

            // 选择题选项处理
            if (in_array($item['type'], [Subject::TYPE_SC, Subject::TYPE_MC])) {
                $options = [];

                foreach ($item['options'] as $option) {
                    $options[] = [
                        'subject_id' => $subject->id,
                        'name' => $this->dataToHtml($option['name'], $subject->id, BusinessType::Subject),
                        'is_correct' => $option['is_correct'],
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }

                SubjectOption::query()->insert($options);
            }
        }
    }

    public function getExcelData(string $filepath): array
    {
        $spreadsheet = IOFactory::load($filepath);

        $rows = [];
        foreach ($spreadsheet->getActiveSheet()->getRowIterator() as $row) {
            $rowData = [];

            foreach ($row->getCellIterator() as $cell) {
                $rowData[] = $cell->getValue();
            }

            $rows[] = $rowData;
        }

        $data = [];

        $questionIndex = 0;
        $answerIndex = 0;
        $typeIndex = 0;
        $optionIndex = 0;
        $analysisIndex = 0;

        foreach ($rows as $index => $row) {
            if ($index === 0) {
                foreach ($row as $cIndex => $cell) {
                    if (str_contains($cell, '题目')) {
                        $questionIndex = $cIndex;
                    } elseif (str_contains($cell, '答案')) {
                        $answerIndex = $cIndex;
                    } elseif (str_contains($cell, '题型')) {
                        $typeIndex = $cIndex;
                    } elseif (str_contains($cell, '选项')) {
                        $optionIndex = $cIndex;
                    } elseif (str_contains($cell, '分析')) {
                        $analysisIndex = $cIndex;
                    }
                }
                continue;
            }

            $data[] = [
                'question' => $row[$questionIndex],
                'answer' => $row[$answerIndex],
                'type' => $row[$typeIndex],
                'analysis' => $row[$analysisIndex],
                'options' => $row[$optionIndex],
            ];
        }

        return $this->parseData($data);
    }

    public function getWordData(string $filepath): array
    {
        return [];
    }

    /**
     * 解析文档中的数据
     * @param array $data
     * @return array
     */
    public function parseData(array $data): array
    {
        $subjectType = [
            '单选题' => Subject::TYPE_SC,
            '多选题' => Subject::TYPE_MC,
            '判断题' => Subject::TYPE_TF,
            '问答题' => Subject::TYPE_QA,
        ];

        $subjects = [];

        foreach ($data as $index => $row) {
            $rowIndex = $index + 2;
            if (!isset($subjectType[$row['type']])) {
                throw new ServiceException("第{$rowIndex}行 题型不支持");
            }

            if (empty($row['answer'])) {
                throw new ServiceException("第{$rowIndex}行 未设置答案");
            }

            $subject = [
                'question' => $row['question'],
                'answer' => '',
                'judge_correct' => 0,
                'type' => $subjectType[$row['type']],
                'analysis' => $row['analysis'],
                'options' => [],
            ];

            if ($subject['type'] === Subject::TYPE_TF) {
                $subject['judge_correct'] = $row['answer'] == '正确' ? 1 : 2;
            } elseif ($subject['type'] === Subject::TYPE_QA) {
                $subject['answer'] = $row['answer'];
            } elseif (in_array($subject['type'], [Subject::TYPE_SC, Subject::TYPE_MC])) {
                $options = preg_split('/[a-zA-Z][:.]/', $row['options'],-1, PREG_SPLIT_NO_EMPTY);

                if (empty($options)) {
                    throw new ServiceException("第{$rowIndex}行 选项有误");
                }

                $correctCount = 0;
                foreach ($options as $oIndex => $option) {
                    $isCorrect = $this->toChoiceIsCorrect($row['answer'], $oIndex);

                    if ($isCorrect) {
                        $correctCount++;
                    }

                    $subject['options'][] = [
                        'name' => $option,
                        'is_correct' => $isCorrect ? 1 : 0,
                    ];
                }

                if ($subject['type'] == Subject::TYPE_SC) {
                    if ($correctCount != 1) {
                        throw new ServiceException("第{$rowIndex}行 单选题答案设置有误");
                    }
                } else {
                    if ($correctCount < 2) {
                        throw new ServiceException("第{$rowIndex}行 多选题答案设置有误");
                    }
                }
            }

            $subjects[] = $subject;
        }

        return $subjects;
    }

    /**
     * 返回当前选项是否正确
     * @param array|string $answer 选择的答案
     * @param int $optionIndex
     * @return bool
     */
    public function toChoiceIsCorrect(array|string $answer, int $optionIndex): bool
    {
        if (is_string($answer)) {
            $answer = str_replace(',','', strtoupper(trim($answer)));
            $answer = str_replace('，','', $answer);
            $answer = str_split($answer);
        }

        $selectOptions = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P"];


        $selects = [];
        foreach ($answer as $select) {
            $selects[] = array_search($select, $selectOptions);
        }

        return in_array($optionIndex, $selects);
    }

    public function hasImage($data): bool
    {
        if (empty($data)) {
            return false;
        }

        if (is_string($data)) {
            $data = [$data];
        }

        $bool = false;
        foreach ($data as $item) {
            if (str_contains($item, '/images/')) {
                $bool = true;
                break;
            }
        }

        return $bool;
    }

    public function handleImage($path, $targetId, $targetType): string
    {
        // todo 文档中图片兼容用要处理
        $path = str_replace('/images/', '', $path);
        $path = config('app.url') . '/storage/train/' . $path;

        $download = AttachmentService::download($path, upload: true, diskName: config('heguibao.storage.pub'));
        $attachment = AttachmentService::store($download['key'], 'topic', $targetType, $targetId);

        return $attachment->url;
    }

    public function dataToHtml($data, $targetId = 0, $targetType = ''): string
    {
        if (empty($data)) {
            return '';
        }

        if (is_string($data)) {
            $data = [$data];
        }

        $html = '';
        foreach ($data as $item) {
            if (str_contains($item, '/images/')) {
                // 图片处理
                try {
                    $imageSrc = $this->handleImage($item, $targetId, $targetType);
                } catch (Throwable $e) {
                    $this->logger->warning("下载图片失败：{$e->getMessage()}，$item");

                    $imageSrc = "https://s.shiwusuo100.com/static/images/no-image.jpg";
                }

                $html .= "<img src='$imageSrc' alt='' width='100%'>\n";
            } elseif (str_contains($item, 'html_table')) {
                // 表格处理
                $tableData = json_decode(str_replace('html_table ', '', $item), true);

                $html .= $this->handleTable($tableData);
            } else {
                // 默认 p 标签处理
                $html .= "<p>$item</p>\n";
            }
        }

        return $html;
    }

    protected function handleTable($data): string
    {
        $table = "<table border='1' style='border-collapse: collapse; width: 100%; text-align: center;'>";

        $total = count($data[0]);

        foreach ($data as $row) {
            $count = count($row);
            $start = $total - $count + 1;

            $colspan = [];
            while ($start < $total) {
                $colspan[] = 1;
                $start++;
            }

            $colspan[] = $total - $count + 1;

            $table .= "<tr>";
            foreach ($row as $index => $cell) {
                $table .= "<td colspan='$colspan[$index]'>$cell</td>";
            }
            $table .= "</tr>";
        }

        $table .= "</table>\n";

        return $table;
    }
}
