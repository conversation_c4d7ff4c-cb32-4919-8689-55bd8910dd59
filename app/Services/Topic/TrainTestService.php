<?php

namespace App\Services\Topic;

use App\Exceptions\ServiceException;
use App\Models\Org\Enrollment;
use App\Models\Train\Subject;
use App\Models\Train\Test;
use App\Models\Train\TestSubject;

class TrainTestService
{
    /**
     * 最后一次考试信息
     *
     * @param Enrollment $enrollment
     * @return Test|null
     */
    public static function lastTrainTest(Enrollment $enrollment): ?Test
    {
        /** @var Test $test */
        $test = Test::query()
            ->with(['topic', 'subjects', 'subjects.subject', 'subjects.subject.options'])
            ->where('user_id', $enrollment->user_id)
            ->where('topic_id', $enrollment->resource->topic_id)
            ->where('enroll_id', $enrollment->id)
            ->where('type', Test::TYPE_EXAM)
            ->where('status', Test::STATUS_STOP)
            ->latest('id')
            ->first();

        if (!$test) {
            return null;
        }

        return $test;
    }

    /**
     * 选项答案
     *
     * @param Test $test
     * @return array
     */
    public static function subjectInfo(Test $test): array
    {
        $testSubjects = $test->subjects;
        $data = [];
        foreach ($testSubjects as $testSubject) {
            $subject = $testSubject->subject;
            $content = $subject->intro;
            $options = [];
            $studentAnswer = '';
            $correctAnswer = '';
            switch ($subject->type) {
                case Subject::TYPE_SC:
                case Subject::TYPE_MC:
                    $select = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
                    foreach ($subject->options as $k => $option) {
                        $options[$option->id] = $select[$k] . '.' . $option->name;
                        if ($option->is_correct) {
                            $correctAnswer .= $select[$k];
                        }
                    }
                    $answerOptionIds = explode(',', $testSubject->option_id);
                    foreach ($answerOptionIds as $optionId) {
                        $studentAnswer .= $optionId ? substr($options[$optionId], 0, 1) : '未答';
                    }
                    break;
                case Subject::TYPE_TF:
                    $options = [
                        1 => 'A.正确',
                        2 => 'B.错误'
                    ];
                    $studentAnswer = substr($options[$testSubject->option_id], 0, 1);
                    $correctAnswer = $testSubject->correct ? $studentAnswer : ($testSubject->option_id == 1 ? 'B' : 'A');
                    break;
                default:
                    throw new ServiceException("类型错误");
            }

            $data[] = [
                'content' => strip_tags($content),
                'options' => array_values($options),
                'student_answer' => $studentAnswer,
                'correct_answer' => $correctAnswer,
                'is_correct' => $testSubject->correct == TestSubject::CORRECT_RIG ? '正确' : '错误',
            ];
        }

        return $data;
    }
}
