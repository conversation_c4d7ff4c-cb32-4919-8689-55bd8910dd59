<?php

namespace App\Services\Topic;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Train\Section;
use App\Models\Train\Subject;
use App\Models\Train\SubjectOption;
use App\Services\Common\AttachmentService;
use Illuminate\Support\Facades\Log;
use Throwable;

class SubjectService
{
    public static function create(array $params): Subject
    {
        $params = self::handleCreateOrUpdateParams(intval($params['section_id'] ?? 0), intval($params['type']), $params);

        $subject = new Subject();

        foreach ($params as $key => $val) {
            if (in_array($key, $subject->getFillable())) {
                $subject->{$key} = $val;
            }
        }

        $subject->save();

        // 处理选项
        if (isset($params['options'])) {
            foreach ($params['options'] as $index => $item) {
                $option = new SubjectOption();
                $option->subject_id = $subject->id;
                $option->sn = $index;
                $option->name = $item['name'];
                $option->is_correct = $item['is_correct'];
                $option->save();
            }
        }

        // 处理富文本里面的附件
        self::handleAttachments($subject, $params);

        return $subject;
    }

    public static function update(int $id, array $params): Subject
    {
        /** @var Subject $subject */
        $subject = Subject::query()->find($id);

        if (!$subject) {
            throw new ServiceException('题目不存在');
        }

        $params = self::handleCreateOrUpdateParams(intval($params['section_id'] ?? 0), $subject->type, $params);

        foreach ($params as $key => $val) {
            if (in_array($key, $subject->getFillable())) {
                $subject->{$key} = $val;
            }
        }

        $subject->save();

        // 处理选项
        if (isset($params['options'])) {
            foreach ($params['options'] as $index => $item) {
                if (isset($item['id']) && $item['id']) {
                    SubjectOption::query()->where('id', $item['id'])->update([
                        'sn' => $index,
                        'name' => $item['name'],
                        'is_correct' => $item['is_correct']
                    ]);
                } else {
                    $option = new SubjectOption();
                    $option->subject_id = $subject->id;
                    $option->sn = $index;
                    $option->name = $item['name'];
                    $option->is_correct = $item['is_correct'];
                    $option->save();
                }
            }
        }

        // 处理富文本里面的附件
        self::handleAttachments($subject, $params);

        return $subject;
    }

    public static function remove(array $ids, bool $force = false): void
    {
        if ($force) {
            Subject::query()->whereIn('id', $ids)->forceDelete();

            SubjectOption::query()->whereIn('subject_id', $ids)->delete();

            AttachmentService::removeRelations(BusinessType::Subject, $ids);
        } else {
            Subject::query()->whereIn('id', $ids)->delete();
        }
    }

    public static function getTypeCount(int $topicId): array
    {
        $result = [
            'single_choice' => 0,
            'multiple_choice' => 0,
            'judge' => 0,
        ];

        $stats = Subject::query()
            ->selectRaw("type,count(*) count")
            ->where('topic_id', $topicId)
            ->groupBy('type')
            ->get();

        if ($stats) {
            foreach ($stats as $item) {
                if ($item->type == Subject::TYPE_SC) {
                    $result['single_choice'] = $item->count;
                } elseif ($item->type == Subject::TYPE_MC) {
                    $result['multiple_choice'] = $item->count;
                } elseif ($item->type == Subject::TYPE_TF) {
                    $result['judge'] = $item->count;
                }
            }
        }

        return $result;
    }

    protected static function handleCreateOrUpdateParams(int $sectionId, int $type, array $params): array
    {
        // 问答题
        if ($type == Subject::TYPE_QA && empty($params['answer'])) {
            throw new ServiceException('问答题答案必需设置');
        }

        // 选择题
        if (in_array($type, [Subject::TYPE_SC, Subject::TYPE_MC]) && empty($params['options'])) {
            throw new ServiceException('单选或多选题必须设置选项');
        }

        // 多选题
        if ($type == Subject::TYPE_MC) {
            $count = 0;
            foreach ($params['options'] as $option) {
                if ($option['is_correct'] == 1) {
                    $count += 1;
                }
            }

            if ($count < 2) {
                throw new ServiceException('多选题必须至少两个选项');
            }
        }

        // 案例题
        if ($sectionId > 0) {
            /** @var Section $section */
            $section = Section::query()->with(['example'])->find($sectionId);

            if (!$section) {
                throw new ServiceException('选择的章节不存在');
            }

            if ($section->example) {
                $params['example_id'] = $section->example->id;
            }
        }

        return $params;
    }

    protected static function handleAttachments(Subject $subject, array $params): void
    {
        if (empty($params['upload_files'])) {
            return;
        }

        $targetType = BusinessType::Subject;
        $groupDir = 'topic';

        $uploadData = [];
        foreach ($params['upload_files'] as $file) {
            if (!$file['type_id']) {
                try {
                    $uploadData[$file['url']] = AttachmentService::store($file['filepath'], $groupDir, $targetType, $subject->id);
                } catch (Throwable) {
                    Log::error("题目{$subject->id}保存附件失败: {$file['filepath']}");
                }
            }
        }

        if (empty($uploadData)) {
            return;
        }

        foreach ($uploadData as $url => $attachment) {
            $subject->intro = str_replace($url, $attachment->url, $subject->intro);
            $subject->analysis = str_replace($url, $attachment->url, $subject->analysis);
            $subject->save();

            foreach ($subject->options as $option) {
                $option->name = str_replace($url, $attachment->url, $option->name);
                $option->save();
            }
        }
    }
}
