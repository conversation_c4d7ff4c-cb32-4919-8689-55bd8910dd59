<?php

namespace App\Services\Topic;

use App\Exceptions\ServiceException;
use App\Models\Train\Chapter;
use App\Models\Train\Section;

class ChapterService
{
    public static function create(int $topicId, string $name, int $example = 0): Chapter
    {
        $chapter = new Chapter();
        $chapter->topic_id = $topicId;
        $chapter->name = $name;
        $chapter->example = $example;
        $chapter->save();

        return $chapter;
    }

    public static function update(int $id, string $name): Chapter
    {
        /** @var Chapter $chapter */
        $chapter = Chapter::query()->find($id);

        if (!$chapter) {
            throw new ServiceException('该章不存在');
        }

        $chapter->name = $name;
        $chapter->save();

        return $chapter;
    }

    public static function remove(int $id): void
    {
        /** @var Chapter $chapter */
        $chapter = Chapter::query()->find($id);

        if (!$chapter) {
            throw new ServiceException('该章不存在');
        }

        if (Section::query()->where('chapter_id', $chapter->id)->count() > 0) {
            throw new ServiceException('请先删除该章下面的节');
        }

        $chapter->delete();
    }
}
