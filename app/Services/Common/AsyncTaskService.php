<?php

namespace App\Services\Common;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\AsyncTask;
use App\Libs\AsyncTasks\AsyncTask as Task;
use Illuminate\Support\Facades\Log;

/**
 * 异步任务服务
 */
class AsyncTaskService
{

    /**
     * 启动一个异步任务
     *
     * @param Task $task
     * @param BusinessType $businessType 业务类型
     * @param int $businessId
     * @param string $description 描述
     * @return AsyncTask
     */
    public static function start(Task $task, BusinessType $businessType, $businessId, $description='')
    {
        $taskId = $task->start();

        //生成任务
        $model = new AsyncTask();
        $model->type = $task->getTaskType();
        $model->task_id = $taskId;
        $model->business_type = $businessType;
        $model->business_id = $businessId;
        $model->task = serialize($task);
        $model->status = AsyncTask::STATUS_PROCESSING;
        $model->description = $description;
        $model->save();

        return $model;
    }

    /**
     * 查询并获取任务的最终执行结果
     *
     * @param int $taskId 异步任务本地 ID
     * @return mixed 成功返回处理结果，进行中返回 false，失败抛出异常
     * 注意返回的数据具有一定的时效性，应尽快使用，有效时长取决于各对应平台
     */
    public static function complete($taskId)
    {
        /** @var AsyncTask $model */
        $model = AsyncTask::query()
            ->where('id', $taskId)
            ->first();

        if (!$model) {
            throw new ServiceException('异步任务不存在。');
        }

        return match($model->status) {
            AsyncTask::STATUS_PROCESSING => self::checkCallback($model, fn($task) => $task->check($model)),
            AsyncTask::STATUS_FINISHED => json_decode($model->result, true),
            default => throw new ServiceException($model->description)
        };
    }

    /**
     * 调用检查闭包，并对结果进行处理和写入
     *
     * @param AsyncTask $model 任务模型
     * @param \Closure $check 任务检查闭包，接收一个 App\Libs\AsyncTasks\AsyncTask 任务实例参数，与 AsyncTask::check() 返回数据一致
     * $check 闭包除用于 complete() 方法外，还用于其它各第三方任务回调时的数据检查和处理中。
     * 各平台的主动查询（AsyncTask::check() 方法）和回调返回的任务数据可能不一致，使用 $check 闭包在回调处检查任务的回调状态。
     * @return mixed
     * @throws \Exception
     */
    public static function checkCallback(AsyncTask $model, \Closure $check)
    {
        if ($model->status != AsyncTask::STATUS_PROCESSING) {
            throw new ServiceException("任务状态不是进行中，无法继续处理。");
        }

        /** @var Task $task */
        $task = unserialize($model->task);
        try {
            if (($callbackData = $check($task)) !== false) {
                $model->callback_data = json_encode($callbackData);
                $result = $task->callback($model, $callbackData);
                $model->result = json_encode($result);
                $model->status = AsyncTask::STATUS_FINISHED;
                $model->description = '';
                $model->save();
                return $result;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            $model->status = AsyncTask::STATUS_FAILED;
            $model->description = $e->getMessage();
            $model->save();
            Log::error($e);
            throw $e;
        }
    }

}
