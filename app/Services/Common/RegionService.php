<?php

namespace App\Services\Common;

use Illuminate\Support\Facades\File;

/**
 * @property-read array $province_object 省
 * @property-read array $city_object 市
 * @property-read array $county_object 区
 * @property-read array $level 省/地/县/乡层级数据
 */
class RegionService
{

    protected $loaded = [];

    public function __get(string $name)
    {
        if (!isset($this->loaded[$name])) {
            $this->loaded[$name] = json_decode(File::get(resource_path('regions/' . $name . '.json')), true);
        }
        return $this->loaded[$name];
    }

    /**
     * 控制使用 level.json 还是另外的数据源
     */
    private const LEVEL_SOURCE = true;

    /**
     * 地区码转名称
     *
     * @param int $areaCode 地区码
     * @return array 包含省、市、区三个名称的数组
     */
    public static function code2names($areaCode)
    {
        $i = app(self::class);

        if (self::LEVEL_SOURCE) {
            $names = [];
            $arr = $i->level;
            $prefix = '';

            foreach (str_split($areaCode, 2) as $codePart) {
                $prefix .= $codePart;
                $code = sprintf('%-06s', $prefix);
                foreach ($arr as $row) {
                    if ($row['code'] == $code) {
                        $names[] = $row['name'];
                        if (!isset($row['children'])) {
                            break 2;
                        }
                        $arr = $row['children'];
                        break;
                    }
                }
            }

            return $names;
        } else {
            return [
                $i->province_object[substr($areaCode, 0, 2).'0000000000']['name'],
                $i->city_object[substr($areaCode, 0, 4).'00000000']['name'] ?? null,
                $i->county_object[substr($areaCode, 0, 6).'000000']['name'] ?? null
            ];
        }
    }

    /**
     * 地区码转文本
     *
     * @param int $areaCode 地区码
     * @param string $separator 省市区间的分隔符
     * @return string
     */
    public static function code2text($areaCode, $separator=' ')
    {
        $names = self::code2names($areaCode);
        return join($separator, array_filter($names));
    }

}
