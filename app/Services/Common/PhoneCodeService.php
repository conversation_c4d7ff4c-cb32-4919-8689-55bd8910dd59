<?php

namespace App\Services\Common;

use App\Exceptions\ServiceException;
use App\Libs\Sms\SmsTemplate;
use App\Services\SmsService;
use Illuminate\Support\Facades\Cache;

/**
 * 手机号验证码服务
 */
class PhoneCodeService
{

	/**
	 * 允许发送的验证码模板
	 * @var array
	 */
	public static $codeTemplates = [
		SmsTemplate::LOGIN,
        SmsTemplate::BIND,
        SmsTemplate::USER_DESTROY,
        SmsTemplate::RESET_PASSWORD,
        SmsTemplate::CHANGE_PHONE,
        SmsTemplate::PHONE_VERIFY,
        SmsTemplate::ENROLLMENT
	];

	/**
     * 发送手机号验证码
     *
	 * @param string $phone 手机号
	 * @param string $type 验证码类型
	 */
	public static function send($phone, $type)
	{
		if (!in_array($type, self::$codeTemplates)) {
			throw new \InvalidArgumentException("未定义的验证码类型 '$type'.");
		}

		self::checkPhoneNumber($phone);

		$code = (string)mt_rand(1000, 9999);

        try {
            SmsService::send($phone, $type , [$code], "验证码:{$code}");
        } catch (\Throwable $e) {
            throw new ServiceException($e->getMessage());
        }

		//10 分钟有效期，根据规则生成新的验证码在此处覆盖后原先的验证码将全部失效
		Cache::set("phone-code:$phone", ['code' => $code, 'type' => $type, 'created_at' => time()], 600);
	}

	/**
	 * 验证指定手机验证码是否正确
	 *
	 * 当验证码正确时该验证码会被作废
	 *
	 * @param string $phone 手机号
	 * @param string $code 验证码
	 * @param string $type 验证码类型
	 * @return bool 返回验证码是否正确
	 */
	public static function verify($phone, $code, $type)
	{
		$key = "phone-code:$phone";
		$data = Cache::get($key);

		//非正式环境允许 1111 作为通用验证码
		if ((config('app.env') == 'testing' || config('app.env') == 'local') && $code == '1111') {
			return true;
		}

		//审核账号
		if ($phone == '18019580001' && $code == '5678') {
			return true;
		}

		if ($data && $code == $data['code'] && $data['type'] == $type) {
			//验证成功后该验证码删除失效
			Cache::forget($key);
			return true;
		}

		return false;
	}

	/**
	 * 检查手机号码格式是否正确
	 *
	 * @param string $phone
	 */
	public static function checkPhoneNumber($phone)
	{
        if (!preg_match('/^1\d{10}$/', $phone)) {
            throw new \InvalidArgumentException('手机号码格式有误 '.$phone);
        }
	}

}
