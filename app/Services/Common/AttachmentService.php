<?php

namespace App\Services\Common;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Libs\Filesystem\FileUtil;
use App\Libs\Filesystem\UploadAdapterFactory;
use App\Models\Attachment\AttachmentFile;
use App\Models\Attachment\AttachmentRelation;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Qiniu\Etag;

/**
 * 附件服务
 */
class AttachmentService
{

    /**
     * 临时目录
     * 指定目标存储的哪个目录作为临时目录
     */
    public const TMP_DIR = 'tmp/';

    /**
     * 获取用于直传的表单信息
     *
     * @param string $prefix 文件 key 前缀，建议使用用户的 uuid，后台使用管理员 ID
     * @param string|array $fileTypes 允许上传的文件类型，支持 image, doc, video, archive, *
     * @param int $maxSizeKB 允许上传的文件大小，单位为 KB，例如 1024=1MB，0 代表不限制
     * @param string|null $diskName 选中的存储，不指定则使用 filesystems.php 中配置的默认存储
     * @return array{method: string, url: string, name: string, form_params: array, allow_exts: string, allow_mime_types: string, max_size_kb: int} 上传表单信息
     */
    public static function getUploadForm($prefix, $fileTypes='*', $maxSizeKB = 0, $diskName=null)
    {
        $allowExts = [];
        $allowMimeTypes = [];

        if ($fileTypes !== '*') {
            if (!is_array($fileTypes)) {
                $fileTypes = explode('|', $fileTypes);
            }

            foreach ($fileTypes as $type) {
                [$exts, $mimes] = match($type) {
                    'video' => [
                        ['mp4', 'mov', 'avi', 'mkv', 'rmvb', 'wmv'],
                        ['video/*']
                    ],
                    'image' => [
                        ['jpg', 'jpeg', 'gif', 'png'],
                        ['image/jpeg', 'image/gif', 'image/png']
                    ],
                    'doc' => [
                        ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'],
                        [
                            'application/rtf',
                            'application/msword',
                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            'application/vnd.ms-excel',
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            'application/vnd.ms-powerpoint',
                            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                            'application/pdf'
                        ]
                    ],
                    'archive' => [
                        ['zip', 'rar', '7z'],
                        [
                            'application/zip',
                            'application/vnd.rar',
                            'application/x-7z-compressed'
                        ]
                    ],
                    default => throw new \InvalidArgumentException("Unsupported file type '$type'.")
                };
                array_push($allowExts, ...$exts);
                array_push($allowMimeTypes, ...$mimes);
            }
        }

        $uploadAdapter = UploadAdapterFactory::create($diskName);
        $config = $uploadAdapter->form($prefix, $allowMimeTypes, $maxSizeKB, $diskName);

        return array_merge($config, [
            'allow_exts' => join(',', $allowExts),
            'allow_mime_types' => join(',', $allowMimeTypes),
            'max_size_kb' => $maxSizeKB
        ]);
    }

    /**
     * 存储上传后的临时文件
     *
	 * 保存临时文件到正式附件目录并确立关联，相同的文件将自动排重
	 * 文件将被移动到 StorageDir/GroupDir/YearMonth/DayHour/Filename
     * 只能该临时文件存储到相同的 Disk
	 *
     * @param string $key 上传后返回的临时文件 key
	 * @param string $groupDir 在正式目录下的分组目录
	 * @param BusinessType $targetType 文件归属关系目标业务类型
	 * @param array|int $targetIds 文件归属关系目标业务 ID 列表
	 * @param string|null $filename 文件名，不指定则使用自动文件名
	 * @param int|null $filesize 文件大小，不指定则自动计算
	 * @param string|null $mime 文件 MIME 信息，不指定则自动检测
	 * @return AttachmentFile 返回保存后的文件
     */
    public static function store($key, $groupDir, BusinessType $targetType, $targetIds, $filename=null, $filesize=null, $mime=null)
    {
        if (!str_contains($key, ':')) {
            throw new ServiceException('保存附件失败，临时 key 错误。');
        }

        [$diskName, $tmp] = explode(':', $key, 2);
        !$diskName && $diskName = config('filesystems.default');

        $uploadAdapter = UploadAdapterFactory::create($diskName);
        $info = $uploadAdapter->info($tmp);
        $disk = $uploadAdapter->getDisk();

        //尝试从上传后的缓存中拿到文件原始名称
        if (!$filename && ($cachedFileInfo = Cache::get('uploaded-file:'.$key)) !== null) {
            $filename = $cachedFileInfo['filename'];
        }

        if ($filesize === null) {
            $filesize = $info['filesize'] ?? $disk->fileSize($tmp);
        }

        if ($mime === null || strtolower($mime) == 'application/octet-stream') {
            $mime = $info['mime_type'] ?? $disk->mimeType($tmp);
        }

        //音视频的宽高信息
        //Todo: 视频的宽高、时长信息还不支持 || str_starts_with($mime, 'video/')
        if (str_starts_with($mime, 'image/')) {
            $width = $info['width'];
            $height = $info['height'];
        } else {
            $width = $height = 0;
        }

		$file = AttachmentFile::query()
			->where('etag', $info['hash'])
            ->where('disk', $diskName)
			->first();

		if (!$file) {
			$filename === null && $filename = basename($tmp);

            $path = $groupDir.'/'.date('Ym/dH/').$info['hash'].'.'.pathinfo($tmp, PATHINFO_EXTENSION);

            try {
                $result = $disk->move($tmp, $path);
                Log::info("保存附件 $key 到 $path 结果 $result");
            } catch (\Throwable $e) {
                throw new ServiceException($e->getMessage(), $e->getCode());
            }

			$file = new AttachmentFile();
            $file->disk = $diskName;
			$file->path = $path;
			$file->filename = $filename;
			$file->filesize = $filesize;
			$file->mime = $mime;
			$file->etag = $info['hash'];
			$file->width = $width;
			$file->height = $height;
			$file->save();
		}

        AttachmentRelation::saveRelations($file, $targetType, $targetIds);

		return $file;
    }

    /**
     * 存储本地文件
     *
     * @param string $localPath
     * @param string $diskName 要上传至的目标存储名称
     * @param string $groupDir
     * @param BusinessType $targetType
     * @param int|array $targetIds
     * @param string $filename
     * @param string $mime
     * @return AttachmentFile
     */
    public static function storeFromLocal($localPath, $diskName, $groupDir, BusinessType $targetType, $targetIds, $filename=null, $mime=null)
    {
        if (!is_file($localPath)) {
            throw new ServiceException("文件 $localPath 不存在。");
        }

        !$diskName && $diskName = config('filesystems.default');
        $filesize = filesize($localPath);

        if ($mime === null || strtolower($mime) == 'application/octet-stream') {
            $mime = FileUtil::detectMimeType($localPath);
        }

        //音视频的宽高信息
        //Todo: 视频的宽高、时长信息还不支持 || str_starts_with($mime, 'video/')
        if (str_starts_with($mime, 'image/')) {
            [$width, $height] = getimagesize($localPath);
        } else {
            $width = $height = 0;
        }

        [$hash, $err] = Etag::sum($localPath);

        if ($err) {
            Log::channel('task')->error('localPath:' . $localPath . ', err:' . json_encode($err));
            throw new ServiceException("计算文件 Hash 失败：{$err['message']}");
        }

        $file = AttachmentFile::query()
            ->where('etag', $hash)
            ->where('disk', $diskName)
            ->first();

        if (!$file) {
            $uploadAdapter = UploadAdapterFactory::create($diskName);
            $disk = $uploadAdapter->getDisk();

            $filename === null && $filename = basename($localPath);
            $path = $groupDir.'/'.date('Ym/dH/').$hash.'.'.pathinfo($localPath, PATHINFO_EXTENSION);

            $fp = fopen($localPath, 'r');
            try {
                if (!$disk->writeStream($path, $fp)) {
                    throw new ServiceException("存储到 $diskName:$path 失败。");
                }
            } finally {
                fclose($fp);
            }

            $file = new AttachmentFile();
            $file->disk = $diskName;
            $file->path = $path;
            $file->filename = $filename;
            $file->filesize = $filesize;
            $file->mime = $mime;
            $file->etag = $hash;
            $file->width = $width;
            $file->height = $height;
            $file->save();
        }

        AttachmentRelation::saveRelations($file, $targetType, $targetIds);

        return $file;
    }

	/**
	 * 删除附件关联，移除不再被引用的附件文件
	 *
	 * @param BusinessType $targetType
	 * @param array|int $targetIds
     * @param string|array|null $paths 只删除关联中指定的文件，不指定此参数情况下，删除业务关联的所有文件，指定 $paths 则只删除对应路径的关联文件
	 * @return int
	 */
	public static function removeRelations(BusinessType $targetType, $targetIds, $paths=null)
	{
		$relations = AttachmentRelation::fetchByRelations($targetType, $targetIds);

        $files = [];
        $paths !== null && !is_array($paths) && $paths = [$paths];

		foreach ($relations as $relation) {
            if (!$paths || in_array($relation?->file?->path, $paths)) {
                $files[$relation->file_id] = $relation->file;
                $relation->delete();
            }
		}

		$deleteCount = 0;

		foreach ($files as $file) {
			if (AttachmentRelation::countByFileId($file->id) > 0) {
				continue;
			}

            $file->delete();
            ++$deleteCount;
		}

		return $deleteCount;
	}

    /**
     * 获取指定文件系统中文件的访问地址
     *
     * 配置了 'visibility' => 'private' 且支持临时 URL 的适配器将返回鉴权的临时 URL，否则使用固定的公开 URL
     *
     * @param FilesystemAdapter|string $diskOrKey 指定的存储空间实例，或存储空间名，或带有 prefix 的 Key
     * @param string|null $key 当 $diskOrKey 没有指定文件 Key 时，需要指定此 Key 作为文件的路径
     * @param string $filename 下载时的文件名，只在特定的驱动中（如七牛、OSS等）有效
     * @return string
     */
    public static function url(FilesystemAdapter|string $diskOrKey, $key=null, $filename=null)
    {
        if ($diskOrKey instanceof FilesystemAdapter) {
            $disk = $diskOrKey;
        } else {
            if (str_contains($diskOrKey, ':')) {
                [$diskName, $key] = explode(':', $diskOrKey, 2);
            } else {
                $diskName = $diskOrKey;
            }
            $disk = Storage::disk($diskName);
        }

        $config = $disk->getConfig();

        //目前没办法介入到 FilesystemAdapter 的临时 URL 生成逻辑中，所以只能通用的认为都是 S3 文件系统的 attname 或 response-content-disposition 参数来控制
        //具体使用哪个参数取决于配置中是否有 attname=true，有则使用 attname，否则使用 response-content-disposition
        //response-content-disposition 因为里面的文件名在参数中经过了二次编码，会导致超出长度限制的问题，所以在支持 attname 的情况下尽可能使用 attname
        //目前已知七牛云存储、华为 OBS 支持 attname 参数
        if ($filename) {
            if (isset($config['attname']) && $config['attname']) {
                $key .= '?attname='.urlencode($filename);
            } else {
                //严格来说使用 filename*= 更加可靠，但参数值本身就是编码，filename* 后的值还要再次编码，会导致 response-content-disposition 的值过长
                //这会导致某些平台报过长错误，作为妥协，只能使用非编码的 filename 参数
                //详见 https://developer.mozilla.org/zh-CN/docs/web/http/headers/content-disposition
                // $key .= '?response-content-disposition='.urlencode('attachment; filename*=utf-8\'\''.urlencode($filename));
                $key .= '?response-content-disposition='.urlencode('attachment; filename='.$filename);
            }
        }

        if (isset($config['visibility']) && $config['visibility'] == Filesystem::VISIBILITY_PRIVATE && $disk->providesTemporaryUrls()) {
            return $disk->temporaryUrl($key, 3600);
        } else {
            return $disk->url($key);
        }
    }

    /**
     * 下载远程文件为指定存储的临时文件
     *
     * @param string $url
     * @param int $timeout 下载超时时间
     * @param array $headers 发送请求时的 Headers，键值对应 Header 的名称和值
     * @param bool $upload 如果指定的存储不在本地，是否上传到该存储临时区，这取决于是仅在本地处理该文件还是直接存储到外部存储
     * @param string|null $diskName 要下载到的存储，不指定则使用默认存储
     * @return array{url:string, key:string, filename:string, size:int, mime:string, local_path: string} 返回下载的文件信息 \
     * - url: 文件预览地址
     * - key: 文件临时键，用于 store()
     * - filename: 原始文件名称
     * - size: 文件大小
     * - mime: 文件 MIME 类型
     * - local_path: 临时文件在本地的完整路径
     */
    public static function download($url, $timeout=3600, $headers=[], $upload=false, $diskName=null)
    {
        list(, $tmpRealpath) = self::tmpFile('downloading', 'wind-');

        //模拟浏览器
        $ua = $headers['User-Agent'] ?? $headers['user-agent'] ?? null;
        $headers = array_merge($headers, self::getDownloadHeaders($ua));

        if (($fp = fopen($tmpRealpath, 'w')) === false) {
            throw new \RuntimeException('创建临时文件 '.$tmpRealpath.' 失败。');
        }

        //下载到本地
        try {
            Log::info("Download $url -> $tmpRealpath");

            $response = Http::withOptions([
                'sink' => $fp,
                'read_timeout' => $timeout,
                'headers' => $headers
            ])->throw()->get($url);

            fclose($fp);

        } catch (\Throwable $e) {
            Log::error("下载文件 $url 失败：{$e->getMessage()}.");
            throw new \Exception('下载文件失败：'.$e->getMessage());
        } finally {
            is_resource($fp) && fclose($fp);
        }

        //猜测原始文件名
        $mime = $response->header('Content-Type');
        $filesize = filesize($tmpRealpath);
        $contentDisposition = $response->header('Content-Disposition');

        //将 downloading 后缀改为正式后缀
        $fileInfo = FileUtil::detectFile($url, mime: $mime, contentDisposition: $contentDisposition);
        $pathInfo = pathinfo($tmpRealpath);
        $basename = $pathInfo['filename'].'.'.$fileInfo['extension'];
        $localPath = $pathInfo['dirname'].'/'.$basename;

        if (!rename($tmpRealpath, $localPath)) {
            $error = error_get_last();
            throw new \RuntimeException($error['message']);
        }

        $disk = Storage::disk($diskName);
        $tmp = self::TMP_DIR.$basename;

        $localDisk = config('heguibao.storage.local');

        //上传
        if (!$upload || Storage::disk($localDisk) === $disk) {
            $key = $localDisk.':'.$tmp;
        } else {
            //目标文件系统如果非临时文件的系统，则写入目标文件系统的临时区
            $fp = fopen($localPath, 'r');
            try {
                if (!$disk->writeStream($tmp, $fp)) {
                    throw new ServiceException('存储到临时文件失败。');
                }
            } finally {
                fclose($fp);
            }
            $key = ($diskName ?? '').':'.$tmp;
        }

        return [
            'url' => self::url($disk, $tmp),
            'key' => $key,
            'filename' => $fileInfo['basename'],
            'size' => $filesize,
            'mime' => $fileInfo['mime'],

            //注意 local_path 不要暴露给前端
            'local_path' => $localPath
        ];
    }

    /**
     * 将本地文件上传为指定存储的临时文件
     *
     * @param string $localPath 完整的本地文件路径
     * @param string $diskName 要上传至的目标存储名称，不指定则使用默认存储
     * @param string|null $filename 文件名，不指定则使用自动文件名
     * @return array{url:string, key:string, filename:string, size:int, mime:string, local_path: string} 返回下载的文件信息 \
     * - url: 文件预览地址
     * - key: 文件临时键，用于 store()
     * - filename: 原始文件名称
     * - size: 文件大小
     * - mime: 文件 MIME 类型
     */
    public static function upload($localPath, $diskName=null, $filename=null)
    {
        if ($filename) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
        } else {
            $extension = pathinfo($localPath, PATHINFO_EXTENSION);
        }

        [$basename] = self::tmpFile($extension);

        $disk = Storage::disk($diskName);
        $tmp = self::TMP_DIR.$basename;

        //上传
        $fp = fopen($localPath, 'r');
        try {
            if (!$disk->writeStream($tmp, $fp)) {
                throw new ServiceException('存储到临时文件失败。');
            }
        } finally {
            fclose($fp);
        }

        return [
            'url' => self::url($disk, $tmp),
            'key' => ($diskName ?? '').':'.$tmp,
            'filename' => $filename ?: $basename,
            'size' => filesize($localPath),
            'mime' => FileUtil::detectMimeType($localPath),
        ];
    }

    /**
     * 模拟下载时的通用头部
     *
     * @param string $userAgent 已经指定的 UA，不指定则随机生成一个
     * @return array
     */
    protected static function getDownloadHeaders($userAgent = null)
    {
        $userAgents = [
            //Windows Chrome
            'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.122 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 Edg/87.0.664.57',
            //QQ Browser
            'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.25 Safari/537.36 Core/1.70.3880.400 QQBrowser/10.8.4554.400',
            //Windows Firefox
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:92.0) Gecko/20100101 Firefox/92.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/20100101 Firefox/95.0',
            //MacOS Safari
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Safari/605.1.15',
            //iOS Safari
            'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Mobile/15E148 Safari/604.1'
        ];

        $userAgent === null && $userAgent = $userAgents[array_rand($userAgents)];

        if (str_contains($userAgent, 'Chrome')) {
            $accept = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9';
        } elseif (str_contains($userAgent, 'Firefox')) {
            $accept = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8';
        } elseif (str_contains($userAgent, 'Safari')) {
            $accept = 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8';
        } else {
            $accept = '*/*';
        }

        return [
            'Accept' => $accept,
            'Accept-Encoding' => 'gzip, deflate',
            'Accept-Language' => 'zh-CN,zh;q=0.9',
            'User-Agent' => $userAgent,
            'Connection' => 'keep-alive'
        ];
    }

    /**
     * 获得一个新的本地临时文件路径用于创建或写入
     *
     * 此时临时文件还没有生成，临时文件目录位于共享的专用临时文件目录中。
     *
     * @param string $ext 后缀，如 "jpg", "mp4"
     * @param string $prefix 文件名前缀
     * @return string[] 返回两个元素：[文件名, 绝对路径]
     */
    public static function tmpFile($ext, $prefix='')
    {
        $filename = $prefix.strtolower(app('nanoid')->generateId(16)).'.'.$ext;
        $realPath = self::tmpPath($filename);

        return [$filename, $realPath];
    }

    /**
     * 拼接临时文件路径或获取临时文件的目录（本地存储）
     *
     * @param string $filename 拼接的文件名，不指定则只返回临时文件的目录
     * @return string 临时文件绝对路径
     */
    public static function tmpPath($filename=null)
    {
        return Storage::disk(config('heguibao.storage.local'))->path(self::TMP_DIR.$filename);
    }

    /**
     * 获取临时文件的绝对路径（本地存储）
     *
     * @param string $tmpPath 临时文件的相对或绝对路径
     * @return string
     */
    public static function realTmpPath($tmpPath)
    {
        $tmpDir = self::tmpPath();
        //不是临时目录开头的认为是相对路径
        return strncmp($tmpPath, $tmpDir, strlen($tmpDir)) !== 0 ? $tmpDir.DIRECTORY_SEPARATOR.$tmpPath : $tmpPath;
    }

}
