<?php

namespace App\Services\Common;

use App\Core\Enums\BusinessType;
use App\Core\OrderableInterface;
use App\Exceptions\ServiceException;
use App\Models\Cms\Content;
use App\Models\Order\Order;
use App\Models\Train\Topic;
use App\Models\User\UserCreditLog;
use Illuminate\Database\Eloquent\Model;

/**
 * 订单服务
 */
class OrderService
{

    /**
     * 创建订单
     *
     * @param int $userId 用户ID
     * @param Model&OrderableInterface $business 业务模型对象，该模型必须实现了 OrderableInterface 才能下单
     * @param int|float $amount 订单金额
     * @param array|null $extend 订单的扩展信息，传递给 $business 类作特殊用途
     * @param BusinessType|null $businessType 指定 BusinessType，不指定则自动检测（如果 $business 类有多个多态别名，则必须指定，否则可能匹配不到或者匹配到错误的 BusinessType）
     * @return Order
     */
    public static function create($userId, Model & OrderableInterface $business, $amount, $extend=null, ?BusinessType $businessType=null): Order
    {
        //获取订单标题
        $title = $business->orderName();

        if (!$businessType) {
            if ($business instanceof Content) {
                //CMS 的内容有子类型，需要特别获取
                $businessType = $business->getBusinessType();
            } else {
                $businessType = BusinessType::from($business->getMorphClass());
            }
        }

        if ($amount <= 0) {
            throw new ServiceException("订单金额异常");
        }

        bcscale(2);

        $order = new Order();
        $order->user_id = $userId;
        $order->order_no = self::generateUniqueOrderNo($userId);
        $order->total_amount = $amount;
        $order->title = $title;
        $order->business_type = $businessType;
        $order->business_id = $business->getKey() ?: 0;
        $order->extend = $extend;
        $order->save();

        return $order;
    }

    /**
     * 生成唯一 21 位长的编号
     *
     * 编号由 年2位月日时分秒(12) + 用户ID后5位补零(5) + 随机数(4)
     *
     * @param int $userId
     * @return string
     */
    public static function generateUniqueOrderNo($userId)
    {
        while (true){
            $userId > 99999 && $userId = substr($userId, -5);
            $orderNo = sprintf('%s%05d%04d', date('ymdHis'), $userId, mt_rand(0, 9999));
            if (!Order::query()->where('order_no', $orderNo)->exists()) {
                return $orderNo;
            }
        }
    }

}
