<?php

namespace App\Services\Chat;

use App\Services\Chat\AI\BaiduAIService;

class ChatSessionService
{
    public function setRecommend(int $number = 25): array
    {
        $index = $this->getRecommendCacheIndex();
        $questions = [];

        $result = app(BaiduAIService::class)->chat("推荐{$number}个常见企业安全生产问题，如下JSON格式输出: [\"\",\"\"]");

        if (!isset($result['result'])) {
            return $questions;
        }

        $jsonString = preg_replace('/\s+/', '', $result['result']);
        preg_match("/(\[.+])/", $jsonString, $matches);

        if(!isset($matches[1])) {
            return $questions;
        }

        $questions = json_decode($matches[1], true);

        foreach ($questions as $key => $value) {
            $questions[$key] = preg_replace('/\d+\./', '', $value);
        }

        cache()->set($index, $questions, 86400 * 8);

        return $questions;
    }

    public function getRecommendCacheIndex(): string
    {
        return "chat:session:recommend";
    }

    public function getRecommendDefault(): array
    {
        return [
            "如何有效预防生产设备的故障导致的安全事故？",
            "如何提高员工的安全意识和应急处理能力？",
            "工作场所消防通道被堵塞会有什么后果？",
            "生产线上的安全设备是否定期检查和维护？",
            "员工是否了解并掌握基本的安全生产知识和技能？",
            "危险化学品存储不当会有哪些风险？",
            "电气设备老化、裸露会引发什么问题？",
            "高空作业没有采取防护措施会有多危险？",
            "叉车等运输工具在车间内超速行驶有什么风险？",
            "压力容器操作不当会引发哪些事故？",
            "特种作业人员无证上岗会有什么后果？",
            "没有制定应急预案或预案不完备会怎样？",
            "工作场所通风不良对员工健康有什么影响？",
            "易燃易爆物品与普通物品混放会有什么危险？",
            "电线私拉乱接会引发什么安全问题？",
            "有毒有害物质泄露应急处理不当会怎样？",
            "对于危险作业，企业是否进行了风险评估和审批？",
            "消防器材是否要定期检查并保持在可用状态？"
        ];
    }
}
