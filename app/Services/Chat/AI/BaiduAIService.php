<?php

namespace App\Services\Chat\AI;

class BaiduAIService
{
    public function chat(string $prompt, array $messages = [], $callable = null)
    {
        $curl = curl_init();

        $messages[] = [
            "role" => "user",
            "content" => $prompt,
        ];

        $postData = [
            "messages" => $messages,
            "temperature" => 0.95,
            "top_p" => 0.8,
            "penalty_score" => 1,
            "system" => "你一名管理企业安全生产的工程师，解答相关领域的专业知识，返回内容要简单易懂。",
            "prompt" => $prompt,
            "stream" => (bool)$callable
        ];

        $options = array(
            CURLOPT_URL => "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-turbo-8k?access_token={$this->getAccessToken()}",
            CURLOPT_TIMEOUT => 60,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER  => false,
            CURLOPT_SSL_VERIFYHOST  => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postData, JSON_UNESCAPED_UNICODE),

            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            )
        );

        if ($callable) {
            $options[CURLOPT_WRITEFUNCTION] = function ($curl, $raw) use ($callable, $prompt) {
                $length = strlen($raw);

                if (str_contains($raw, "error_code")) {
                    return $callable(null, 0);
                }

                $data = explode("\n\n", $raw);

                $sessions = [];

                foreach ($data as $item) {
                    if ($item) {
                        $item = json_decode(substr($item, 6), true);

                        $sessions[] = $item;
                    }
                }

                return $callable($sessions, $length);
            };
        }

        curl_setopt_array($curl, $options);

        $response = curl_exec($curl);

        curl_close($curl);

        return json_decode($response, true);
    }


    /**
     * 使用 AK，SK 生成鉴权签名（Access Token）
     * @return string 鉴权签名信息（Access Token）
     */
    private function getAccessToken(): string
    {
        $config = config('services.chat.ERNIE_Bot_4');

        $curl = curl_init();
        $postData = array(
            'grant_type' => 'client_credentials',
            'client_id' => $config['api_key'],
            'client_secret' => $config['api_secret']
        );
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://aip.baidubce.com/oauth/2.0/token',
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_SSL_VERIFYPEER  => false,
            CURLOPT_SSL_VERIFYHOST  => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POSTFIELDS => http_build_query($postData)
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $rtn = json_decode($response);
        return $rtn->access_token;
    }
}
