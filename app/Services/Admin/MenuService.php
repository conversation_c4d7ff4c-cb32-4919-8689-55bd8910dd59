<?php

namespace App\Services\Admin;

use App\Exceptions\ServiceException;
use App\Models\Admin\Menu;
use App\Models\Admin\RoleMenu;
class MenuService
{
    public static function getMenus(?int $status = null): array
    {
        $query = Menu::query();

        if ($status !== null) {
            $query->where('status', $status);
        }

        return $query->orderByRaw('sort asc, id asc')->get()->toArray();
    }

    public static function create(array $params): Menu
    {
        $menu = new Menu();

        foreach ($params as $key => $val) {
            if (in_array($key, $menu->getFillable())) {
                $menu->{$key} = $val;
            }
        }

        $menu->save();

        PermissionService::getAllMenus(Menu::TYPE_API,true);

        return $menu;
    }

    public static function update(int $id, array $params): Menu
    {
        /** @var Menu $menu */
        $menu = Menu::query()->find($id);

        if (!$menu) {
            throw new ServiceException('菜单不存在');
        }

        if (isset($params['parent_id']) && $menu->id == $params['parent_id']) {
            throw new ServiceException('父类不能选择自己');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $menu->getFillable())) {
                $menu->{$key} = $val;
            }
        }

        $menu->save();

        PermissionService::getAllMenus(Menu::TYPE_API,true);

        return $menu;
    }

    public static function remove(int $id): void
    {
        /** @var Menu $menu */
        $menu = Menu::query()->find($id);

        if (!$menu) {
            throw new ServiceException('菜单不存在');
        }

        if (Menu::query()->where('parent_id', $id)->exists()) {
            throw new ServiceException('请先删除子菜单');
        }

        $menu->delete();

        RoleMenu::query()->where('menu_id', $menu->id)->delete();

        PermissionService::getAllMenus(Menu::TYPE_API,true);
    }
}
