<?php

namespace App\Services\Admin;

use App\Exceptions\ServiceException;
use App\Models\Cms\Content;
use App\Models\Cms\ContentDoc;
use App\Models\Cms\ContentRichText;
use Illuminate\Support\Facades\DB;

/**
 * 爬虫服务
 */
class CrawlerService
{

    /**
     * 生成内容草稿
     *
     * @param int $categoryId
     * @param string $title
     * @param ContentDoc|ContentRichText $extendModel
     * @param string $intro
     * @param string $source
     * @return Content
     */
    public static function createDraft($categoryId, $title, ContentDoc|ContentRichText $extendModel, $intro='', $source='')
    {
        $type = array_search($extendModel::class, Content::$resource);

        if ($type === false) {
            throw new ServiceException("不支持 ".$extendModel::class." 作为扩展模型录入。");
        }

        DB::beginTransaction();

        try {
            $content = new Content();
            $content->type = $type;
            $content->category_id = $categoryId;
            $content->title = $title;
            $content->status = Content::STATUS_DRAFT;
            $content->intro = $intro;
            $content->source = $source;
            $content->save();

            $extendModel->content_id = $content->id;
            $extendModel->save();

            DB::commit();

            return $content;

        } catch (\Throwable $e) {
            DB::rollBack();
            throw new ServiceException($e->getMessage());
        }
    }

}
