<?php

namespace App\Services\Admin;

use App\Exceptions\ServiceException;
use App\Models\Admin\Admin;
use App\Models\Admin\Menu;
use App\Models\Admin\RoleMenu;
use App\Models\Admin\Role;
use App\Models\Admin\UserRole;
use Carbon\Carbon;

class RoleService
{
    public static function create(array $params): Role
    {
        if (Role::query()->where('code', $params['code'])->exists()) {
            throw new ServiceException('编码已存在');
        }

        $role = new Role();
        foreach ($params as $key => $val) {
            if (in_array($key, $role->getFillable())) {
                $role->{$key} = $val;
            }
        }
        $role->save();

        self::handleMenus($role, $params, false);

        return $role;
    }

    public static function update(int $id, array $params): Role
    {
        /** @var Role $role */
        $role = Role::query()->find($id);

        if (!$role) {
            throw new ServiceException('权限不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $role->getFillable())) {
                $role->{$key} = $val;
            }
        }

        $role->save();

        self::handleMenus($role, $params);

        return $role;
    }

    public static function remove(int $id): void
    {
        /** @var Role $role */
        $role = Role::query()->find($id);

        if (!$role) {
            throw new ServiceException('权限不存在');
        }

        if (UserRole::query()->where('role_id', $role->id)->exists()) {
            throw new ServiceException('请先移除关联的用户');
        }

        $role->delete();

        RoleMenu::query()->where('role_id', $role->id)->delete();
    }

    public static function checkPermission(int $adminId, string $permission): bool
    {
        $roleIds = Role::query()->where('permissions', 'like', "%$permission%")->get()->pluck('id')->toArray();

        if (empty($roleIds)) {
            return false;
        }

        return UserRole::query()->where('admin_id', $adminId)->whereIn('role_id', $roleIds)->exists();
    }

    protected static function handleMenus(Role $role, array $params, bool $isUpdate = true): void
    {
        $indeterminateIds = $params['indeterminate_ids'] ?? [];
        $checkedIds = $params['checked_ids'] ?? [];

        if ($isUpdate) {
            RoleMenu::query()->where('role_id', $role->id)->delete();
        }

        $insert = [];
        $now = Carbon::now();
        foreach ($indeterminateIds as $id) {
            $insert[] = [
                'role_id' => $role->id,
                'menu_id' => $id,
                'checked' => 0,
                'created_at' => $now
            ];
        }

        foreach ($checkedIds as $id) {
            $insert[] = [
                'role_id' => $role->id,
                'menu_id' => $id,
                'checked' => 1,
                'created_at' => $now
            ];
        }

        RoleMenu::query()->insert($insert);

        Admin::query()->chunkById(100, function ($admins) {
            foreach ($admins as $admin) {
                PermissionService::getUserMenus($admin->id, Menu::TYPE_MENU, true);
            }
        });
    }
}
