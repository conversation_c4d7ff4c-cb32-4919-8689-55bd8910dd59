<?php

namespace App\Services\Admin;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Services\Common\AttachmentService;

class AttachmentRelationService
{
    const UPLOAD_KEY = ':tmp';

    /**
     * 保存附件关联
     *
     * @param mixed $model 对应模型
     * @param array $params 请求参数
     * @param string $groupDir
     * @param BusinessType $targetType
     * @param bool $isCreate
     * @param string $modelKey 模型对应的key
     * @param string $paramKey 请求参数对应的key
     * @return void
     * @throws ServiceException
     */
    public static function saveAttachment(mixed $model, array $params, string $groupDir, BusinessType $targetType, bool $isCreate = true, string $modelKey = 'cover', string $paramKey = 'cover'): void
    {
        if (!isset($params[$paramKey])) {
            return;
        }

        $path = $params[$paramKey];

        if ($isCreate) {
            // 创建模型时，附件处理
            if ($path) {
                $attachment = AttachmentService::store($path, $groupDir, $targetType, $model->id);

                $model->{$modelKey} = $attachment->path;
                $model->save();
            }
        } else {
            // 更新模型时，附件处理
            if ($path) {
                // 上传了新附件，如果有旧附件，则删除旧附件，更新新附件
                if ($model->{$modelKey}) {
                    if (str_contains($path, self::UPLOAD_KEY)) {
                        AttachmentService::removeRelations($targetType, $model->id, $model->{$modelKey});

                        $attachment = AttachmentService::store($path, $groupDir, $targetType, $model->id);

                        $model->{$modelKey} = $attachment->path;
                        $model->save();
                    }
                } else {
                    $attachment = AttachmentService::store($path, $groupDir, $targetType, $model->id);

                    $model->{$modelKey} = $attachment->path;
                    $model->save();
                }
            } else {
                // 未上传附件，历史附件删除
                if ($model->{$modelKey}) {
                    $model->{$modelKey} = '';
                    $model->save();
                }
            }
        }
    }

    public static function removeAttachment(BusinessType $targetType, $targetIds, $paths = null): void
    {
        AttachmentService::removeRelations($targetType, $targetIds, $paths);
    }
}
