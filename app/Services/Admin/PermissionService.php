<?php

namespace App\Services\Admin;

use App\Models\Admin\Admin;
use App\Models\Admin\Menu;
use App\Models\Admin\RoleMenu;
use App\Models\Admin\UserRole;

class PermissionService
{
    public static function getUserRoles(int $adminId): array
    {
        return UserRole::query()->where('admin_id', $adminId)->get()->pluck('role_id')->toArray();
    }

    public static function getUserMenus(int $adminId, int $type = Menu::TYPE_MENU, bool $force = false): array
    {
        $cacheIndex = "user:$adminId:menus:$type";

        $menus = cache()->get($cacheIndex);

        if ($force || !$menus) {
            $roleIds = self::getUserRoles($adminId);

            $menuIds = RoleMenu::query()->whereIn('role_id', $roleIds)->get()->pluck('menu_id')->toArray();

            $menus = Menu::query()
                ->select(['id', 'name', 'icon', 'route_name', 'route_path', 'method', 'component', 'sort'])
                ->whereIn('id', $menuIds)
                ->where('type', $type)
                ->get()->toArray();

            cache()->set($cacheIndex, $menus, 3600);
        }

        return $menus;
    }

    public static function getAllMenus(int $type = Menu::TYPE_API, bool $force = false)
    {
        $cacheIndex = "all:menus:$type";

        $routePaths = cache()->get($cacheIndex);

        if ($force || !$routePaths) {
            $routePaths = Menu::query()->select(['route_path', 'method'])->where('type', $type)->get()->toArray();

            cache()->set($cacheIndex, $routePaths, 3600);
        }

        return $routePaths;
    }

    public static function checkApiPermission(Admin $admin, string $routePath, string $method): bool
    {
        $method = strtoupper($method);
        $routePath = substr($routePath, 5);

        // 超管，不做权限判断
        if ($admin->is_admin) {
            return true;
        }

        $allRoutes = self::getAllMenus();

        // 没配置路由, 不做权限判断
        if (empty($allRoutes)) {
            return true;
        }

        // 该路由不在所有路由中，不做权限判断
        if (!self::isCheck($allRoutes, $method, $routePath)) {
            return true;
        }

        // 角色权限判断
        $userRoutes = self::getUserMenus($admin->id, Menu::TYPE_API);

        if (empty($userRoutes)) {
            return false;
        }

        if (self::isCheck($userRoutes, $method, $routePath)) {
            return true;
        }

        return false;
    }

    protected static function isCheck(array $routes, string $method, string $value): bool
    {
        return !collect($routes)->where('route_path', $value)->where('method', $method)->isEmpty();
    }
}
