<?php

namespace App\Services\Inspect;

use App\Exceptions\ServiceException;
use App\Models\Inspect\Task;
use App\Models\Inspect\TaskDevicesRecord;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

/**
 * 巡检任务服务
 */
class InspectTaskService
{
    /**
     * 创建巡检任务
     * @param string $name 任务名称
     * @param int $frequency 巡检频率
     * @param array $devices 设备ID
     * @return Task
     */
    public function  create(string $name, int $frequency, array $devices): Task
    {
        try {
            $userID = Auth::id() ?? 0;
            if (!$userID) {
                throw new ServiceException("用户不存在");
            }
            $task = new Task();
            $task->fill([
                'name' => $name,
                'frequency' => $frequency,
                'user_id' => $userID,
                'device_count' => count($devices),
            ]);
            $task->save();
            // 添加任务设备关联
            $task->devices()->attach($devices);
            return $task;
        } catch (ServiceException $e) {
            logger()->error("创建巡检任务失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("创建巡检任务失败");
        }
    }

    /**
     * 获取用户巡检任务列表
     * @param int $userID 用户ID
     * @param int|null $limit
     * @return array
     */
    public function getOwnTaskListByUserID(int $userID, ?int $limit = null): array
    {
        try {
            /**
             * 列表数据:
             * 任务id
             * 巡检名称
             * 设备数量
             * 待审批数量
             * 是否存在巡检设备记录表（不存在调用生成设备记录表api,仅任务创建后还未进行过巡检）
             */
            $query = Task::query()
                ->where('user_id', $userID)
                ->with(['devices', 'taskDevicesRecords']) // 预加载关联数据，避免N+1查询
                ->orderBy('id', 'desc');

            if ($limit !== null) {
                $query->take($limit);
            }

            return $query->get()
                ->map(function ($task) {
                    // 直接从预加载的关系中获取数据
                    $pendingApprovalCount = $task->taskDevicesRecords
                        ->where('status', TaskDevicesRecord::PENDING_APPROVAL)
                        ->count();

                    return [
                        'id' => $task->id,
                        'name' => $task->name,
                        'device_count' => $task->devices->count(),
                        'pending_approval_count' => $pendingApprovalCount,
                        'exists_task_devices_records' => (bool)$task->taskDevicesRecords->count(),
                    ];
                })
                ->filter(function ($task) {
                    return $task['device_count'] > 0;
                })
                ->toArray();
        } catch (ServiceException $e) {
            logger()->error("获取用户巡检任务失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取用户巡检任务失败");
        }
    }


    /**
     * 获取用户巡检任务记录表
     * @param int $taskID
     * @return array
     */
    public function getTaskRecordTableByTaskID(int $taskID): array
    {
        try {
            if (!$taskID) {
                throw new ServiceException("参数错误");
            }
            // 获取任务
            $task = Task::query()
                ->with([
                    'devices',
                    'taskDevicesRecords' => function($query) {
                        $query->select('id', 'task_id', 'device_id', 'checked_at', 'status')
                            ->orderBy('checked_at', 'desc');
                    }
                ])
                ->findOrFail($taskID);

            // 检查是否到达巡检周期
            $frequency = $task->frequency;
            $latestCheckedAt = $task->taskDevicesRecords->max('checked_at');
            $daysSinceCreation = Carbon::parse($latestCheckedAt)->diffInDays(Carbon::now());
            if ($daysSinceCreation % $frequency != 0) {
                return [
                    'message' => "未到巡检周期（{$frequency}天1次）\n上次巡检时间："
                        . Carbon::parse($latestCheckedAt)->format('Y-m-d'),
                    'waitInspect' => [],
                    'inspected' => []
                ];
            } else {
                // 到达巡检周期，创建巡检设备记录表
                app(InspectTaskRecordService::class)->generateTaskDeviceRecordTable($task);

                // 刷新数据,避免后面获取记录表时不是最新的数据
                $task->refresh();

                // 获取设备巡检状态
                $devicesStatus = $task->devices->mapToGroups(function ($device) use ($task) {
                    // 获取设备最新的巡检记录
                    $latestRecord = $task->taskDevicesRecords
                        ->where('device_id', $device->id)
                        ->sortByDesc('checked_at')
                        ->first();

                    // 默认待巡检
                    $status = 'waitInspect';

                    if ($latestRecord) {
                        $isSameDay = Carbon::parse($latestRecord->checked_at)->isToday();
                        // 是否在已巡检状态集
                        $isInspected = in_array($latestRecord->status, TaskDevicesRecord::INSPECTED_STATUS);

                        // 同一天且已巡检
                        if ($isSameDay && $isInspected) {
                            $status = 'inspected';
                        }
                    }
                    $device = [
                        'id' => $latestRecord->id,
                        'device_id' => $device->id,
                        'device_name' => $device->name,
                        'device_img' => $device->image_url,
                        'device_item_count' => $device->inspection_item_count,
                        'device_remark' => $device->remark ?: '',
                    ];
                    return [$status => $device];
                });

                return [
                    'message' => '获取['.date('Y-m-d').']巡检任务记录表成功',
                    'waitInspect' => $devicesStatus->get('waitInspect', collect())->values()->all(),
                    'inspected' => $devicesStatus->get('inspected', collect())->values()->all(),
                ];
            }

        } catch (\Exception $e) {
            logger()->error("获取巡检任务记录表失败", [
                'error' => $e->getMessage(),
                'task_id' => $taskID,
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取巡检任务记录表失败");
        }
    }

    /**
     * 添加关联设备
     * @param int $taskID
     * @param array $devices
     * @return void
     */
    public function addAssocDevice(int $taskID, array $devices): void
    {
        try {
            $task = Task::query()->findOrFail($taskID);

            // 获取当前已关联设备 ID
            $existingDeviceIDs = $task->devices->pluck('id')->toArray();

            // 计算真正需要新增的
            $newDeviceIDs = array_diff($devices, $existingDeviceIDs);

            // 如果有需要新增的
            if (!empty($newDeviceIDs)) {
                // 新增关联（只加未存在的）
                $task->devices()->attach($newDeviceIDs);

                // 更新 device_count
                $task->increment('device_count', count($newDeviceIDs));
            }

        } catch (\Exception $e) {
            logger()->error("新增关联设备失败", [
                'task_id' => $taskID,
                'devices' => $devices,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("新增关联设备失败");
        }
    }
}
