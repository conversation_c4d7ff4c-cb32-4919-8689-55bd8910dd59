<?php

namespace App\Services\Inspect;

use App\Exceptions\ServiceException;
use App\Models\Inspect\Clues;
use App\Models\Inspect\HiddenDanger;
use App\Models\Inspect\UserAmaze;
use App\Models\User;
use App\Services\HuaWei\ModerationService;
use Illuminate\Contracts\Pagination\CursorPaginator;
use Illuminate\Support\Facades\Auth;
use Sqids\Sqids;

/**
 * 随手拍-隐患服务
 */
class InspectHiddenDangerService
{
    /**
     * 创建隐患记录
     * @param string $question
     * @param string $suggestion
     * @param array $images
     * @param int $isPublic
     * @return HiddenDanger
     */
    public function create(string $question, string $suggestion, array $images, int $isPublic): HiddenDanger
    {
        try {
            $userID = Auth::id();
            if (!$userID) {
                throw new ServiceException("用户不存在");
            }
            $moderationService = app(ModerationService::class);
            $contentRes = $moderationService->contentCheck($question . ' ' . $suggestion);
            if ($contentRes['result']['suggestion'] != 'pass') {
                throw new ServiceException('您输入的内容中包含敏感词汇，确保内容合规后再试');
            }
            foreach ($images as $image) {
                $imageRes = $moderationService->imageCheck($image);
                if ($imageRes['result']['suggestion'] != 'pass') {
                    throw new ServiceException('您上传的图片中包含敏感内容，确保图片合规后再试');
                }
            }

            $hiddenDanger = new HiddenDanger();
            $hiddenDanger->fill([
                'question' => $question,
                'suggestion' => $suggestion,
                'images' => $images,
                'is_public' => $isPublic,
                'is_approve' => $isPublic === HiddenDanger::IS_PUBLIC_NO ? HiddenDanger::APPROVE_YES : HiddenDanger::APPROVE_NO, // 是否需要审批
                'user_id' => $userID,
            ]);
            $hiddenDanger->save();
            return $hiddenDanger;
        } catch (ServiceException $e) {
            logger()->error("创建隐患记录失败", [
                'data' => [
                    'question' => $question,
                    'suggestion' => $suggestion,
                    'images' => $images,
                    'is_public' => $isPublic,
                    'user_id' => Auth::id() ?? 0,
                ],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException($e->getMessage());
        }
    }

    /**
     * 获取用户隐患记录
     *
     * @param int $userID
     * @return array
     * @throws ServiceException
     */
    public function getHiddenDangerByUserID(int $userID): array
    {
        try {
            if (!$userID) {
                throw new ServiceException("用户ID不能为空");
            }
            $hiddenDangers = HiddenDanger::query()
                ->where('is_approve', HiddenDanger::APPROVE_YES)
                ->where('user_id', $userID)
                ->select(['id', 'question', 'suggestion', 'images', 'approver_status'])
                ->orderBy('id', 'desc')
                ->get()
                ->mapToGroups(function ($item) {
                    $approverStatus = $item->approver_status;
                    $status = 'waitConfirm';
                    $hiddenDanger = $item->toArray();
                    $hiddenDanger['approver_text'] = '';
                    if (!is_null($approverStatus)) {
                        $status = 'confirmed';
                        $hiddenDanger['approver_text'] = $approverStatus == HiddenDanger::APPROVE_WAIT ? '不存在隐患' : '存在隐患';
                    }
                    return [$status => $hiddenDanger];
                });
            return [
                'waitConfirm' => $hiddenDangers->get('waitConfirm', collect())->values()->all(),
                'confirmed' => $hiddenDangers->get('confirmed', collect())->values()->all(),
            ];
        } catch (ServiceException $e) {
            logger()->error("获取用户隐患记录失败", [
                'user_id' => $userID,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取用户隐患记录失败," . $e->getMessage());
        }
    }

    /**
     * 按类型获取隐患大厅数据
     * @param string $type
     * @param string|null $cursor
     * @return CursorPaginator
     */
    public function getHiddenDangerHallDataByType(string $type, string $cursor = null): \Illuminate\Contracts\Pagination\CursorPaginator
    {
        try {
            $query = HiddenDanger::query()
                ->select(['id', 'question', 'suggestion', 'images']);
            switch ($type) {
                case 'top':
                    // 前10按顶赞最多，后面按时间倒序
                    $query->where('is_public', HiddenDanger::IS_PUBLIC_YES)
                        ->orderByDesc('amazed')
                        ->orderByDesc('created_at');
                    break;

                case 'down':
                    // 前10按踩赞最多，后面按时间倒序
                    $query->where('is_public', HiddenDanger::IS_PUBLIC_YES)
                        ->orderByDesc('no_amazed')
                        ->orderByDesc('created_at');
                    break;

                case 'mine':
                    // 当前用户发布
                    $query->where('user_id', Auth::id())
                        ->orderByDesc('created_at');
                    break;

                default:
                    // 默认等同于 top
                    $query->where('is_public', HiddenDanger::IS_PUBLIC_YES)
                        ->orderByDesc('amazed')
                        ->orderByDesc('created_at');
                    break;
            }

            return $query->cursorPaginate(perPage: 20, cursor: $cursor);
        } catch (ServiceException $e) {
            logger()->error("获取大厅隐患记录失败", [
                'type' => $type,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取大厅隐患记录失败");
        }
    }

    /**
     * 获取隐患记录详情
     * @param int $hiddenDangerID
     * @param string $type 请求来源:mime, hall
     * @return array
     */
    public function getHiddenDangerByID(int $hiddenDangerID, string $type): array
    {
        try {
            if (!in_array($type, HiddenDanger::REQUEST_ORIGIN)) {
                throw new ServiceException("参数错误");
            }
            if (empty($hiddenDangerID)) {
                throw new ServiceException("参数错误");
            }
            $hiddenDanger = HiddenDanger::query()->findOrFail($hiddenDangerID);

            if ($type === HiddenDanger::REQUEST_ORIGIN_HALL
                && $hiddenDanger->is_public == HiddenDanger::IS_PUBLIC_NO
                && $hiddenDanger->user_id !== Auth::id()
            ) {
                throw new ServiceException("您没有权限查看");
            }

            $defaultFields = [
                'number' => (new Sqids(minLength: 6))->encode([$hiddenDangerID]),
                'question' => $hiddenDanger->question ?? '',
                'suggestion' => $hiddenDanger->suggestion ?? '',
                'images' => $hiddenDanger->images ?? [],
                'amazed' => $hiddenDanger->amazed ?? 0,
                'no_amazed' => $hiddenDanger->no_amazed ?? 0,
                'created_at' => $hiddenDanger?->created_at?->toDateTimeString(),
                'is_owner' => $hiddenDanger?->user_id === Auth::id(),
                'is_public' => $hiddenDanger->is_public,
                'type' => $type,
            ];

            $handleProcess = [];
            if ($type === HiddenDanger::REQUEST_ORIGIN_MINE) {
                // 为null说明未找任何人审批处理
                $isCanApprove = is_null($hiddenDanger->approver_status);
                if ($isCanApprove) {
                    $handleProcess = [
                        [
                            'avatar' => $hiddenDanger->user->avatar,
                            'nickname' => $hiddenDanger->user->nickname,
                            'time' => $hiddenDanger->created_at->toDateString(),
                            'text' => '提交'
                        ]
                    ];
                } else {
                    $approverUser = User::query()->find($hiddenDanger->approver_id ?? 0);
                    $handleProcess = [
                        [
                            'avatar' => $approverUser->avatar,
                            'nickname' => $approverUser->nickname,
                            'time' => $hiddenDanger->approver_at->toDateString(),
                            'text' => '确认 ' . ($hiddenDanger->approver_status == HiddenDanger::APPROVE_WAIT ? '不存在隐患' : '存在隐患')
                        ],
                        [
                            'avatar' => $hiddenDanger->user->avatar,
                            'nickname' => $hiddenDanger->user->nickname,
                            'time' => $hiddenDanger->created_at->toDateString(),
                            'text' => '提交'
                        ],
                    ];
                }
            }
            return array_merge($defaultFields, [
                'handle_process' => $handleProcess
            ]);
        } catch (ServiceException $e) {
            logger()->error("获取隐患记录详情失败", [
                'hidden_danger_id' => $hiddenDangerID,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取隐患记录详情失败," . $e->getMessage());
        }
    }

    /**
     * 处理审批隐患记录
     * @param int $hiddenDangerID
     * @param int $status
     * @return void
     */
    public function approveHiddenDanger(int $hiddenDangerID, int $status): void
    {
        try {
            $hiddenDanger = HiddenDanger::query()->findOrFail($hiddenDangerID);

            if ($hiddenDanger->user_id === Auth::id()) {
                throw new ServiceException("不能处理自己提交的记录");
            }
            if ($hiddenDanger->approver_status !== null) {
                throw new ServiceException("该隐患记录已处理");
            }
            if ($status !== HiddenDanger::APPROVE_WAIT && $status !== HiddenDanger::APPROVE_DONE) {
                throw new ServiceException("参数错误");
            }
            $hiddenDanger->fill([
                'approver_id' => Auth::id(),
                'approver_status' => $status,
                'approver_at' => now(),
            ]);
            $hiddenDanger->save();
        } catch (ServiceException $e) {
            logger()->error("处理审批隐患记录失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("处理审批隐患记录失败," . $e->getMessage());
        }
    }

    /**
     * 处理隐患记录
     * @param int $hiddenDangerID
     * @param string $type amazed顶赞, no_amazed踩赞, lock设为私密或公开, delete删除
     * @return void
     */
    public function handleHiddenDanger(int $hiddenDangerID, string $type): void
    {
        try {
            if (!in_array($type, ['amazed', 'no_amazed', 'lock', 'delete'])) {
                throw new ServiceException("参数错误");
            }
            $userID = Auth::id();
            if (empty($userID)) {
                throw new ServiceException("请先登录");
            }

            $hiddenDanger = HiddenDanger::query()->findOrFail($hiddenDangerID);

            if (in_array($type, ['amazed', 'no_amazed'])) {
                $userAmazed = UserAmaze::query()
                    ->where('user_id', $userID)
                    ->where('hidden_danger_id', $hiddenDangerID)
                    ->count();
                if ($userAmazed > 0) {
                    throw new ServiceException("您已对该记录进行过投票了");
                }
            }

            if (in_array($type, ['lock', 'delete'])) {
                if ($hiddenDanger->user_id !== $userID) {
                    throw new ServiceException("您没有权限处理该记录");
                }
            }

            switch ($type) {
                case 'amazed':
                    $hiddenDanger->increment('amazed');
                    UserAmaze::query()->create([
                        'user_id' => $userID,
                        'hidden_danger_id' => $hiddenDangerID,
                        'type' => UserAmaze::DING_TYPE,
                    ]);
                    break;
                case 'no_amazed':
                    $hiddenDanger->increment('no_amazed');
                    UserAmaze::query()->create([
                        'user_id' => $userID,
                        'hidden_danger_id' => $hiddenDangerID,
                        'type' => UserAmaze::CAI_TYPE,
                    ]);
                    break;
                case 'lock':
                    $hiddenDanger->is_public = (int)!$hiddenDanger->is_public;
                    $hiddenDanger->save();
                    break;
                case 'delete':
                    $hiddenDanger->delete();
                    break;
            }

        } catch (ServiceException $e) {
            logger()->error("处理隐患记录失败[顶、踩、私密、删除]", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException($e->getMessage());
        }
    }

    /**
     * 添加线索记录
     * @param int $hiddenDangerID
     * @param string $name
     * @param string $phone
     * @param string $company
     * @return void
     */
    public function addClueRecords(int $hiddenDangerID, string $name, string $phone, string $company): void
    {
        try {
            Clues::query()->create([
                'target_id' => $hiddenDangerID,
                'target_type' => HiddenDanger::class,
                'name' => $name,
                'phone' => $phone,
                'company' => $company,
            ]);
        } catch (ServiceException $e) {
            logger()->error("添加线索记录失败", [
                'hidden_danger_id' => $hiddenDangerID,
                'name' => $name,
                'phone' => $phone,
                'company' => $company,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("添加线索记录失败");
        }
    }
}
