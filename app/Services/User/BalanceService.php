<?php

namespace App\Services\User;

use App\Core\Enums\BusinessType;
use App\Core\OrderableInterface;
use App\Exceptions\ServiceException;
use App\Models\Order\Order;
use App\Models\User;
use Illuminate\Support\Facades\DB;

/**
 * 用户余额服务
 */
class BalanceService
{

    /**
     * 收入余额（充值）
     *
     * @param int $userId 用户ID
     * @param int|float|string $amount 要增加的金额(精确到两位小数点，小数时请传字符串)
     * @param BusinessType $businessType 业务类型
     * @param int $businessId 业务ID
     * @param string $remark 备注
     * @return User\UserBalanceRecord 产生的余额记录
     */
    public static function recharge($userId, $amount, BusinessType $businessType, $businessId, $remark='')
    {
        return self::update($userId, $amount, User\UserBalanceRecord::TYPE_RECHARGE, $businessType, $businessId, $remark);
    }

    /**
     * 支出余额（消费）
     *
     * @param int $userId 用户ID
     * @param int|float|string $amount 要减少的金额（精确到两位小数点）
     * @param BusinessType $businessType 业务类型
     * @param int $businessId 业务ID
     * @param string $remark 备注
     * @return User\UserBalanceRecord 产生的余额记录
     */
    public static function consume($userId, $amount, BusinessType $businessType, $businessId, $remark='')
    {
        return self::update($userId, $amount, User\UserBalanceRecord::TYPE_CONSUME, $businessType, $businessId, $remark);
    }

    /**
     * 使用余额支付订单
     *
     * @param Order $order 订单
     * @param int $userId
     * @return User\UserBalanceRecord 产生的余额记录
     */
    public static function payment(Order $order, $userId)
    {
        if ($order->status != Order::STATUS_UNPAID) {
            throw new ServiceException('该订单状态无法支付。');
        }

        $modelClass = $order->business_type->modelClass();

        //必须在 BusinessType 中为对应资源类型定义了 modelClass 才能进行处理
        if (!$modelClass) {
            throw new ServiceException("订单 {$order->order_no} 资源类型未知。");
        }

        if ($order->business_id) {
            $orderable = $modelClass::find($order->business_id);
        } else {
            $orderable = new $modelClass();
        }

        //对应类型的 modelClass 必须实现了 OrderableInterface 才能进行处理
        if (!$orderable instanceof OrderableInterface) {
            throw new ServiceException("订单 {$order->order_no} 无法进行处理。");
        }

        DB::beginTransaction();

        try {
            $amount = $order->total_amount;
            $record = self::consume($userId, $amount, $order->business_type, $order->business_id, $order->title);

            if ($orderable->orderDelivery($order)) {
                $order->payment_amount = $amount;
                $order->status = Order::STATUS_PAID;
                $order->payment_method = Order::PAY_METHOD_BALANCE;
                $order->payment_id = $record->id;
                $order->payment_at = now();
                $order->save();

                DB::commit();
            } else {
                throw new ServiceException('支付处理失败，请稍候再试。');
            }

            return $record;
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 变更积分
     *
     * @param int $userId
     * @param int $amount
     * @param string $type
     * @psalm-param "recharge"|"consume" $type 变更类型，recharge 收入，consume 支出
     * @param BusinessType $businessType
     * @param int $businessId
     * @param string $remark
     * @return User\UserBalanceRecord
     */
    public static function update($userId, $amount, $type, BusinessType $businessType, $businessId, $remark='')
    {
        if ($amount == 0) {
            throw new ServiceException('积分没有变化。');
        }

        /** @var User $user */
        $user = User::query()
            ->where('id', $userId)
            ->first();

        if ($type == User\UserBalanceRecord::TYPE_CONSUME && $user->balance < $amount) {
            throw new ServiceException('余额不足。', 403);
        }

        $originBalance = $user->balance;
        $changeBalance = $type == User\UserBalanceRecord::TYPE_RECHARGE ? $amount : -$amount;

        DB::beginTransaction();

        try {
            //变更积分
            $type == User\UserBalanceRecord::TYPE_RECHARGE ? $user->increment('balance', $amount) : $user->decrement('balance', $amount);

            //新增记录
            $log = new User\UserBalanceRecord();
            $log->user_id = $userId;
            $log->origin_balance = $originBalance;
            $log->amount = $changeBalance;
            $log->type = $type;
            $log->business_type = $businessType;
            $log->business_id = $businessId;
            $log->remark = $remark;
            $log->save();

            DB::commit();

            return $log;
        } catch (\Throwable $e) {
            DB::rollBack();
            throw new ServiceException('更新余额失败。'.$e->getMessage(), previous: $e);
        }
    }

}
