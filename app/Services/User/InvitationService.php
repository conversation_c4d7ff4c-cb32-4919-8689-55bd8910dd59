<?php

namespace App\Services\User;

use App\Core\Enums\BusinessType;
use App\Models\Invitation;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 邀请服务
 */
class InvitationService
{

    /**
     * 建立推荐关系并发放推荐奖励
     *
     * @param string $referralUuid
     * @param int $inviteeUserId
     */
    public static function invite($referralUuid, $inviteeUserId)
    {
        $referralId = User::query()
            ->select('id')
            ->where('uuid', $referralUuid)
            ->value('id');

        if (!$referralId) {
            Log::warning("建立邀请关系失败，邀请者 $referralUuid 未找到。");
            return;
        }

        $config = config('heguibao.invitation');

        //检查是否超过每日邀请上限，是则不再奖励
        $reward = $config['reward_credit'] > 0;

        if ($config['limit_per_day'] > 0) {
            $countToday = Invitation::query()
                ->where('referral_id', $referralId)
                ->whereBetween('created_at', [Carbon::now()->startOfDay(), Carbon::today()->endOfDay()])
                ->where('status', Invitation::STATUS_REWARDED)
                ->count();
            if ($countToday >= $config['limit_per_day']) {
                $reward = false;
                Log::info("用户 $referralId 邀请 $inviteeUserId 不再奖励，因邀请人数 $countToday 已超过当日上限 {$config['limit_per_day']}。");
            }
        }

        DB::beginTransaction();

        try {
            $invitation = new Invitation();
            $invitation->referral_id = $referralId;
            $invitation->invitee_id = $inviteeUserId;
            $invitation->status = $reward ? Invitation::STATUS_REWARDED : Invitation::STATUS_INVITED;
            $invitation->credit = $reward ? $config['reward_credit'] : 0;
            $invitation->send_at = $reward ? Carbon::now() : null;
            $invitation->save();

            if ($reward) {
                CreditService::recharge($referralId, $config['reward_credit'], BusinessType::Invitation, $invitation->id, '邀请注册奖励');
            }

            DB::commit();

        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error("处理邀请失败，$referralId 邀请 {$inviteeUserId}，失败原因：{$e->getMessage()}");
        }
    }

}
