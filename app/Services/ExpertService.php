<?php

namespace App\Services;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Expert;
use App\Services\Common\AttachmentService;
use App\Services\Common\PhoneCodeService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ExpertService
{
    /**
     * 创建专家数据
     *
     * @param array $data
     * @param int $userId
     * @return Expert
     * @throws \Throwable
     */
    public static function store(array $data, int $userId): Expert
    {
        PhoneCodeService::checkPhoneNumber($data['phone']);
        try {
            DB::beginTransaction();

            $expert = new Expert();
            foreach ($data as $key => $value) {
                switch ($key) {
                    case 'photo':
                        $expert->$key = '';
                        break;
                    case 'certs_add':
                        $expert->certs = [];
                        break;
                    case 'scene_photos_add':
                        $expert->scene_photos = [];
                        break;
                    default:
                        $expert->$key = $value;
                        break;
                }
            }
            $expert->user_id = $userId;
            $expert->is_visible = Expert::VISIBLE_NO;
            $expert->save();

            if (isset($data['photo'])) {
                $file = AttachmentService::store($data['photo'], 'form', BusinessType::Expert, $expert->id);
                $expert->photo = $file->path;
            }

            if (isset($data['certs_add'])) {
                $expert->certs = self::uploadFiles($data['certs_add'], [], $expert->id);
            }

            if (isset($data['scene_photos_add'])) {
                $expert->scene_photos = self::uploadFiles($data['scene_photos_add'], [], $expert->id);
            }
            $expert->save();

            DB::commit();

            return $expert;
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新专家数据
     *
     * @param array $data
     * @param Expert $expert
     * @return Expert
     * @throws \Throwable
     */
    public static function update(array $data, Expert $expert): Expert
    {
        PhoneCodeService::checkPhoneNumber($data['phone']);

        try {
            DB::beginTransaction();

            if (isset($data['photo']) && $data['photo'] != $expert->photo) {
                AttachmentService::removeRelations(BusinessType::Expert, $expert->id, $expert->photo);
                $file = AttachmentService::store($data['photo'], 'form', BusinessType::Expert, $expert->id);
                $data['photo'] = $file->path;
            }

            foreach ($data as $key => $value) {
                if (!in_array($key, ['certs', 'certs_add', 'certs_remove', 'scene_photos', 'scene_photos_add', 'scene_photos_remove'])) {
                    $expert->$key = $value;
                }
            }

            $certs = $expert->certs;

            if (isset($data['certs_add'])) {
                $certs = self::uploadFiles($data['certs_add'], $certs, $expert->id);
            }

            if (isset($data['certs_remove'])) {
                $certs = self::removeFiles($data['certs_remove'], $certs, $expert->id);
            }

            if ($certs != $expert->certs) {
                $certs = array_values($certs);
                $expert->certs = $certs;
            }

            $scenePhotos = $expert->scene_photos;

            if (isset($data['scene_photos_add'])) {
                $scenePhotos = self::uploadFiles($data['scene_photos_add'], $scenePhotos, $expert->id);
            }

            if (isset($data['scene_photos_remove'])) {
                $scenePhotos = self::removeFiles($data['scene_photos_remove'], $scenePhotos, $expert->id);
            }

            if ($scenePhotos != $expert->scene_photos) {
                $scenePhotos = array_values($scenePhotos);
                $expert->scene_photos = $scenePhotos;
            }

            $expert->save();

            DB::commit();

            return $expert;
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 保存文件
     *
     * @param array $postPaths 上传文件key
     * @param array $files 原文件
     * @param int $expertId 专家ID
     * @return array
     */
    public static function uploadFiles(array $postPaths, array $files, int $expertId): array
    {
        foreach ($postPaths as $postPath) {
            $file = AttachmentService::store($postPath, 'form', BusinessType::Expert, $expertId);
            $files[] = [
                'id' => $file->id,
                'mime' => $file->mime,
                'path' => $file->path,
                'filename' => $file->filename,
                'filesize' => $file->filesize
            ];
        }

        return $files;
    }

    /**
     * 删除文件
     *
     * @param array $removeFiles 删除文件key
     * @param array $files 原文件
     * @param int $expertId 专家ID
     * @return array
     */
    public static function removeFiles(array $removeFiles, array $files, int $expertId): array
    {
        foreach ($removeFiles as $path) {
            // 删除文件关联
            AttachmentService::removeRelations(BusinessType::Expert, $expertId, $path);
            // 删除相关数据
            $files = array_filter($files, fn($file) => $file['path'] != $path);
        }

        return $files;
    }

    /**
     * 拒绝
     * @param int $id
     * @param string $reason
     * @return Expert
     */
    public static function reject(int $id, string $reason): Expert
    {
        /** @var Expert $expert */
        $expert = Expert::query()->find($id);

        if (!$expert) {
            throw new ServiceException("对象不存在");
        }

        $expert->reason = $reason;
        $expert->status = Expert::STATUS_REJECT;
        $expert->save();

        return $expert;
    }

    /**
     * 通过
     * @param int $id
     * @return Expert
     */
    public static function resolve(int $id): Expert
    {
        /** @var Expert $expert */
        $expert = Expert::query()->find($id);

        if (!$expert) {
            throw new ServiceException("对象不存在");
        }

        $expert->reason = '';
        $expert->pass_at = Carbon::now();
        $expert->status = Expert::STATUS_NORMAL;
        $expert->is_visible = Expert::VISIBLE_YES;
        $expert->save();

        return $expert;
    }
}
