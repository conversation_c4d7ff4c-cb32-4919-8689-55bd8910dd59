<?php

namespace App\Services;

use App\Exceptions\ServiceException;
use App\Libs\Wechat\EasyWechatFactory;
use App\Models\User\UserMpAccount;
use App\Models\WechatNoticeRecord;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class WechatService
{
    /**
     * 微信小程序订阅通知公共方法
     *
     * @param int $userId 用户ID
     * @param string $templateId 模板ID
     * @param array $data 发送数据
     * @param string $messageSample 数据样本
     * @param string $noticeType 通知类型
     * @param string|null $page 跳转页面
     * @return void
     */
    public static function commonNotice(int $userId, string $templateId, array $data, string $messageSample,
                                        string $noticeType, ?string $page = null): void
    {
        $log = Log::channel('wechat_notice');

        $openId = UserMpAccount::query()
            ->where('user_id', $userId)
            ->where('platform', UserMpAccount::PLATFORM_WECHAT_MP)
            ->value('open_id');
        if (!$openId) {
            return;
        }

        $status = WechatNoticeRecord::STATUS_SUCCESS;
        $errorMessage = '';

        $context = [
            'open_id' => $openId,
            'template_id' => $templateId
        ];

        for ($i = 0; $i < 3; $i++) {
            try {
                $res = EasyWechatFactory::sendSubscribeMessage($openId, $templateId, $data, $page);
                if ($res['errcode'] == 0) {
                    $log->info("发送{$noticeType}消息成功\n$messageSample\n", $context);
                } else {
                    $status = WechatNoticeRecord::STATUS_FAIL;
                    $errorMessage = $res['errmsg'];
                    $log->error("发送{$noticeType}消息失败：{$res['errmsg']}\n$messageSample\n", $context);
                }
                // 微信订阅消息通知记录
                WechatNoticeRecord::add($userId, $openId, $templateId, $status, $noticeType, $messageSample, $errorMessage);
                return;
            } catch (\Throwable $e) {
                $status = WechatNoticeRecord::STATUS_FAIL;
                $errorMessage = $e->getMessage();
                $log->error("发送{$noticeType}消息失败：" . $e->__toString() . "\n$messageSample\n", $context);
            }
        }

        // 微信订阅消息通知记录
        WechatNoticeRecord::add($userId, $openId, $templateId, $status, $noticeType, $messageSample, $errorMessage);
    }

    /**
     * 生成适用于小程序二维码传递的 scene 值
     *
     * @param string $action 代表应用场景的类型字符串
     * @return string 固定32位长度随机字符串
     */
    public static function randomScene($action)
    {
        if (strlen($action) > 10) {
            $prefix = substr($action, 0, 10);
            $randLen = 12;
        } else {
            $prefix = $action;
            $randLen = 10 - strlen($action) + 12;
        }
        return $prefix.Str::random($randLen).time();
    }

    /**
     * 创建 scene 透传，用于微信小程序传参场景
     *
     * @param string $action 对应的动作
     * @param array $data 要保存的数据
     * @return string Scene 值（固定32位长度）
     */
    public static function makeScene($action, $data)
    {
        $key = self::randomScene($action);
        Cache::put('wechat:scene:'.$key, compact('action', 'data'), 600);
        return $key;
    }

    /**
     * 更新 scene 透传数据
     *
     * @param string $key
     * @param array $data 要更新的数据，注意更新的数据会与原有数据合并，第一层相同的键将被覆盖（通过 array_merge）
     * @return void
     */
    public static function updateScene($key, $data)
    {
        $key = 'wechat:scene:'.$key;
        $scene = Cache::get($key);

        if (!$scene) {
            throw new ServiceException('会话已经失效，请重新操作。');
        }

        $scene['data'] = array_merge($scene['data'], $data);
        Cache::put($key, $scene, 600);
    }

    /**
     * 获取 scene 透传数据
     *
     * @param string $key
     * @return mixed|null
     */
    public static function getScene($key)
    {
        return Cache::get('wechat:scene:'.$key);
    }

    /**
     * 生成不限制的小程序码
     *
     * @param string $scene 唯一可用的小程序扫码透传参数，32 个字符以内
     * @param string $page 扫码到的页面路径，如 pages/login/authorize，前面不要有 /
     * @param bool $checkPath 是否检查页面是否存在
     * @param string|null $envVersion 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"，仅在微信外打开时生效，默认是正式版。
     * @return array{type: string, image: string} type: 图片 mime 类型，string: 图片 Buffer
     * @throws ServiceException
     * @see https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/qr-code/getUnlimitedQRCode.html
     */
    public static function getWxaCodeUnlimit($scene, $page, $checkPath=false, $envVersion=null)
    {
        try {
            $data = [
                'scene' => $scene,
                'page' => $page,
                'check_path' => $checkPath
            ];

            if ($envVersion) {
                $data['env_version'] = $envVersion;
            }

            $response = EasyWechatFactory::mp()->getClient()->post('wxa/getwxacodeunlimit', [
                'json' => $data
            ]);
        } catch (\Exception $e) {
            Log::warning("获取微信小程序二维码失败：{$e->getMessage()}");
            throw new ServiceException('获取二维码失败，请稍候再试。');
        }

        if ($response->isSuccessful()) {
            return [
                'type' => $response->getHeaderLine('Content-Type'),
                'image' => $response->getContent()
            ];
        } else {
            Log::warning("获取微信小程序二维码失败：{$response['errcode']} {$response['errmsg']}");
            throw new ServiceException($response['errmsg']);
        }
    }

}
