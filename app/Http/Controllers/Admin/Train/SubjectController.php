<?php

namespace App\Http\Controllers\Admin\Train;

use App\Http\Controllers\Admin\Controller;
use App\Models\Train\Subject;
use App\Services\Admin\OperateLogService;
use App\Services\Topic\SubjectService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class SubjectController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'topic_id' => 'integer',
            'chapter_id' => 'integer',
            'section_id' => 'integer',
            'type' => 'integer',
            'keyword' => 'string',
            'created_at' => 'array',
        ]);

        $builder = Subject::query()->with(['topic']);
        $this->builderWhere($builder, $params, ['id', 'topic_id', 'chapter_id', 'section_id', 'type', 'created_at']);
        $this->builderOrderBy($request, $builder);

        if (!empty($params['keyword'])) {
            $builder->where('intro', 'like', "%{$params['keyword']}%");
        }

        $data = $this->apiPaginate($request, $builder);

        $data['records']->map(function ($subject) {
            $subject->topic?->setHidden([]);
            $subject->options?->map(function ($option) {
                $option->setHidden([]);
            });
        });

        return $data;
    }

    public function show(int $id): Subject
    {
        /** @var Subject $subject */
        $subject = Subject::query()->with(['topic', 'options', 'attachments', 'attachments.file'])->find($id);

        if (!$subject) {
            throw new BadRequestHttpException('试题不存在');
        }

        $subject->options?->map(function ($option) {
            $option->setHidden([]);
        });

        return $subject->setHidden([]);
    }

    public function store(Request $request): Subject
    {
        $params = $request->validate([
            'topic_id' => 'required|integer',
            'chapter_id' => 'integer',
            'section_id' => 'integer',
            'intro' => 'required|string',
            'type' => 'required|integer',
            'judge_correct' => 'integer',
            'answer' => 'string',
            'analysis' => 'string',
            'options' => 'array|min:2',
            'upload_files' => 'array'
        ]);

        $subject = SubjectService::create($params);

        OperateLogService::create(auth('admin')->id(), '添加题目', ['id' => $subject->id, ...$params]);

        return $subject->setHidden([]);
    }

    public function update(Request $request, int $id): Subject
    {
        $params = $request->validate([
            'chapter_id' => 'integer',
            'section_id' => 'integer',
            'intro' => 'string',
            'judge_correct' => 'integer',
            'answer' => 'string',
            'analysis' => 'string',
            'options' => 'array|min:2',
            'upload_files' => 'array'
        ]);

        $subject = SubjectService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改题目', ['id' => $subject->id, ...$params]);

        return $subject->setHidden([]);
    }

    public function destroy(int $id): array
    {
        SubjectService::remove([$id]);

        OperateLogService::create(auth('admin')->id(), '删除题目', $id);

        return $this->success();
    }

    public function batchDestroy(Request $request): array
    {
        $params = $request->validate([
            'ids' => 'required|array',
            'force' => 'in:0,1'
        ]);

        SubjectService::remove($params['ids'], boolval($params['force'] ?? 0));

        OperateLogService::create(auth('admin')->id(), '删除题目', $params);

        return $this->success();
    }

    public function getTypeCount(Request $request): array
    {
        $params = $request->validate([
            'topic_id' => 'required|integer',
        ]);

        return SubjectService::getTypeCount($params['topic_id']);
    }
}
