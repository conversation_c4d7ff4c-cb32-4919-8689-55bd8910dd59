<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Http\Controllers\Admin\Controller;
use App\Models\Cms\Category;
use App\Models\Cms\ContentCourseProgress;
use App\Models\Cms\ContentCourseSection;
use App\Models\Org\Course;
use App\Models\User;
use App\Models\User\UserOwnContent;
use App\Services\Cms\ContentCourseProgressService;
use App\Services\Cms\ContentCourseSectionService;
use App\Services\Org\Export\CourseProgressExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ContentCourseStatisticController extends Controller
{
    /**
     * 课程学习进度列表
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
            'phone' => 'string',
            'nickname' => 'string',
            'progress_status' => 'integer|in:1,2,3',
            'org_id' => 'integer'
        ]);

        $builder = UserOwnContent::query()
            ->with(['user' => fn ($q) => $q->with('org')])
            ->where('content_id', $params['content_id'])
            ->where('classify', Category::CLASSIFY_COURSE);

        if (!empty($params['phone'])) {
            $userId = User::query()->where('phone', $params['phone'])->value('id');
            $builder->where('user_id', $userId ?? 0);
        }

        if (!empty($params['nickname'])) {
            $userIds = User::query()
                ->where('nickname', 'like', "%{$params['nickname']}%")
                ->pluck('id')
                ->toArray();
            $builder->whereIn('user_id', $userIds);
        }

        if (!empty($params['org_id'])) {
            $builder->whereHas('user', fn($q) => $q->whereHas('org', fn ($q) => $q->where('id', $params['org_id'])));
        }

        $userIds = $builder->pluck('user_id')->toArray();
        if (empty($userIds)) {
            return $this->apiPaginate($request, $builder);
        }

        $progresses = ContentCourseProgress::query()
            ->with('section', fn ($q) => $q->with('video'))
            ->select(['user_id', 'duration', 'pos', 'finished', 'section_id'])
            ->where('content_id', $params['content_id'])
            ->whereIn('user_id', $userIds)
            ->where('org_id', 0)
            ->where('enroll_id', 0)
            ->get();
        $userProgresses = ContentCourseProgressService::getUserProgresses($progresses);
        $courseDuration = ContentCourseSectionService::getCourseDuration($params['content_id']);
        // 学习状态
        if (!empty($params['progress_status'])) {
            switch ($params['progress_status']) {
                case 1:// 未学
                    $studyUserIds = $progresses->pluck('user_id')->unique()->toArray();
                    $unStudyUserIds = array_diff($userIds, $studyUserIds);
                    $builder->whereIn('user_id', $unStudyUserIds);
                    break;
                case 2:// 学习中
                    $userIds = [];
                    foreach ($userProgresses as $key => $progress) {
                        if ($progress['study_duration'] < $courseDuration) {
                            $userIds[] = $key;
                        }
                    }
                    $builder->whereIn('user_id', $userIds);
                    break;
                case 3:// 已学
                    $userIds  = [];
                    foreach ($userProgresses as $key => $progress) {
                        if ($progress['study_duration'] == $courseDuration) {
                            $userIds[] = $key;
                        }
                    }
                    $builder->whereIn('user_id', $userIds);
                    break;
                default:
                    throw new BadRequestHttpException("学习状态不存在");
            }
        }

        $builder->orderByDesc('created_at');

        $list = $this->apiPaginate($request, $builder);
        if (empty($list['records'])) {
            return $list;
        }

        foreach ($list['records'] as &$record) {
            $record = ContentCourseProgressService::setProgress($record, $userProgresses, $courseDuration);
        }

        return $list;
    }

    /**
     * 学习人数统计
     *
     * @param Request $request
     * @return array
     */
    public function statistic(Request $request): array
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
        ]);

        $builder = UserOwnContent::query()
            ->where('content_id', $params['content_id'])
            ->where('classify', Category::CLASSIFY_COURSE);

        $buyCount = $builder->count();

        $userIds = $builder->pluck('user_id')->toArray();

        $studyingCount = ContentCourseProgressService::getprogressBuilder($params['content_id'], $userIds)
            ->where('finished', 0)
            ->where('duration', '>', 0)
            ->groupBy('user_id')
            ->get()
            ->count();

        $studyFinishCount = ContentCourseProgressService::getprogressBuilder($params['content_id'], $userIds)
            ->where('finished', 1)
            ->groupBy('user_id')
            ->get()
            ->count();

        return ['buy_count' => $buyCount, 'studying_count' => $studyingCount, 'study_finish_count' => $studyFinishCount];
    }

    /**
     * 导出
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
            'phone' => 'string',
            'nickname' => 'string',
            'progress_status' => 'integer|in:1,2,3',
            'org_id' => 'integer'
        ]);

        return Excel::download(new CourseProgressExportService($params['content_id'], $params['phone'] ?? '',
            $params['nickname'] ?? '', $params['progress_status'] ?? 0, $params['org_id'] ?? 0),
            '课程学习进度.xlsx');
    }
}
