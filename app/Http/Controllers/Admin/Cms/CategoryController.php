<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Http\Controllers\Admin\Controller;
use App\Libs\Utils\Helpers;
use App\Models\Cms\Category;
use App\Services\Admin\OperateLogService;
use App\Services\Cms\CategoryService;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'classify' => 'string',
            'visible' => 'integer',
        ]);

        $categories = CategoryService::getCategoriesByClassify($params['classify'] ?? 'material', $params['visible'] ?? null);

        return Helpers::dataToTree($categories, 'pid');
    }

    public function store(Request $request): Category
    {
        $params = $request->validate([
            'name' => 'required|string',
            'pid' => 'required|integer',
            'intro' => 'string',
            'logo' => 'string',
            'classify' => 'string',
            'visible' => 'integer',
            'allow_types' => 'array',
            'sort' => 'integer',
        ]);

        $category = CategoryService::create($params['name'], intval($params['pid']), $params);

        OperateLogService::create(auth('admin')->id(), '添加分类', ['id' => $category->id, ...$params]);

        return $category;
    }

    public function update(Request $request, int $id): Category
    {
        $params = $request->validate([
            'name' => 'string',
            'pid' => 'integer',
            'intro' => 'string',
            'logo' => 'string',
            'classify' => 'string',
            'visible' => 'integer',
            'allow_types' => 'array',
            'sort' => 'integer',
            'hot_position' => 'integer'
        ]);

        $category = CategoryService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改分类', ['id' => $category->id, ...$params]);

        return $category;
    }

    public function destroy(int $id): array
    {
        CategoryService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除分类', ['id' => $id]);

        return $this->success();
    }
}
