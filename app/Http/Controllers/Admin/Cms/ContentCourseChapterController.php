<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Http\Controllers\Admin\Controller;
use App\Models\Cms\ContentCourseChapter;
use App\Services\Admin\OperateLogService;
use App\Services\Cms\ContentCourseChapterService;
use Illuminate\Http\Request;

class ContentCourseChapterController extends Controller
{
    public function children(Request $request): array
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
        ]);

        $chapters = ContentCourseChapter::query()
            ->with([
                'sections' => function ($query) {
                    $query->orderByRaw('sort desc,id asc');
                },
                'sections.video'
            ])
            ->where('content_id', $params['content_id'])
            ->orderByRaw('sort desc,id asc')
            ->get();

        $list = ContentCourseChapterService::children($chapters);

        return ContentCourseChapterService::generateSerialNo($list);
    }

    public function store(Request $request): ContentCourseChapter
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
            'name' => 'required|string',
            'status' => 'integer',
            'sort' => 'integer',
        ]);

        $params['status'] = $params['status'] ?? 1;
        $params['sort'] = $params['sort'] ?? 0;

        $chapter = ContentCourseChapterService::create(intval($params['content_id']), $params['name'], intval($params['status']), intval($params['sort']))->setHidden([]);

        OperateLogService::create(auth('admin')->id(), '添加章', ['id' => $chapter->id, ...$params]);

        return $chapter;
    }

    public function update(Request $request, int $id): ContentCourseChapter
    {
        $params = $request->validate([
            'name' => 'string',
            'status' => 'integer',
            'sort' => 'integer',
        ]);

        $chapter = ContentCourseChapterService::update($id, $params)->setHidden([]);

        OperateLogService::create(auth('admin')->id(), '修改章', ['id' => $chapter->id, ...$params]);

        return $chapter;
    }

    public function destroy(int $id): array
    {
        ContentCourseChapterService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除章', ['id' => $id]);

        return $this->success();
    }
}
