<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Http\Controllers\Admin\Controller;
use App\Models\Cms\ContentCourseDoc;
use App\Services\Admin\OperateLogService;
use App\Services\Cms\ContentCourseDocService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ContentCourseDocController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
        ]);

        return ContentCourseDoc::query()
            ->where('content_id', $params['content_id'])
            ->orderByRaw('sort desc,id asc')
            ->get()
            ->toArray();
    }

    public function store(Request $request): ContentCourseDoc
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
            'filename' => 'required|string',
            'filepath' => 'required|string',
            'sort' => 'required|integer',
        ]);

        $doc = ContentCourseDocService::create($params['content_id'], $params['filename'], $params['filepath'], $params['sort']);

        OperateLogService::create(auth('admin')->id(), '创建课程资料', ['id' => $doc->id, ...$params]);

        return $doc;
    }

    public function update(Request $request, int $docId): ContentCourseDoc
    {
        $params = $request->validate([
            'filename' => 'required|string',
            'filepath' => 'required|string',
            'sort' => 'required|integer',
        ]);

        $doc = ContentCourseDoc::find($docId);
        if (!$doc) {
            throw new NotFoundHttpException("课程资料不存在");
        }

        $doc = ContentCourseDocService::update($doc, $params['filename'], $params['filepath'], $params['sort']);

        OperateLogService::create(auth('admin')->id(), '编辑课程资料', ['id' => $doc->id, ...$params]);

        return $doc;
    }

    public function destroy(int $docId): array
    {
        $doc = ContentCourseDoc::find($docId);
        if (!$doc) {
            throw new NotFoundHttpException("课程资料不存在");
        }

        ContentCourseDocService::delete($doc);

        OperateLogService::create(auth('admin')->id(), '删除课程资料', ['id' => $doc->id]);

        return ['message' => 'success'];
    }
}
