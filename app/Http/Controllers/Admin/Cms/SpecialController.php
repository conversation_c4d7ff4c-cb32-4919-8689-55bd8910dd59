<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Http\Controllers\Admin\Controller;
use App\Models\Admin\Role;
use App\Models\Cms\Special;
use App\Models\Cms\SpecialContent;
use App\Services\Admin\OperateLogService;
use App\Services\Admin\RoleService;
use App\Services\Cms\SpecialService;
use Illuminate\Http\Request;

class SpecialController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'name' => 'string',
            'status' => 'integer',
            'created_at' => 'array',
        ]);

        $adminId = auth('admin')->id();

        // 内容权限判断
        if (!RoleService::checkPermission($adminId, Role::PERMISSION_MANAGE_EDITOR)) {
            $params['admin_id'] = $adminId;
        }

        $builder = Special::query()->with(['admin']);
        $this->builderWhere($builder, $params, ['id', 'status', 'admin_id', 'created_at']);
        $this->builderOrderBy($request, $builder);

        if (!empty($params['name'])) {
            $builder->where('name', 'like', "%{$params['name']}%");
        }

        return $this->apiPaginate($request, $builder);
    }

    public function show(int $id): Special
    {
        /** @var Special $special */
        $special = Special::query()->with(['contents', 'contents.content'])->find($id)->setHidden([]);

        $special->contents->map(function ($specialContent) {
            /** @var SpecialContent $specialContent */
            $specialContent->content?->setHidden([]);
        });

        return $special;
    }

    public function store(Request $request): Special
    {
        $params = $request->validate([
            'name' => 'required|string',
            'intro' => 'string',
            'cover' => 'required|string',
            'charge_credit' => 'required|integer',
            'status' => 'integer',
            'recommend' => 'integer',
            'content_ids' => 'required|array|min:1',
        ]);

        $adminId = auth('admin')->id();

        $special = SpecialService::create($adminId, $params)->setHidden([]);

        OperateLogService::create(auth('admin')->id(), '添加专题', ['id' => $special->id, ...$params]);

        return $special;
    }

    public function update(Request $request, int $id): Special
    {
        $params = $request->validate([
            'name' => 'string',
            'intro' => 'string',
            'cover' => 'string',
            'charge_credit' => 'integer',
            'status' => 'integer',
            'recommend' => 'integer',
            'content_ids' => 'array|min:1',
        ]);

        $special = SpecialService::update($id, $params)->setHidden([]);

        OperateLogService::create(auth('admin')->id(), '修改专题', ['id' => $special->id, ...$params]);

        return $special;
    }

    public function recommend(Request $request, int $id): Special
    {
        $params = $request->validate([
            'recommend' => 'required|integer',
        ]);

        $special = SpecialService::recommend($id, $params)->setHidden([]);

        OperateLogService::create(auth('admin')->id(), '推荐专题', ['id' => $special->id, ...$params]);

        return $special;
    }

    public function destroy(int $id): array
    {
        SpecialService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除专题', ['id' => $id]);

        return $this->success();
    }
}
