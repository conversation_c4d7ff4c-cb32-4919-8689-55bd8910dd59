<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Http\Controllers\Admin\Controller;
use App\Models\Cms\ContentCourseSection;
use App\Services\Admin\OperateLogService;
use App\Services\Cms\ContentCourseSectionService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ContentCourseSectionController extends Controller
{
    public function store(Request $request): ContentCourseSection
    {
        $params = $request->validate([
            'chapter_id' => 'required|integer',
            'ref_video_id' => 'integer',
            'name' => 'required|string',
            'filepath' => 'string',
            'status' => 'integer',
            'sort' => 'integer',
        ]);

        $sort = $params['sort'] ?? 0;
        $refVideoId = $params['ref_video_id'] ?? 0;
        $filepath = $params['filepath'] ?? '';

        if (!$filepath && !$refVideoId) {
            throw new BadRequestHttpException('课程视频不能为空');
        }

        $section = ContentCourseSectionService::create(intval($params['chapter_id']), $params['name'], $refVideoId, $filepath, $sort)->setHidden([]);

        OperateLogService::create(auth('admin')->id(), '添加节', ['id' => $section->id, ...$params]);

        return $section;
    }

    public function update(Request $request, int $id): ContentCourseSection
    {
        $params = $request->validate([
            'name' => 'string',
            'ref_video_id' => 'integer',
            'filepath' => 'string',
            'status' => 'integer',
            'sort' => 'integer',
        ]);

        $section = ContentCourseSectionService::update($id, $params)->setHidden([]);

        OperateLogService::create(auth('admin')->id(), '修改节', ['id' => $section->id, ...$params]);

        return $section;
    }

    public function destroy(int $id): array
    {
        ContentCourseSectionService::remove($id);

        OperateLogService::create(auth('admin')->id(), '修改节', ['id' => $id]);

        return $this->success();
    }
}
