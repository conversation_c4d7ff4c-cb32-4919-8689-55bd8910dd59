<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Ers\FormProjectForm;
use App\Services\Ers\FormProjectFormService;
use Illuminate\Http\Request;

class FormProjectFormController extends Controller
{
    public function getForm(Request $request): ?FormProjectForm
    {
        $params = $request->validate([
            'project_id' => 'required|integer',
            'step_id' => 'required|integer',
            'industry_id' => 'required|integer',
            'enterprise_id' => 'required|integer',
        ]);

        $form = FormProjectFormService::getProjectForm($params['project_id'], $params['step_id'], $params['industry_id'], $params['enterprise_id']);

        if (!$form) {
            $form = FormProjectFormService::create($params);
        }

        $form->inputs;

        return $form;
    }
}
