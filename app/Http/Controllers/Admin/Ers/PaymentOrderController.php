<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Ers\PaymentOrderPayment;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Services\Ers\Modules\PaymentModule;
use App\Services\Ers\Modules\PaymentStageModule;
use App\Services\Ers\ServiceOrderStepService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * 付款模块&工单控制器
 */
class PaymentOrderController extends Controller
{

    /**
     * 设置付款价格，推送给用户
     *
     * total_amount: 500.00 - 付款金额
     * pay_amount: 100.00 - 实际需要支付的金额，仅在付款模式时需要此字段，此时代表预付款金额
     */
    public function store(Request $request, int $orderId, int $stepId)
    {
        $params = $request->validate([
            'total_amount' => 'required|numeric',
            'pay_amount' => 'numeric'
        ]);

        $t = PaymentModule::configure()->t; //直接付款模块
        $tStage = PaymentStageModule::configure()->t; //预付款模块

        /** @var ServiceOrderStep $step */
        $step = ServiceOrderStep::query()
            ->where('order_id', $orderId)
            ->where('step_id', $stepId)
            ->whereIn('module', [$t, $tStage])
            ->firstOrFail();

        //只有还没付的时候才能设置
        if ($step->status == ServiceOrderStep::STATUS_FINISH) {
            throw new BadRequestHttpException('该付款已完成无法再设置金额。');
        }

        //预付款模块必须设置预付款金额
        if ($step->module == $tStage && !isset($params['pay_amount'])) {
            throw new BadRequestHttpException('你需要设置预付款金额。');
        }

        //是不是预设置，允许没到这一步的时候就设置价格
        $preset = $step->status == ServiceOrderStep::STATUS_PENDING;

        /** @var PaymentOrderPayment $payment */
        $payment = $step->data;

        if (!$payment) {
            $payment = new PaymentOrderPayment();
            $payment->order_id = $step->order_id;
            $payment->order_step_id = $step->id;
            $payment->type = $step->module == $t ? 'normal' : 'advance';
        }

        $payment->total_amount = $params['total_amount'];
        $payment->pay_amount = $step->module == $t ? $params['total_amount'] : $params['pay_amount'];
        $payment->save();

        $step->data_id = $payment->id;
        $step->last_admin_handled_at = now();
        !$preset && $step->status = ServiceOrderStep::STATUS_USER_PENDING;

        $step->save();

        !$preset && ServiceOrderStepService::forward($step->order, $step);

        return $step;
    }

}
