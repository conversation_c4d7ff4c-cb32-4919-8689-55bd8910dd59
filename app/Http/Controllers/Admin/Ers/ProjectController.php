<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Ers\Project;
use App\Services\Admin\OperateLogService;
use App\Services\Ers\Modules\FormModule;
use App\Services\Ers\ProjectService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ProjectController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'title' => 'string',
            'status' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = Project::query()->with(['flow']);
        $this->builderWhere($builder, $params, ['id', 'status', 'created_at']);
        $this->builderOrderBy($request, $builder, 'sort asc, id desc');

        if (isset($params['title']) && $params['title']) {
            $builder->where('title', 'like', "%{$params['title']}%");
        }

        return $this->apiPaginate($request, $builder);
    }

    public function search(): array
    {
        return Project::query()->where('status', Project::STATUS_NORMAL)->get()->toArray();
    }

    public function show(int $id): Project
    {
        /** @var Project $project */
        $project = Project::query()->with([
            'flow',
            'flow.steps' => function ($query) {
                $query->where('module', FormModule::configure()->t);
            }
        ])->find($id);

        if (!$project) {
            throw new BadRequestHttpException('对象不存在');
        }

        $project->setHidden([]);

        return $project;
    }

    public function store(Request $request): Project
    {
        $params = $request->validate([
            'title' => 'required|string|min:2',
            'icon' => 'string',
            'intro' => 'string',
            'is_bind_category' => 'boolean',
            'sort' => 'integer',
            'status' => 'integer',
            'flow_id' => 'required|integer',
            'upload_files' => 'array'
        ]);

        $info = ProjectService::create($params);

        $info->setHidden([]);

        OperateLogService::create(auth('admin')->id(),'创建服务项目', $info->id);

        return $info;
    }

    public function update(Request $request, int $id): Project
    {
        $params = $request->validate([
            'title' => 'string|min:2',
            'icon' => 'string',
            'intro' => 'string',
            'status' => 'integer',
            'sort' => 'integer',
            'upload_files' => 'array'
        ]);

        $info = ProjectService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改服务项目', $info->id);

        $info->setHidden([]);

        return $info;
    }

    public function destroy(int $id): array
    {
        ProjectService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除服务项目', ['id' => $id]);

        return $this->success();
    }
}
