<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Admin\Admin;
use App\Models\Admin\Role;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Services\Admin\RoleService;
use App\Services\Ers\ServiceOrderService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ServiceOrderController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'sid' => 'string',
            'user_id' => 'integer',
            'project_id' => 'integer',
            'industry_id' => 'integer',
            'enterprise_id' => 'integer',
            'admin_id' => 'integer',
            'status' => 'array',
            'created_at' => 'array',
        ]);

        $adminId = auth('admin')->id();

        // 内容权限判断
        if (!RoleService::checkPermission($adminId, Role::PERMISSION_ERS_ORDER_MANAGER)) {
            $params['admin_id'] = $adminId;
        }

        $builder = ServiceOrder::withTrashed()->with(['user', 'project', 'industry', 'enterprise', 'operator']);
        $this->builderWhere($builder, $params, ['user_id', 'project_id', 'industry_id', 'enterprise_id', 'admin_id', 'status', 'created_at']);
        $this->builderOrderBy($request, $builder);

        if (isset($params['sid']) && $params['sid']) {
            $id = ServiceOrder::decodeSid($params['sid']);
            $builder->where('id', $id);
        }

        return $this->apiPaginate($request, $builder);
    }

    public function show(int $id): array
    {
        $query = ServiceOrder::query()->with(['user', 'project', 'industry', 'enterprise', 'operator']);

        $adminId = auth('admin')->id();

        // 内容权限判断
        if (!RoleService::checkPermission($adminId, Role::PERMISSION_ERS_ORDER_MANAGER)) {
            $query->where('admin_id', $adminId);
        }

        /** @var ServiceOrder $order */
        $order = $query->find($id);

        if (!$order) {
            throw new BadRequestHttpException('工单不存在或已删除');
        }

        $flows = ServiceOrderService::getFlows($order, true, true);

        $order = $order->setHidden([])->toArray();

        $order['flows'] = $flows;

        return $order;
    }

    public function getOperators(): array
    {
        $role = Role::query()->with(['admins', 'admins.admin'])->where('code', 'R_ERS_ORDER_OPERATE')->first();

        $operators = [];

        if ($role) {
            foreach ($role->admins as $admin) {
                $operators[] = $admin->admin;
            }
        }

        return $operators;
    }

    // 指定代办人
    public function updateOperator(Request $request): ServiceOrder
    {
        $params = $request->validate([
            'order_id' => 'required|integer',
            'admin_id' => 'required|integer',
        ]);

        /** @var ServiceOrder $order */
        $order = ServiceOrder::query()->find($params['order_id']);

        if (!$order) {
            throw new BadRequestHttpException('工单不存在');
        }

        /** @var Admin $admin */
        $admin = Admin::query()->find($params['admin_id']);
        if (!$admin) {
            throw new BadRequestHttpException('代办人不存在');
        }

        $order->admin_id = $params['admin_id'];

        $order->save();

        // 发送短信
        ServiceOrderService::sendAssignMessage($admin);

        return $order;
    }

    /**
     * 结束/完成工单
     */
    public function finish(Request $request, int $id)
    {
        $params = $request->validate([
            'force' => 'boolean'
        ]);

        $force = $params['force'] ?? false;

        /** @var ServiceOrder $order */
        $order = ServiceOrder::withTrashed()->findOrFail($id);

        if ($order->status == ServiceOrder::STATUS_FINISH) {
            throw new BadRequestHttpException('该工单已经完成不可继续操作。');
        }

        if (!$force && $order->steps->where('status', '!=', ServiceOrderStep::STATUS_FINISH)->count() > 0) {
            throw new BadRequestHttpException('该工单还有未完成的流程，请先完成所有流程或确认强制完成。');
        }

        $order->status = ServiceOrder::STATUS_FINISH;
        $order->finished_at = now();
        $order->save();

        return $order;
    }

}
