<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Ers\FormLibrary;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\FormProjectInput;
use App\Services\Admin\OperateLogService;
use App\Services\Ers\FormProjectInputService;
use Illuminate\Http\Request;

class FormProjectInputController extends Controller
{
    public function store(Request $request): FormProjectInput
    {
        $typeMap = FormLibrary::getTypeLabels();

        $params = $request->validate([
            'project_form_id' => 'required|integer',
            'form_library_id' => 'required_if:project_form_id,0|integer',
            'type' => 'required|string|in:' . implode(',', array_keys($typeMap)),
            'title' => 'required|string',
            'sort' => 'integer',
            'is_required' => 'boolean',
            'desc' => 'string',
            'options' => 'array',
        ]);

        /** @var FormProjectForm $form */
        $form = FormProjectForm::query()->find($params['project_form_id']);

        $info = FormProjectInputService::create($form, $params);

        OperateLogService::create(auth('admin')->id(),'创建项目表单项', $info->id);

        return $info;
    }

    public function update(Request $request, int $id): FormProjectInput
    {
        $params = $request->validate([
            'title' => 'string',
            'sort' => 'integer',
            'is_required' => 'boolean',
            'desc' => 'string',
            'options' => 'array'
        ]);

        $info = FormProjectInputService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改项目表单项', $info->id);

        return $info;
    }

    public function destroy(int $id): array
    {
        FormProjectInputService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除项目表单项', ['id' => $id]);

        return $this->success();
    }
}
