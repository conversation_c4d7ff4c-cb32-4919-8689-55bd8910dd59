<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Ers\ServiceOrderStep;
use App\Services\Ers\Modules\SolutionDownloadModule;
use App\Services\Ers\ServiceOrderStepService;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * 方案下载确认发送&工单控制器
 */
class SolutionDownloadOrderController extends Controller
{

    /**
     * 确认已发送完整方案
     */
    public function store(int $orderId, int $stepId)
    {
        /** @var ServiceOrderStep $step */
        $step = ServiceOrderStep::query()
            ->where('order_id', $orderId)
            ->where('step_id', $stepId)
            ->where('module', SolutionDownloadModule::configure()->t)
            ->firstOrFail();

        if (!$step->data || $step->status != ServiceOrderStep::STATUS_ADMIN_PENDING) {
            throw new BadRequestHttpException('暂不能确认发送，用户尚未提交邮箱地址。');
        }

        $step->last_admin_handled_at = now();
        $step->finished_by = ServiceOrderStep::FINISHED_BY_ADMIN;
        $step->status = ServiceOrderStep::STATUS_FINISH;
        $step->save();

        ServiceOrderStepService::forward($step->order, $step);

        return $step;
    }

}
