<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Ers\EnterpriseCategory;
use App\Services\Admin\OperateLogService;
use App\Services\Ers\EnterpriseCategoryService;
use Illuminate\Http\Request;

class EnterpriseController extends Controller
{
    public function search(Request $request): array
    {
        $params = $request->validate([
            'industry_id' => 'required|integer',
        ]);

        return EnterpriseCategory::query()->where('industry_id', $params['industry_id'])->orderBy('sort')->get()->toArray();
    }

    public function store(Request $request): EnterpriseCategory
    {
        $params = $request->validate([
            'industry_id' => 'required|integer',
            'name' => 'required|string|min:2'
        ]);

        $info = EnterpriseCategoryService::create($params);

        OperateLogService::create(auth('admin')->id(),'创建企业类别', $info->id);

        return $info;
    }

    public function update(Request $request, int $id): EnterpriseCategory
    {
        $params = $request->validate([
            'name' => 'string|min:2'
        ]);

        $info = EnterpriseCategoryService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改企业类别', $info->id);

        return $info;
    }

    public function move(Request $request): array
    {
        $params = $request->validate([
            'from_id' => 'required|integer',
            'to_id' => 'required|integer',
        ]);

        EnterpriseCategoryService::move($params['from_id'], $params['to_id']);

        return $this->success();
    }

    public function destroy(int $id): array
    {
        EnterpriseCategoryService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除企业类别', ['id' => $id]);

        return $this->success();
    }
}
