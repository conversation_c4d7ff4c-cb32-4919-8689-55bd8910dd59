<?php

namespace App\Http\Controllers\Admin\Stat;

use App\Http\Controllers\Admin\Controller;
use App\Models\Admin\Role;
use App\Models\Admin\RoleMenu;
use App\Models\Admin\UserRole;
use App\Models\Attachment\AttachmentFile;
use App\Models\Stat\DailyOverview;
use App\Services\Admin\RoleService;
use App\Services\Stat\DailyOverviewService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DailyOverviewController extends Controller
{
    public function permission(): array
    {
        $check = RoleService::checkPermission(auth('admin')->id(), Role::PERMISSION_MANAGE_PROMOTER);

        return [
            'permission' => $check ? 'root' : 'guest'
        ];
    }

    public function permissionStat(): array
    {
        $adminId = auth('admin')->id();
        $role = UserRole::query()->where('admin_id', $adminId)->get();
        $isPermission = false;
        if ($role->count()) {
            $roleIds =  $role->pluck('role_id');
            $isPermission = RoleMenu::query()->whereIn('role_id', $roleIds)->where('menu_id', 59)->exists();
        }
        return ['permission' => $isPermission];
    }

    public function getSubtotalData(): array
    {
        $today = DailyOverviewService::getSubtotalData(Carbon::now()->toDateString());
        $yesterday = DailyOverviewService::getSubtotalData(Carbon::yesterday()->toDateString());
        $subtotal = DailyOverviewService::getSubtotalData();
        $attachment = AttachmentFile::query()->selectRaw("count(*) num,sum(filesize) size")->first();

        if ($attachment) {
            $attachment['size'] = intval($attachment['size']);
        } else {
            $attachment['num'] = 0;
            $attachment['size'] = 0;
        }

        return [
            'today' => $today,
            'yesterday' => $yesterday,
            'subtotal' => $subtotal,
            'attachment' => $attachment,
        ];
    }

    public function getDateList(Request $request): array
    {
        $params = $request->validate([
            'date_range' => 'array|size:2'
        ]);

        if (!isset($params['date_range'])) {
            $endDate = Carbon::yesterday()->toDateString();
            $startDate = Carbon::parse($endDate)->subDays(14)->toDateString();
        } else {
            $startDate = $params['date_range'][0];
            $endDate = $params['date_range'][1];
        }

        $dateRange = DailyOverviewService::getDateRange($startDate, $endDate);

        return DailyOverview::query()->whereBetween('date', $dateRange)->orderBy('date')->get()->toArray();
    }
}
