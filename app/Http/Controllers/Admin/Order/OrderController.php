<?php

namespace App\Http\Controllers\Admin\Order;

use App\Core\Enums\BusinessType;
use App\Http\Controllers\Admin\Controller;
use App\Models\Order\Order;
use App\Models\Order\Payment;
use App\Models\Org;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'user_id' => 'integer',
            'order_no' => 'string',
            'transaction_no' => 'string',
            'business_type' => 'string',
            'business_id' => 'integer',
            'status' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = Order::query()->with(['user', 'payment']);
        // 排除机构报名订单
        $builder->whereNotIn('business_type', [BusinessType::Enroll]);
        $this->builderWhere($builder, $params, ['id', 'user_id', 'order_no', 'business_type', 'business_id', 'status', 'created_at']);
        $this->builderOrderBy($request, $builder);

        if (isset($params['transaction_no']) && $params['transaction_no']) {
            /** @var Payment $payment */
            $payment = Payment::query()->where('transaction_no', $params['transaction_no'])->first();
            if ($payment) {
                $paymentId = $payment->id;
            } else {
                $paymentId = -1;
            }

            $builder->where('payment_id', $paymentId);
        }

        return $this->apiPaginate($request, $builder);
    }
}
