<?php

namespace App\Http\Controllers\Admin\System;

use App\Core\Enums\BusinessType;
use App\Http\Controllers\Admin\Controller;
use App\Models\SettingBooth;
use App\Services\Admin\OperateLogService;
use App\Services\SettingBoothService;
use Illuminate\Http\Request;

class SettingBoothController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'type' => 'integer',
            'enable' => 'integer',
        ]);

        $builder = SettingBooth::query();

        $this->builderWhere($builder, $params, ['type', 'enable']);
        $this->builderOrderBy($request, $builder, 'sort desc, id asc');

        return $this->apiPaginate($request, $builder);
    }

    public function store(Request $request): SettingBooth
    {
        $params = $request->validate([
            'name' => 'required|string',
            'type' => 'required|integer',
            'image' => 'string',
            'url' => 'string',
            'sort' => 'integer',
            'enable' => 'integer',
        ]);

        $booth = SettingBoothService::create($params['name'], intval($params['type']), $params);

        OperateLogService::create(auth('admin')->id(), '添加展位', ['id' => $booth->id, ...$params]);

        return $booth;
    }

    public function update(Request $request, int $id): SettingBooth
    {
        $params = $request->validate([
            'name' => 'string',
            'image' => 'string',
            'url' => 'string',
            'sort' => 'integer',
            'enable' => 'integer',
        ]);

        $booth = SettingBoothService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改展位', ['id' => $booth->id, ...$params]);

        return $booth;
    }

    public function destroy(int $id): array
    {
        SettingBoothService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除展位', $id);

        return $this->success();
    }
}
