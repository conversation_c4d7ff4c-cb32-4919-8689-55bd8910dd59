<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Admin\Controller;
use App\Models\Attachment\AttachmentRelation;
use Illuminate\Http\Request;

class AttachmentRelationController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'file_id' => 'integer',
        ]);

        $builder = AttachmentRelation::query();

        $this->builderWhere($builder, $params, ['file_id']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }
}
