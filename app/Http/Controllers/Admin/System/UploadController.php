<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Admin\Controller;
use App\Services\Common\AttachmentService;
use Illuminate\Http\Request;

class UploadController extends Controller
{
    public function config(Request $request): array
    {
        $params = $request->validate([
            'file_types' => 'array',
            'storage' => 'in:priv,pub,local',
            'prefix' => 'alpha_dash',
            'max_size' => 'integer'
        ]);

        $fileTypes = empty($params['file_types']) ? '*' : $params['file_types'];
        $storage = $params['storage'] ?? 'pub';
        $prefix = $params['prefix'] ?? 0;
        $maxSize = $params['max_size'] ?? 0;

        $diskName = config("heguibao.storage.$storage");

        return AttachmentService::getUploadForm($prefix, $fileTypes, $maxSize, $diskName);
    }
}
