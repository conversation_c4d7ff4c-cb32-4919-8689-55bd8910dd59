<?php

namespace App\Http\Controllers\Admin\Admin;

use App\Http\Controllers\Admin\Controller;
use App\Libs\Utils\Helpers;
use App\Models\Admin\Menu;
use App\Services\Admin\MenuService;
use App\Services\Admin\OperateLogService;
use Illuminate\Http\Request;

class MenuController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'status' => 'integer',
        ]);

        $menus = MenuService::getMenus($params['status'] ?? null);

        return Helpers::dataToTree($menus);
    }

    public function store(Request $request): Menu
    {
        $params = $request->validate([
            'parent_id' => 'required|integer',
            'type' => 'required|integer',
            'name' => 'required|string|min:2',
            'icon' => 'string',
            'method' => 'string',
            'route_name' => 'string',
            'route_path' => 'string',
            'component' => 'string',
            'sort' => 'integer',
            'status' => 'integer',
        ]);

        $menu = MenuService::create($params);

        OperateLogService::create(auth('admin')->id(),'创建菜单', $menu->id);

        return $menu;
    }

    public function update(Request $request, int $id): Menu
    {
        $params = $request->validate([
            'parent_id' => 'integer',
            'type' => 'integer',
            'name' => 'string|min:2',
            'icon' => 'string',
            'method' => 'string',
            'route_name' => 'string',
            'route_path' => 'string',
            'component' => 'string',
            'sort' => 'integer',
            'status' => 'integer',
        ]);

        $menu = MenuService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改菜单', $menu->id);

        return $menu;
    }

    public function destroy(int $id): array
    {
        MenuService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除菜单', ['id' => $id]);

        return $this->success();
    }
}
