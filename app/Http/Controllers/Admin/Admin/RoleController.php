<?php

namespace App\Http\Controllers\Admin\Admin;

use App\Http\Controllers\Admin\Controller;
use App\Models\Admin\Role;
use App\Services\Admin\OperateLogService;
use App\Services\Admin\RoleService;
use Illuminate\Http\Request;

class RoleController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'code' => 'string',
            'status' => 'integer',
        ]);

        $builder = Role::query()->with(['menus']);
        $this->builderWhere($builder, $params, ['code', 'status']);
        $this->builderOrderBy($request, $builder, 'id asc');

        return $this->apiPaginate($request, $builder);
    }

    public function search(Request $request): array
    {
        $params = $request->validate([
            'status' => 'integer',
        ]);

        $builder = Role::query();
        $this->builderWhere($builder, $params, ['status']);

        return $builder->get()->toArray();
    }

    public function store(Request $request): Role
    {
        $params = $request->validate([
            'name' => 'required|string|min:3',
            'code' => 'required|string',
            'desc' => 'string',
            'status' => 'integer',
            'permissions' => 'array',
            'menu_ids' => 'required|array|min:1',
            'indeterminate_ids' => 'array',
            'checked_ids' => 'array'
        ]);

        $role = RoleService::create($params);

        OperateLogService::create(auth('admin')->id(),'创建角色', $role->id);

        return $role;
    }

    public function update(Request $request, int $id): Role
    {
        $params = $request->validate([
            'name' => 'string|min:3',
            'code' => 'string',
            'desc' => 'string|min:2',
            'status' => 'integer',
            'permissions' => 'array',
            'indeterminate_ids' => 'array',
            'checked_ids' => 'array'
        ]);

        $role = RoleService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改角色', $role->id);

        return $role;
    }

    public function destroy(int $id): array
    {
        RoleService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除角色', ['id' => $id]);

        return $this->success();
    }
}
