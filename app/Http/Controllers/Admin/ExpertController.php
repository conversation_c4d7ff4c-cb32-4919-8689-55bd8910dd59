<?php

namespace App\Http\Controllers\Admin;

use App\Models\Expert;
use App\Services\Admin\OperateLogService;
use App\Services\ExpertService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ExpertController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'user_id' => 'integer',
            'fields' => 'string',
            'phone' => 'string',
            'status' => 'integer',
            'is_visible' => 'integer',
            'created_at' => 'array',
            'name' => 'string',
            'education' => 'string',
            'occupation' => 'string',
            'industry' => 'string',
            'services' => 'string',
            'residence' => 'string',
            'gender' => 'integer',
            'work_year' => 'integer',
            'keyword' => 'string',
        ]);

        $builder = Expert::query()->with(['user']);
        $this->builderWhere($builder, $params, ['id', 'user_id', 'phone', 'status', 'is_visible', 'created_at']);
        $this->builderOrderBy($request, $builder, 'sort desc, id desc');

        if (!empty($params['fields'])) {
            $builder->whereJsonContains('fields', $params['fields']);
        }

        if (!empty($params['name'])) {
            $builder->where('name', 'like', '%' . $params['name'] . '%');
        }

        if (!empty($params['education'])) {
            $builder->where('education', 'like', '%' . $params['education'] . '%');
        }

        if (!empty($params['occupation'])) {
            $builder->where('occupation', 'like', '%' . $params['occupation'] . '%');
        }

        if (!empty($params['industry'])) {
            $builder->where('industry', 'like', '%' . $params['industry'] . '%');
        }

        if (!empty($params['services'])) {
            $builder->whereJsonContains('services', $params['services']);
        }

        if (!empty($params['gender'])) {
            $builder->where('gender', $params['gender']);
        }

        if (!empty($params['work_year'])) {
            $builder->where('work_year', $params['work_year']);
        }
        if (!empty($params['residence'])) {
            $builder->where('residence', 'like', '%' . $params['residence'] . '%');
        }

        if (!empty($params['keyword'])) {
            $keywords = preg_split('/\s+/', trim($params['keyword']));
            // 严格匹配，加 "+"，如：高空作业 电梯，必须同时包含
             $booleanQuery = implode(' ', array_map(fn($word) => '+' . $word, $keywords));
            // 宽松匹配，不加 "+"， 如：高空作业 电梯，任意包含即可
//            $booleanQuery = implode(' ', $keywords);

            // 对字段做全文关键词搜索
            $builder->whereRaw("
            MATCH(
              name, residence, major, education, occupation, industry,safety_work_experience, course_scopes, typical_cases,
              serve_customers, teaching_styles, extra_text
            ) AGAINST (? IN BOOLEAN MODE)
        ", [$booleanQuery]);
        }

        return $this->apiPaginate($request, $builder);
    }

    public function store(Request $request): Expert
    {
        $params = $request->validate([
            'user_id' => 'integer',
            'name' => 'required|string|max:10',
            'phone' => 'required|string|max:11',
            'gender' => 'required|in:1,2',
            'residence' => 'required|string|max:128',
            'major' => 'required|string|max:32',
            'safety_work_experience' => 'required|string',
            'education' => 'required|string',
            'occupation' => 'required|string|max:64',
            'industry' => 'required|string|max:30',
            'work_year' => 'required|integer|min:1',
            'fields' => 'required|array',
            'services' => 'required|array',
            'photo' => 'required|string',
            'certs_add' => 'required|array',
            'course_scopes' => 'required|string',
            'typical_cases' => 'required|string',
            'scene_photos_add' => 'required|array',
            'serve_customers' => 'string',
            'teaching_styles' => 'string',
            'remark' => 'string|max:1000'
        ]);

        $expert = ExpertService::store($params, $params['user_id'] ?? 0);

        OperateLogService::create(auth('admin')->id(), '专家信息创建', ['id' => $expert->id, ...$params]);

        return $expert->setHidden([]);
    }

    public function update(Request $request, int $id): Expert
    {
        $params = $request->validate([
            'user_id' => 'integer',
            'name' => 'required|string|max:10',
            'phone' => 'required|string|max:11',
            'gender' => 'required|in:1,2',
            'residence' => 'required|string|max:128',
            'major' => 'required|string|max:32',
            'safety_work_experience' => 'required|string',
            'education' => 'required|string',
            'occupation' => 'required|string|max:64',
            'industry' => 'required|string|max:30',
            'work_year' => 'required|integer|min:1',
            'fields' => 'required|array',
            'services' => 'required|array',
            'photo' => 'required|string',
            'certs_add' => 'array',
            'is_visible' => 'integer',
            'sort' => 'integer',
            'certs_remove' => 'array',
            'course_scopes' => 'required|string',
            'typical_cases' => 'required|string',
            'scene_photos_add' => 'array',
            'scene_photos_remove' => 'array',
            'serve_customers' => 'string',
            'teaching_styles' => 'string',
            'remark' => 'string|max:1000'
        ], [
            'residence.required' => '居住地不能为空',
        ]);

        /** @var Expert $expert */
        $expert = Expert::query()->where('id', $id)->first();
        if (!$expert) {
            throw new NotFoundHttpException("专家数据不存在");
        }

        $expert = ExpertService::update($params, $expert);

        OperateLogService::create(auth('admin')->id(), '专家信息修改', ['id' => $expert->id, ...$params]);

        return $expert->setHidden([]);
    }

    public function config()
    {
        return config('expert');
    }

    public function reject(Request $request, int $id): Expert
    {
        $params = $request->validate([
            'reason' => 'required|string'
        ]);

        $expert = ExpertService::reject($id, $params['reason']);

        OperateLogService::create(auth('admin')->id(), '专家信息审核未通过', ['id' => $expert->id, ...$params]);

        return $expert->setHidden([]);
    }

    public function resolve(int $id): Expert
    {
        $expert = ExpertService::resolve($id);

        OperateLogService::create(auth('admin')->id(), '专家信息审核已通过', ['id' => $expert->id]);

        return $expert->setHidden([]);
    }
}
