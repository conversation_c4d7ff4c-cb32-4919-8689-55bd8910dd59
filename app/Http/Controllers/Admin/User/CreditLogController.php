<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Admin\Controller;
use App\Models\User\UserCreditLog;
use Illuminate\Http\Request;

class CreditLogController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'user_id' => 'integer',
            'type' => 'string',
            'business_type' => 'string',
            'business_id' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = UserCreditLog::query()->with(['user']);
        $this->builderWhere($builder, $params, ['user_id', 'business_type', 'business_id', 'type', 'created_at']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }
}
