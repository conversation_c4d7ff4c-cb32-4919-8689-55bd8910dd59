<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Admin\Controller;
use App\Models\Cms\Content;
use App\Models\User\UserOwnContent;
use App\Services\Admin\OperateLogService;
use App\Services\User\OwnContentService;
use Illuminate\Http\Request;

class OwnContentController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'user_id' => 'integer',
            'classify' => 'string',
            'keyword' => 'string',
        ]);

        if (isset($params['keyword']) && $params['keyword']) {
            if (preg_match('/^[1-9]\d{0,8}$/', $params['keyword'])) {
                $relationId = $params['keyword'];
            } else {
                $relationId = Content::query()->where('title', 'like', "%{$params['keyword']}%")->pluck('id')->toArray();

                if (empty($relationId)) {
                    $relationId = 0;
                }
            }

            $params['content_id'] = $relationId;
        }

        $builder = UserOwnContent::withTrashed()->with(['content']);
        $this->builderWhere($builder, $params, ['id', 'user_id', 'content_id', 'classify']);
        $this->builderOrderBy($request, $builder);

        $data = $this->apiPaginate($request, $builder);

        foreach ($data['records'] as $item) {
            $item->status = 1;

            if ($item->deleted_at) {
                $item->status = 0;
            } elseif (now()->gte($item->expired_at)) {
                $item->status = 2;
            }
        }

        return $data;
    }

    public function destroy(int $id): array
    {
        OwnContentService::remove($id);

        OperateLogService::create(auth('admin')->id(),'删除用户课程', ['id' => $id]);

        return $this->success();
    }
}
