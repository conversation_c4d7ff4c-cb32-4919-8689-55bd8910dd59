<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Admin\Controller;
use App\Models\Train\Topic;
use App\Models\User\UserOwnTopic;
use App\Services\Admin\OperateLogService;
use App\Services\User\OwnTopicService;
use Illuminate\Http\Request;

class OwnTopicController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'user_id' => 'integer',
            'keyword' => 'string',
        ]);

        if (isset($params['keyword']) && $params['keyword']) {
            if (preg_match('/^[1-9]\d{0,8}$/', $params['keyword'])) {
                $relationId = $params['keyword'];
            } else {
                $relationId = Topic::query()->where('name', 'like', "%{$params['keyword']}%")->pluck('id')->toArray();

                if (empty($relationId)) {
                    $relationId = 0;
                }
            }

            $params['topic_id'] = $relationId;
        }

        $builder = UserOwnTopic::withTrashed()->with(['topic']);
        $this->builderWhere($builder, $params, ['id', 'user_id', 'topic_id']);
        $this->builderOrderBy($request, $builder);

        $data = $this->apiPaginate($request, $builder);

        foreach ($data['records'] as $item) {
            $item->status = 1;

            if ($item->deleted_at) {
                $item->status = 0;
            } elseif (now()->gte($item->expired_at)) {
                $item->status = 2;
            }
        }

        return $data;
    }

    public function destroy(int $id): array
    {
        OwnTopicService::remove($id);

        OperateLogService::create(auth('admin')->id(),'删除用户题库', ['id' => $id]);

        return $this->success();
    }
}
