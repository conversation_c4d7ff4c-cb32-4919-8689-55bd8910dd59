<?php

namespace App\Http\Controllers\Admin\Chat;

use App\Http\Controllers\Admin\Controller;
use App\Models\Chat\ChatSession;
use Illuminate\Http\Request;

class ChatSessionController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'user_id' => 'integer',
            'title' => 'string',
            'created_at' => 'array',
        ]);

        $builder = ChatSession::query()->with(['user'])->withCount(['messages']);
        $this->builderWhere($builder, $params, ['user_id', 'created_at']);
        $this->builderOrderBy($request, $builder);

        if (!empty($params['title'])) {
            $builder->where('title', 'like', "%{$params['title']}%");
        }

        return $this->apiPaginate($request, $builder);
    }
}
