<?php

namespace App\Http\Controllers\Admin\Chat;

use App\Http\Controllers\Admin\Controller;
use App\Models\Chat\ChatMessage;
use Illuminate\Http\Request;

class ChatMessageController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'session_id' => 'integer',
        ]);

        $builder = ChatMessage::query();
        $this->builderWhere($builder, $params, ['session_id']);
        $this->builderOrderBy($request, $builder, 'id asc');

        return $this->apiPaginate($request, $builder);
    }
}
