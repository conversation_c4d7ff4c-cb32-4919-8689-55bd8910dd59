<?php

namespace App\Http\Controllers\Admin;

use App\Models\Org\BalanceRecord;
use App\Models\Org;
use App\Models\Org\Enrollment;
use App\Models\Org\EnrollmentForm;
use App\Models\User;
use App\Services\OrgService;
use App\Services\Org\BalanceService;
use Hidehalo\Nanoid\Client;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OrgController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'name' => 'string',
            'contact' => 'string',
            'area_code' => 'integer',
            'verified_status' => 'string',
        ]);

        $builder = Org::query()->with('mainAdmin');
        $this->builderWhere($builder, $params, ['id', 'contact', 'verified_status', 'created_at']);

        if (!empty($params['name'])) {
            $builder->where('name', 'like', "%{$params['name']}%");
        }

        if (!empty($params['area_code'])) {
            $builder->where('area_code', 'like', "{$params['area_code']}%");
        }

        if (!empty($params['contact'])) {
            $builder->where('contact', 'like', "{$params['contact']}%");
        }

        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }

    public function store(Request $request)
    {
        $params = $request->validate([
            'name' => 'required|string|max:64',
            'alias' => 'required|string|max:64',
            'contact' => 'required|string|max:20',
            'area_code' => 'required|integer|min:100000',
            'area_text' => 'required|array',
            'need_photo' => 'required|boolean',
            'enable_enroll' => 'required|boolean',
            'merchant_id' => 'required_if:enable_enroll,true|string',
            'merchant_name' => 'required_if:enable_enroll,true|string',
            'merchant_enable' => 'required_if:enable_enroll,true|boolean',
            'invoice_enabled' => 'required_if:enable_enroll,true|boolean',
        ]);

        // 默认通过，todo 需要验证的时候，删除代码
        $params['verified_status'] = Org::VERIFIED_STATUS_VERIFIED;

        $org = OrgService::create($params);

        $form = new EnrollmentForm();
        $form->org_id = $org->id;
        $form->title = '报名';
        $form->fields = [
            [
                'id' => (new Client())->generateId(),
                'name' => '证件照',
                'type' => EnrollmentForm::TYPE_PHOTO,
                'required' => true,
            ],
            [
                'id' => (new Client())->generateId(),
                'name' => '工作单位',
                'type' => EnrollmentForm::TYPE_WORK_UNIT,
                'required' => true,
            ]
        ];
        $form->save();

        return $org->makeVisible(['id']);
    }

    public function update(Request $request, int $id): Org
    {
        $params = $request->validate([
            'name' => 'required|string|max:64',
            'alias' => 'required|string|max:64',
            'contact' => 'required|string|max:20',
            'area_code' => 'required|integer|min:100000',
            'area_text' => 'required|array',
            'need_photo' => 'required|boolean',
            'enable_enroll' => 'required|boolean',
            'merchant_id' => 'required_if:enable_enroll,true|string',
            'merchant_name' => 'required_if:enable_enroll,true|string',
            'merchant_enable' => 'required_if:enable_enroll,true|boolean',
            'invoice_enabled' => 'required_if:enable_enroll,true|boolean',
        ]);

        /** @var Org $org */
        $org = Org::query()->find($id);

        if (!$org) {
            throw new NotFoundHttpException("该机构不存在");
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $org->getFillable()) && !in_array($key, ['logo', 'business_license', 'service_qrcode', 'official_seal_image', 'enroll_bg_img'])) {
                $org->{$key} = $val;
            }
        }
        $org->save();

        return $org;
    }

    // 暂时不需要
    public function verify(Request $request, int $id): Org
    {
        $statusVerified = Org::VERIFIED_STATUS_VERIFIED;
        $statusRejected = Org::VERIFIED_STATUS_REJECTED;

        $params = $request->validate([
            'verified_status' => "required|in:{$statusVerified},{$statusRejected}",
        ]);

        /** @var Org $org */
        $org = Org::query()->find($id);
        if (!$org) {
            throw new NotFoundHttpException("该机构不存在");
        }

        $org->verified_status = $params['verified_status'];
        $org->verified_at = now();
        $org->save();

        return $org;
    }

    public function destroy(int $id): array
    {
        /** @var Org $org */
        $org = Org::query()->find($id);

        if (!$org) {
            throw new NotFoundHttpException("该机构不存在或已删除");
        }

        // 检查是否有关联数据
        $adminExists = Org\Admin\Admin::query()->where('org_id', $id)->exists();

        if ($adminExists) {
            throw new BadRequestHttpException("该机构下还有绑定的管理员，请先解绑");
        }

        $userExists = User::query()->where('org_id', $id)->exists();

        if ($userExists) {
            throw new BadRequestHttpException("该机构下还有绑定的用户，请先解绑");
        }

        $org->delete();

        return $this->success();
    }

    public function search(Request $request): array
    {
        $params = $request->validate([
            'keyword' => 'required|string'
        ]);

        $query = Org::query();

        if (preg_match('/^[1-9]\d*$/', $params['keyword'])) {
            $query->where('id', $params['keyword']);
        } else {
            $query->where('name', 'like', "{$params['keyword']}%");
        }

        return $query->limit(20)
            ->get(['id', 'name', 'alias', 'area_code', 'area_text', 'balance', 'created_at'])
            ->setHidden([])
            ->toArray();
    }
}
