<?php
/**
 * OrgAdminController.php class file.
 *
 * <AUTHOR>
 * @time 2025/2/18 15:17
 * @copyright 2025 pp.cc All Right Reserved
 */

namespace App\Http\Controllers\Admin\Org;

use App\Http\Controllers\Admin\Controller;
use App\Models\Org\Admin\Admin;
use App\Services\Org\Admin\AdminService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\Password;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class AdminController extends Controller
{
    public function store(Request $request, int $oid)
    {
        $params = $request->validate([
            'username' => 'required|string',
            // 确认密码字段格式 {field}_confirmation
            'password' => ['required', 'confirmed', Password::min(6)]
        ]);

        $orgAdmin = Admin::query()
            ->where('username', $params['username'])
            ->exists();

        if ($orgAdmin) {
            throw new BadRequestHttpException("用户名已存在，请重新输入");
        }

        AdminService::create($oid, $params['username'], $params['password'], ['is_main' => 1, 'role_ids' => [1], 'status' => Admin::STATUS_ENABLED]);

        return $this->success();
    }


    public function updatePassword(Request $request, int $oid, int $id)
    {
        $params = $request->validate([
            'password' => ['required', 'confirmed', Password::min(6)],
        ]);

        AdminService::updatePassword($id, $params['password']);

        return $this->success();
    }
}
