<?php

namespace App\Http\Controllers\Admin\Org;

use App\Http\Controllers\Admin\Controller;
use App\Models\Cms\Content;
use App\Services\Org\CourseService;
use Illuminate\Http\Request;
use App\Models\Org\Course;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * 机构课程管理
 */
class CourseController extends Controller
{
    /**
     * 获取机构课程列表
     */
    public function index(Request $request, int $oid)
    {
        $params = $request->validate([
            'keywords' => 'string',
        ]);

        $contentTable = (new Content)->getTable();
        $courseTable = (new Course)->getTable();
        $builder = Course::query()
            ->where('org_id', $oid)
            ->leftJoin($contentTable, $courseTable . '.course_id', '=', $contentTable . '.id')
            ->select([
                $courseTable . '.*',
                $contentTable . '.title as course_name'
            ]);

        $this->builderWhere($builder, $params);

        if (!empty($params['keywords'])) {
            if (is_numeric($params['keywords'])) {
                $builder->where($courseTable . '.course_id', $params['keywords']);
            } else {
                $builder->where($contentTable . '.name', 'like', "%{$params['keywords']}%");
            }
        }

        $this->builderOrderBy($request, $builder);
        $data = $this->apiPaginate($request, $builder);
        $data['selected_course_ids'] = Course::query()->where('org_id', $oid)->pluck('course_id')->toArray();

        return $data;
    }

    /**
     * 更新机构课程
     */
    public function update(Request $request, int $oid, int $courseId)
    {
        $params = $request->validate([
            'price' => 'required|numeric',
        ]);

        $course = $this->getCourse($oid, $courseId);
        $course->price_original = $params['price'];
        $course->save();

        return $this->success();
    }

    /**
     * 删除机构课程
     */
    public function destroy(int $oid, int $courseId)
    {
        $course = $this->getCourse($oid, $courseId);
        // todo 判断是否有人学习了
        $course->delete();

        return $this->success();
    }

    /**
     * 添加机构课程
     */
    public function store(Request $request, int $oid)
    {
        $params = $request->validate([
            'courses' => 'required|array',
            'courses.*.id' => 'required|integer',
            'courses.*.price' => 'required|numeric',
        ]);

        $courseIds = array_column($params['courses'], 'id');
        $coursesData = collect($params['courses'])->keyBy('id')->map(function($course) {
            return [
                'price' => $course['price'],
            ];
        })->all();

        // 验证课程是否已添加
        $isAdded = Course::query()->where('org_id', $oid)->whereIn('course_id', $courseIds)->exists();

        if ($isAdded) {
            throw new BadRequestHttpException('添加课程中存在已添加的课程，请检查后重试');
        }

        CourseService::add($oid, $courseIds, $coursesData);
        return $this->success();
    }

    /**
     * 获取机构课程
     *
     * @param int $oid
     * @param int $courseId
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|Course
     */
    protected function getCourse(int $oid, int $courseId)
    {
        $course = Course::query()
            ->where('org_id', $oid)
            ->where('course_id', $courseId)
            ->first();

        if (!$course) {
            throw new NotFoundHttpException('机构课程不存在或已删除');
        }

        return $course;
    }
}
