<?php
/**
 * BalanceController.php class file.
 *
 * <AUTHOR>
 * @time 2025/2/26 14:37
 * @copyright 2025 pp.cc All Right Reserved
 */

namespace App\Http\Controllers\Admin\Org;

use App\Http\Controllers\Admin\Controller;
use App\Models\Org;
use App\Models\Org\BalanceRecord;
use App\Services\Org\BalanceService;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Illuminate\Http\Request;

class BalanceController extends Controller
{
    public function index(Request $request, int $oid)
    {
        $builder = BalanceRecord::query()->where('org_id', $oid)->orderBy('id', 'desc');

        return $this->apiPaginate($request, $builder);
    }

    public function store(Request $request, int $oid)
    {
        $typeIncome = BalanceRecord::TYPE_INCOME;
        $typeExpense = BalanceRecord::TYPE_EXPENSE;

        $params = $request->validate([
            'type' => "string|in:{$typeIncome},{$typeExpense}",
            'amount' => 'required|integer|min:1',
            'remark' => 'required|string'
        ]);

        $org = Org::query()->find($oid);

        if (!$org) {
            throw new NotFoundHttpException("该机构不存在或已删除");
        }

        BalanceService::update($oid, $params['amount'], $params['type'], 0, $params['remark']);

        return $this->success();
    }
}
