<?php

namespace App\Http\Controllers\Admin\Qa;

use App\Http\Controllers\Admin\Controller;
use App\Models\Qa\Answer;
use Illuminate\Http\Request;

class AnswerController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'user_id' => 'integer',
            'question_id' => 'integer',
            'status' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = Answer::query()->with(['user', 'question']);
        $this->builderWhere($builder, $params, ['id', 'user_id', 'question_id', 'status', 'created_at']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }

    public function destroy(int $id): array
    {
        Answer::query()->where('id', $id)->delete();

        return $this->success();
    }

    public function batchDestroy(Request $request): array
    {
        $params = $request->validate([
            'ids' => 'required|array',
        ]);

        Answer::query()->whereIn('id', $params['ids'])->delete();

        return $this->success();
    }
}
