<?php

namespace App\Http\Controllers\Admin\Qa;

use App\Http\Controllers\Admin\Controller;
use App\Models\Qa\Answer;
use App\Models\Qa\Question;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class QuestionController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'user_id' => 'integer',
            'title' => 'string',
            'status' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = Question::query()->with(['user']);
        $this->builderWhere($builder, $params, ['id', 'user_id', 'status', 'created_at']);
        $this->builderOrderBy($request, $builder);

        if (!empty($params['title'])) {
            $builder->where('title', 'like', "%{$params['title']}%");
        }

        return $this->apiPaginate($request, $builder);
    }

    public function answers(int $id): array
    {
        return Answer::query()->where('question_id', $id)->get()->setHidden([])->toArray();
    }

    public function recommend(Request $request, int $id): Question
    {
        $params = $request->validate([
            'recommend' => 'required|integer',
        ]);

        /** @var Question $question */
        $question = Question::query()->find($id);

        if (!$question) {
            throw new BadRequestHttpException('问题不存在');
        }

        $question->recommend_at = $params['recommend'] ? Carbon::now() : null;

        $question->save();

        return $question;
    }

    public function destroy(int $id): array
    {

        Answer::query()
            ->where('question_id', $id)
            ->delete();
        Question::query()->where('id', $id)->delete();

        return $this->success();
    }

    public function batchDestroy(Request $request): array
    {
        $params = $request->validate([
            'ids' => 'required|array',
        ]);

        Question::query()->whereIn('id', $params['ids'])->delete();

        return $this->success();
    }
}
