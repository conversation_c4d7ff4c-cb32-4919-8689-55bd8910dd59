<?php

namespace App\Http\Controllers\Admin;

use App\Models\Admin\Admin;
use App\Models\Admin\Role;
use App\Services\Admin\AdminService;
use App\Services\Admin\PermissionService;
use App\Services\Admin\RoleService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{
    public function __construct(Request $request)
    {
        parent::__construct($request);

        $this->middleware('auth.admin', ['except' => ['login']]);
    }

    public function login(Request $request): array
    {
        $params = $request->validate([
            'username' => 'required|string|min:3',
            'password' => 'required|string|min:6'
        ]);

        $admin = AdminService::login($params['username'], $params['password'], $this->ip);

        $token = auth('admin')->login($admin);

        return $this->respondWithToken($token);
    }

    public function logout(): array
    {
        JWTAuth::parseToken()->invalidate();

        return $this->success();
    }

    public function me(): array
    {
        /** @var Admin $admin */
        $admin = auth('admin')->user();

        AdminService::update($admin->id, [
            'last_active_at' => Carbon::now(),
            'last_active_ip' => $this->ip
        ]);

        $roles = [];
        foreach ($admin->roles as $item) {
            if ($item->role) {
                $roles[] = $item->role->code;
            }
        }

        // 权限 R_SUPER, R_ADMIN, R_USER
        if (empty($roles)) {
            $roles = $admin->is_admin ? ['R_SUPER'] : ['R_USER'];
        }

        return [
            'userId' => $admin->id,
            'userName' => $admin->username,
            'roles' => $roles,
            'permissions' => [
                'cms' => RoleService::checkPermission($admin->id, Role::PERMISSION_MANAGE_EDITOR),
                'ers_order' => RoleService::checkPermission($admin->id, Role::PERMISSION_ERS_ORDER_MANAGER)
            ],
            'menus' => PermissionService::getUserMenus($admin->id)
        ];
    }

    public function menus(): array
    {
        return PermissionService::getUserMenus(auth('admin')->id());
    }

    protected function respondWithToken(string $token): array
    {
        return [
            'token' => $token,
            'refreshToken' => $token,
            'ttl' => JWTAuth::factory()->getTTL() * 60
        ];
    }
}
