<?php

namespace App\Http\Controllers\Api\Inspect;

use App\Http\Controllers\Api\Controller;
use App\Models\Inspect\DeviceItem;
use App\Services\Inspect\InspectDeviceItemService;
use Illuminate\Http\Request;

class DeviceItemsController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'device_id' => 'required|integer'
        ]);

        return DeviceItem::query()
            ->where('device_id', $params['device_id'])
            ->orderBy('id', 'desc')
            ->select(['id', 'name'])
            ->get()
            ->toArray();
    }

    public function store(Request $request)
    {
        $params = $request->validate([
            'device_id' => 'required|integer',
            'names' => 'required|array',
        ]);
        
        InspectDeviceItemService::batchAssign($params['device_id'], $params['names']);
    }
}