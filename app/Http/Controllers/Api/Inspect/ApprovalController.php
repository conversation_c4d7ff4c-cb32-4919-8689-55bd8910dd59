<?php

namespace App\Http\Controllers\Api\Inspect;

use App\Http\Controllers\Api\Controller;
use App\Services\Inspect\InspectApprovalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ApprovalController extends Controller
{
    /**
     * 获取审批列表
     * @return array|\Illuminate\Http\JsonResponse
     */
    public function approvalList()
    {
        try {
            $userID = Auth::id() ?? 0;
            return app(InspectApprovalService::class)->getApprovalListByUserID($userID);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取巡检记录审批详情
     * @param int $taskDeviceRecordID
     * @return array|\Illuminate\Http\JsonResponse
     */
    public function approvalDetail(int $taskDeviceRecordID)
    {
        try {
            return app(InspectApprovalService::class)->getApprovalByTaskDeviceRecordID($taskDeviceRecordID);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 审批
     * @param int $taskDeviceRecordID
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\stdClass
     */
    public function approve(int $taskDeviceRecordID, Request $request)
    {
        try {
            $validated = $request->validate([
                'approve_status' => 'required|in:0,1',
            ]);
            app(InspectApprovalService::class)->approve($taskDeviceRecordID, $validated['approve_status']);
            return $this->ok();
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
