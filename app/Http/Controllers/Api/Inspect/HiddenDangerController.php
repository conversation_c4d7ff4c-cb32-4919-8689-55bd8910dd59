<?php

namespace App\Http\Controllers\Api\Inspect;

use App\Http\Controllers\Api\Controller;
use App\Services\Inspect\InspectHiddenDangerService;
use Illuminate\Http\Request;

/**
 * 随手拍
 */
class HiddenDangerController extends Controller
{
    /**
     * 创建随手拍
     * @param Request $request
     * @return array|\Illuminate\Http\JsonResponse|\stdClass
     */
    public function create(Request $request)
    {
        try {
            $validated = $request->validate([
                'question' => 'required|string',
                'suggestion' => 'string',
                'images' => 'required|array',
                'is_public' => 'required|in:0,1'
            ]);
            $hiddenDanger = app(InspectHiddenDangerService::class)->create(
                $validated['question'],
                $validated['suggestion'],
                $validated['images'],
                $validated['is_public']
            );
            return ['id' => $hiddenDanger->id];
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取用户自己的随手拍
     * @return \Illuminate\Http\JsonResponse|\stdClass
     */
    public function getOwnerHiddenDanger()
    {
        try {
            $userID = auth()->id();
            return app(InspectHiddenDangerService::class)->getHiddenDangerByUserID($userID);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取随手拍大厅 top震惊了 down没什么 mine 我发布的
     * @param Request $request
     * @return array|\Illuminate\Http\JsonResponse|\stdClass
     */
    public function getHiddenDangerHall(Request $request)
    {
        try {
            $validated = $this->validate($request, [
                'type' => 'required|in:top,down,mine',
                'next_cursor' => 'string',
            ]);
            $data = app(InspectHiddenDangerService::class)->getHiddenDangerHallDataByType(
                $validated['type'],
                    $validated['next_cursor'] ?? null
            );
            return  $this->cursorPaginateToArray($data);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }


    /**
     * 获取随手拍详情, mine: 我发布的, hall: 随手拍大厅
     * @param int $id
     * @param Request $request
     * @return array|\Illuminate\Http\JsonResponse
     */
    public function getHiddenDangerDetail(int $id, Request $request)
    {
        try {
            $validated = $this->validate($request, [
                'type' => 'required',
            ]);
            return app(InspectHiddenDangerService::class)->getHiddenDangerByID($id, $validated['type']);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 审批随手拍, status: 0 不存在隐患, 1 存在隐患
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\stdClass
     */
    public function approveHiddenDanger(int $id, Request $request)
    {
       try {
           $validated = $this->validate($request, [
               'status' => 'required|in:0,1',
           ]);
           app(InspectHiddenDangerService::class)->approveHiddenDanger($id, $validated['status']);
           return $this->ok();
       } catch (\Exception $e) {
           return $this->error($e->getMessage());
       }
    }

    /**
     * 添加线索
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\stdClass
     */
    public function addClue(int $id, Request $request)
    {
        try {
            $validated = $this->validate($request, [
                'name' => 'required|string',
                'phone' => 'required|string',
                'company' => 'required|string',
            ]);
            app(InspectHiddenDangerService::class)->addClueRecords(
                $id,
                $validated['name'],
                $validated['phone'],
                $validated['company']
            );
            return $this->ok();
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 处理随手拍
     * amazed 点亮震惊
     * no_amazed 点亮没什么
     * lock 设置私密或公开
     * delete 删除
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\stdClass
     */
    public function handleHiddenDanger(int $id, Request $request)
    {
        try {
            $validated = $this->validate($request, [
                'type' => 'required',
            ]);
            app(InspectHiddenDangerService::class)->handleHiddenDanger($id, $validated['type']);
            return $this->ok();
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
