<?php

namespace App\Http\Controllers\Api\Inspect;

use App\Http\Controllers\Api\Controller;
use App\Models\Inspect\TaskDevicesRecord;
use Illuminate\Http\Request;

class TaskDevicesRecordController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'task_id' => 'required|integer'
        ]);

        $builder = TaskDevicesRecord::query()
            ->select(['id', 'device_id', 'task_id', 'checked_at', 'image_url', 'status', 'abnormal_count'])
            ->with('device', function ($builder) {
                $builder->select('id', 'name', 'image_url', 'deleted_at');
            });


        if (isset($params['task_id'])) {
            $builder->where('task_id', $params['task_id']);
        }

        $data = $builder->whereIn('status', TaskDevicesRecord::INSPECTED_STATUS)->get();

        // 按日期分组数据
        $groupedRecords = [];
        foreach ($data as $record) {
            $date = $record->checked_at ? $record->checked_at->format('Y-m-d') : 'unchecked';
            if (!isset($groupedRecords[$date])) {
                $groupedRecords[$date] = [];
            }
            $groupedRecords[$date][] = $record;
        }

        return $groupedRecords;
    }
}
