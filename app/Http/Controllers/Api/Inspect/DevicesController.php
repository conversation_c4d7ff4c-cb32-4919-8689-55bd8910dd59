<?php

namespace App\Http\Controllers\Api\Inspect;

use App\Http\Controllers\Api\Controller;
use App\Libs\Baidu\AipAPI;
use App\Models\Inspect\Device;
use App\Models\Inspect\DeviceItem;
use App\Models\Inspect\TaskDevice;
use App\Services\Common\AttachmentService;
use App\Services\Inspect\InspectDeviceItemService;
use App\Services\Inspect\InspectDeviceService;
use Illuminate\Http\Request;

class DevicesController extends Controller
{
    /**
     * 设备列表
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        $params = $request->validate([
            'keyword' => 'string|nullable',
            'operate' => 'in:create_task,add_device|nullable',
            'task_id' => 'integer|nullable',
        ]);

        $taskDeviceIds = [];
        if (isset($params['task_id'])) {
            $taskDeviceIds = TaskDevice::query()->where('task_id', $params['task_id'])->pluck('device_id');
        }

        $page = Device::query()
            ->when($params['keyword'] ?? null, function ($query, $keyword) {
                $query->where('name', 'like', "%{$keyword}%");
            })
            ->when($params['operate'] ?? null, function ($query, $operate) use ($taskDeviceIds) {
                // 有场景情况下，只显示有检测项的设备
                $query->where('inspection_item_count', '>', 0);

                // 添加设备场景下，不显示已添加的设备
                if ($operate == 'add_device') {
                    $query->whereNotIn('id', $taskDeviceIds);
                }
            })
            ->where('user_id', auth()->id())
            ->select(['id', 'name', 'image_url', 'inspection_item_count', 'remark'])
            ->orderBy('id', 'desc')
            ->cursorPaginate(20);

        return $this->cursorPaginateToArray($page);
    }

    public function store(Request $request): Device
    {
        $params = $request->validate([
            'name' => 'required|string',
            'image_url' => 'string',
            'remark' => 'string'
        ]);

        return app(InspectDeviceService::class)->create($params['name'], $params['image_url'], $params['remark'] ?? '');
    }

    public function update(Request $request, int $id): Device
    {
        $params = $request->validate([
            'name' => 'required|string',
            'image_url' => 'string',
            'remark' => 'string'
        ]);

        return app(InspectDeviceService::class)->update($id, $params);
    }

    public function show(int $id): Device
    {
        return Device::query()
            ->select(['id', 'name', 'image_url', 'remark'])
            ->with('items', function ($query) {
                $query->select(['id', 'name', 'device_id']);
            })->findOrFail($id);
    }

    public function aiCreate(Request $request): Device
    {
        $params = $request->validate([
            'name' => 'required|string',
            'item_names' => 'array',
            'image_url' => 'string',
        ]);

        $device = app(InspectDeviceService::class)->create($params['name'], $params['image_url'], '');

        if (isset($params['item_names'])) {
            InspectDeviceItemService::batchAssign($device->id, $params['item_names']);
        }

        return $device;
    }

    public function aiRecognition(Request $request, AipAPI $api)
    {
        $params = $request->validate([
            'image_url' => 'string',
        ]);

        return $api->imageUnderstanding($params['image_url']);
    }

    public function destroy(int $id): bool
    {

//            if (TaskDevice::query()->where('device_id', $id)->exists()) {
//                throw new Exception('已有任务正在使用此设备，暂不支持删除！');
//            }
        DeviceItem::query()->where('device_id', $id)->delete();
        return Device::query()->where('user_id', auth()->id())->findOrFail($id)->delete();
    }

    /**
     * 上传文件配置
     * @return array
     */
    public function uploadConfig(): array
    {
        $diskName = config('heguibao.storage.pub');

        return AttachmentService::getUploadForm('inspect', ['image'], maxSizeKB: 1024*10, diskName: $diskName);
    }
}
