<?php

namespace App\Http\Controllers\Api\Inspect;

use App\Http\Controllers\Api\Controller;
use App\Models\Inspect\TaskDevicesRecord;
use App\Services\Inspect\InspectApprovalService;
use App\Services\Inspect\InspectDeviceService;
use App\Services\Inspect\InspectTaskRecordService;
use App\Services\Inspect\InspectTaskService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TaskController extends Controller
{
    /**
     * 创建巡检任务
     * @param Request $request
     * @return array|\Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'frequency' => 'required|integer|min:1',
                'devices' => 'required|array',
            ]);

            $task = app(InspectTaskService::class)->create(
                $validated['name'],
                $validated['frequency'],
                $validated['devices']
            );
            return $task->toArray();
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取用户巡检任务列表
     * @return array|\Illuminate\Http\JsonResponse
     */
    public function myTask(Request $request)
    {
        try {
            $userID = Auth::id() ?? 0;
            $limit = $request->input('limit');
            return app(InspectTaskService::class)->getOwnTaskListByUserID($userID, $limit);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 巡检任务关联新的设备
     *
     * @param int $taskID
     * @param Request $request
     * @return JsonResponse|\stdClass
     */
    public function assocDevice(int $taskID, Request $request)
    {
        try {
            $validated = $request->validate([
                'devices' => 'required|array',
            ]);
            app(InspectTaskService::class)->addAssocDevice($taskID, $validated['devices']);
            return $this->ok();
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    /**
     * 获取巡检任务记录表
     * @param int $taskID
     * @return array|\Illuminate\Http\JsonResponse
     */
    public function taskDevicesRecordTable(int $taskID)
    {
        try {
            return app(InspectTaskService::class)->getTaskRecordTableByTaskID($taskID);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取巡检记录审批列表
     * @param int $taskID
     * @return array|\Illuminate\Http\JsonResponse
     */
    public function taskDevicesRecordApproval(int $taskID)
    {
        try {
            return app(InspectApprovalService::class)->getApprovalByTaskID($taskID);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取设备巡检记录详情
     * @param int $taskDeviceRecordID
     * @return array|\Illuminate\Http\JsonResponse
     */
    public function taskDeviceRecord(int $taskDeviceRecordID)
    {
        try {
            return app(InspectDeviceService::class)->getTaskDeviceRecordByRecordID($taskDeviceRecordID);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 填写巡检任务设备项记录表
     * @param int $taskDeviceRecordID
     * @param Request $request
     * @return array|JsonResponse
     */
    public function fillInTaskDeviceRecordTable(int $taskDeviceRecordID, Request $request)
    {
        try {
            $validated = $request->validate([
                'device_item_status' => 'required|array',
                'img' => 'required|array',
                'desc' => 'string',
                'suggest' => 'string',
            ]);
            $record = app(InspectTaskRecordService::class)->updateTaskDeviceRecord(
                $taskDeviceRecordID,
                $validated['device_item_status'],
                $validated['img'],
                $validated['desc'],
                $validated['suggest']
            );
            return [
                'approveId' => $record->approve_id,
                'needApprovalStatus' => $record->status === TaskDevicesRecord::PENDING_APPROVAL,
            ];
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
