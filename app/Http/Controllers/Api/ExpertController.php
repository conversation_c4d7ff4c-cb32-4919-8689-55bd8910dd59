<?php

namespace App\Http\Controllers\Api;

use App\Models\Expert;
use App\Rules\PhoneNumber;
use App\Services\Common\AttachmentService;
use App\Services\ExpertService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ExpertController extends Controller
{
    /**
     * 专家列表
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        $params = $this->validate($request, [
            'list_rows' => 'integer',
            'next_cursor' => 'string',
            'search' => 'string',
            'fields' => 'string',
        ]);

        $listRows = $params['list_rows'] ?? 20;
        $cursor = $params['next_cursor'] ?? null;

        $keyword = trim($params['search'] ?? '');
        $jsonFiltered = $params['fields'] ?? null;

        $builder = Expert::query()
            ->where('status', Expert::STATUS_NORMAL)
            ->where('is_visible', Expert::VISIBLE_YES)
            ->when($jsonFiltered, fn($q) => $q->whereJsonContains('fields', $jsonFiltered));


        // 处理关键词搜索
        if ($keyword !== '') {
            $keywords = preg_split('/\s+/', $keyword);
            // 严格模式匹配，如：高空作业 电梯，必须同时包含
            $booleanQuery = implode(' ', array_map(fn($word) => '+' . $word, $keywords));
            // 去掉了每个词前的 "+",改用宽松模式进行匹配，如关键词：高空作业 电梯（任意包含即可）
//            $booleanQuery = implode(' ', $keywords);

            /**
             * 对字段做全文关键词搜索
             * https://dev.mysql.com/doc/refman/8.0/en/fulltext-boolean.html
             * ngram 全文解析器,支持中文：https://dev.mysql.com/doc/refman/8.0/en/fulltext-search-ngram.html
             */
            $builder->whereRaw("
            MATCH(
              name, residence, major, education, occupation, industry,safety_work_experience, course_scopes, typical_cases,
              serve_customers, teaching_styles, extra_text
            ) AGAINST (? IN BOOLEAN MODE)
        ", [$booleanQuery]);
        }


        // 分页查询最终记录
        $builder->orderByDesc('sort')->orderByDesc('id');

        $page = $builder->cursorPaginate(perPage: $listRows, cursor: $cursor);

        return $this->cursorPaginateToArray($page);
    }



    /**
     * 专家申请
     *
     * @param Request $request
     * @return Expert
     * @throws \Throwable
     */
    public function store(Request $request): Expert
    {
        $params = $this->validate($request, [
            'name' => 'required|string|max:10',
            'phone' => ['required', new PhoneNumber()],
            'gender' => 'required|in:1,2',
            'residence' => 'required|string|max:128',
            'major' => 'required|string|max:32',
            'safety_work_experience' => 'required|string|max:1000',
            'education' => 'required|string',
            'occupation' => 'required|string|max:64',
            'industry' => 'required|string|max:30',
            'work_year' => 'required|integer|min:1',
            'fields' => 'required|array',
            'services' => 'required|array',
            'photo' => 'required|string',
            'certs_add' => 'required|array',
            'course_scopes' => 'required|string',
            'typical_cases' => 'required|string',
            'scene_photos_add' => 'required|array',
            'serve_customers' => 'string|max:1000',
            'teaching_styles' => 'string|max:1000',
            'remark' => 'string|max:2000'
        ], [], [
            'work_year' => '从事专业年限'
        ]);

        $userId = Auth::id();

        $expert = Expert::query()->where('user_id', $userId)->exists();
        if ($expert) {
            throw new BadRequestHttpException("请勿重复申请");
        }

        $expert = ExpertService::store($params, $userId);
        $expert->status = Expert::STATUS_PENDING;
        $expert->save();

        return $expert;
    }

    /**
     * 专家详情
     *
     * @param $id
     * @return Expert
     */
    public function show($id): Expert
    {
        /** @var Expert $expert */
        $expert = Expert::query()->where('id', $id)->first();
        if (!$expert) {
            throw new NotFoundHttpException("专家数据不存在");
        }

        return $expert;
    }

    /**
     * 我的详情
     *
     * @return Expert
     */
    public function myShow(): Expert
    {
        /** @var Expert $expert */
        $expert = Expert::query()->where('user_id', Auth::id())->first();
        if (!$expert) {
            throw new NotFoundHttpException("专家数据不存在");
        }

        return $expert;
    }

    /**
     * 重新申请
     *
     * @param $id
     * @param Request $request
     * @return Expert
     * @throws \Throwable
     */
    public function update($id, Request $request): Expert
    {
        $params = $this->validate($request, [
            'name' => 'required|string|max:10',
            'phone' => ['required', new PhoneNumber()],
            'gender' => 'required|in:1,2',
            'residence' => 'required|string|max:128',
            'major' => 'required|string|max:32',
            'safety_work_experience' => 'required|string|max:1000',
            'education' => 'required|string',
            'occupation' => 'required|string|max:64',
            'industry' => 'required|string|max:30',
            'work_year' => 'required|integer|min:1',
            'fields' => 'required|array',
            'services' => 'required|array',
            'photo' => 'required|string',
            'certs_add' => 'nullable|array',
            'certs_remove' => 'nullable|array',
            'course_scopes' => 'required|string|max:1000',
            'typical_cases' => 'required|string|max:1000',
            'scene_photos_add' => 'nullable|array',
            'scene_photos_remove' => 'nullable|array',
            'serve_customers' => 'string|max:1000',
            'teaching_styles' => 'string|max:1000',
            'remark' => 'string|max:1000'
        ], [], [
            'work_year' => '从事专业年限'
        ]);

        /** @var Expert $expert */
        $expert = Expert::query()->where('user_id', Auth::id())->where('id', $id)->first();
        if (!$expert) {
            throw new NotFoundHttpException("专家数据不存在");
        }

        if ($expert->status != Expert::STATUS_REJECT) {
            throw new BadRequestHttpException("非拒绝状态不能修改");
        }

        $expert = ExpertService::update($params, $expert);
        $expert->status = Expert::STATUS_PENDING;
        $expert->save();

        return $expert;
    }

    /**
     * 上传文件配置
     *
     * @return array
     */
    public function uploadConfig(): array
    {
        $diskName = config('heguibao.storage.pub');

        return AttachmentService::getUploadForm(Auth::id(), ['image', 'doc'], maxSizeKB: 1024*10, diskName: $diskName);
    }

    /**
     * 表单配置项
     *
     * @return array
     */
    public function formConfig(): array
    {
        return config('expert');
    }
}
