<?php

namespace App\Http\Controllers\Api\Chat;

use App\Http\Controllers\Api\Controller;
use App\Models\Chat\ChatMessage;
use App\Models\Chat\ChatSession;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ChatMessageController extends Controller
{
    public function index(Request $request)
    {
        $params = $request->validate([
            'session_id' => ['required', 'integer'],
        ]);

        $buildQuery = ChatMessage::query()->where('session_id', $params['session_id'])->orderBy('id');

        return $buildQuery->paginate();
    }

    public function store(Request $request)
    {
        $params = $request->validate([
            'prompt' => ['required', 'string'],
            'completion' => ['required', 'string'],
            'session_id' => ['required', 'integer'],
        ]);

        if (!$params['session_id']) {
            $session = new ChatSession();
            $session->user_id = auth()->id();
            $session->title = mb_substr($params['prompt'], 0, 50);
            $session->model = ChatSession::MODEL_ERNIE_BOT_4;
            $session->save();

            $params['session_id'] = $session->id;
        }

        $message = new ChatMessage();
        $message->user_id = auth()->id();
        $message->session_id = $params['session_id'];
        $message->prompt = $params['prompt'];
        $message->completion = $params['completion'];
        $message->like = 0;
        $message->save();

        return $message;
    }

    public function update(Request $request)
    {
        $params = $request->validate([
            'completion' => ['required', 'string'],
            'like' => ['required', 'integer'],
        ]);

        /** @var ChatMessage $message */
        $message = ChatMessage::query()->where('user_id', auth()->id())->find($request->id);

        if (!$message) {
            throw new BadRequestHttpException('会话不存在');
        }

        $message->completion = $params['completion'];
        $message->like = $params['like'];
        $message->save();

        return $message;
    }

    public function like(Request $request)
    {
        $params = $request->validate([
            'id' => ['required', 'integer'],
            'like' => ['required', 'integer', 'in:0,1,2'],
        ]);

        ChatMessage::query()->where('id', $params['id'])->update(['like' => $params['like']]);

        return ['success' => true];
    }
}
