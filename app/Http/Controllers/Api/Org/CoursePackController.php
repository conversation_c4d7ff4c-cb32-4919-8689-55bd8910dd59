<?php

namespace App\Http\Controllers\Api\Org;

use App\Http\Controllers\Api\Controller;
use App\Models\Org;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

/**
 * 机构课程包
 */
class CoursePackController extends Controller
{

    /**
     * 机构课程列表
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=14359b5171d00a
     */
    public function index(string $orgSid)
    {
        $orgId = Org::decodeSid($orgSid);

        return Org\CoursePack::query()
            ->with('contentCoursePack')
            ->with('contentCoursePack.content', fn($query) => $query->publicFields())
            ->with('contentCoursePack', fn($query) => $query->withSum('contentCourses', DB::raw('learning_count+learning_count_add')))
            ->where('org_id', $orgId)
            ->where('status', Org\CoursePack::STATUS_VISIBLE)
            ->latest('id')
            ->get()
            ->map(function(Org\CoursePack $item) {
                $content = $item->contentCoursePack->content->toArray();
                $content = Arr::only($content, ['title', 'intro', 'views', 'release_at', 'sid', 'cover_src']);
                $content['price_sell'] = $item->price_sell;
                $content['learning_count'] = $item->contentCoursePack->content_courses_sum_learning_countlearning_count_add;
                return $content;
            });
    }

}
