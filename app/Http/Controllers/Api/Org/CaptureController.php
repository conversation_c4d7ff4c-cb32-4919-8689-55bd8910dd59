<?php

namespace App\Http\Controllers\Api\Org;

use App\Http\Controllers\Api\Controller;
use App\Models\Org;
use App\Services\Org\CaptureService;
use App\Services\WechatService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class CaptureController extends Controller
{

    /**
     * 提交抓拍
     *
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=8bda27c3bc002
     */
    public function store(Request $request, $scene)
    {
        $data = $this->validate($request, [
            'photo' => 'string|required',
        ]);

        CaptureService::save($scene, $data['photo']);
    }

    /**
     * 获取机构信息
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=29dc2e0ab9302c
     *
     * @param string $scene
     * @return Org
     */
    public function getOrg(string $scene): Org
    {
        return CaptureService::getOrg($scene);
    }

    /**
     * 获取抓拍小程序二维码
     *
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=8ca15ec7bc00d
     */
    public function wechatQrcode($scene)
    {
        $userId = auth()->id();

        if (!CaptureService::verifyCode($scene, $userId)) {
            throw new BadRequestHttpException('抓码请求已失效，请重试！');
        }

        $result = WechatService::getWxaCodeUnlimit($scene, 'packageOrg/organization/photos');

        return [
            'scene' => $scene,
            'type' => $result['type'],
            'image' => base64_encode($result['image'])
        ];
    }

    /**
     * 检查抓拍状态
     *
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=b06eb9230d023
     */
    public function check($scene)
    {
        $uploaded = CaptureService::check($scene);
        return compact('uploaded');
    }

}
