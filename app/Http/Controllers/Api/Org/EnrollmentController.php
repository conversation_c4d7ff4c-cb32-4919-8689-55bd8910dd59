<?php

namespace App\Http\Controllers\Api\Org;

use App\Http\Controllers\Api\Controller;
use App\Libs\Baidu\AipAPI;
use App\Libs\Meitu\AIGCP;
use App\Libs\Sms\SmsTemplate;
use App\Libs\Utils\Helpers;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseProgress;
use App\Models\Org;
use App\Models\Org\CoursePack;
use App\Models\Org\Enrollment;
use App\Models\Org\EnrollmentForm;
use App\Models\Org\EnrollOperateRecord;
use App\Models\Org\OrgClass;
use App\Models\Org\Student;
use App\Models\User;
use App\Models\User\UserOwnTopic;
use App\Services\Cms\ContentCourseProgressService;
use App\Services\Common\AttachmentService;
use App\Services\Common\PhoneCodeService;
use App\Services\Org\CourseService;
use App\Services\Org\EnrollmentFormService;
use App\Services\Org\EnrollmentService;
use App\Services\Org\EnrollService;
use App\Services\Org\Export\HourCertExporter;
use App\Services\Org\Export\ValueItem;
use App\Services\Org\LearnCaptureService;
use App\Services\Org\OrgClassService;
use App\Services\Org\StudentService;
use App\Services\Org\TemplateReplace\ReplaceService;
use Carbon\Carbon;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\GifWriter;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class EnrollmentController extends Controller
{
    /**
     * 报名列表
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=c1bee2d793107
     *
     * @param string $orgSid
     * @return array
     */
    public function index(string $orgSid): array
    {
        /** @var Org $org */
        $org = Org::whereSid($orgSid)->publicFields()->first();
        if (!$org) {
            throw new NotFoundHttpException('机构不存在。');
        }

        $enrollments = Enrollment::publicFields()
            ->where('user_id', Auth::id())
            ->where('org_id', $org->id)
            ->get();

        if ($enrollments->isEmpty()) {
            return [];
        }

        return $enrollments->toArray();
    }

    /**
     * 我的报名信息
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=e75c9e8393002
     *
     * @param string $orgSid
     * @return Student
     */
    public function myEnrollment(string $orgSid): Student
    {
        /** @var Org $org */
        $org = Org::whereSid($orgSid)->publicFields()->first();
        if (!$org) {
            throw new NotFoundHttpException('机构不存在。');
        }

        /** @var Student $student */
        $student = Student::query()
            ->with(['form' => fn ($query) => $query->publicFields()])
            ->where('user_id', Auth::id())
            ->where('org_id', $org->id)
            ->orderByDesc('id')
            ->first();

        if (!$student) {
            return new Student();
        }

        $extras = array_column($student->extra, null, 'id');
        $fields = $student->form?->fields ?? [];
        foreach ($fields as &$field) {
            if (!isset($extras[$field['id']])) {
                if ($field['type'] == EnrollmentForm::TYPE_PHOTO) {
                    $value = $student->photo;
                } elseif (in_array($field['type'], [EnrollmentForm::TYPE_PIC, EnrollmentForm::TYPE_FILE])) {
                    $value = [];
                } else {
                    $value = '';
                }
                $field['value'] = $value;
                $field['display_text'] = '';
            } else {
                $field['value'] = $extras[$field['id']]['value'];
                $field['display_text'] = $extras[$field['id']]['display_text'];
            }
        }
        $student->extra = $fields;
        $student->form?->setHidden(['id', 'org_id']);
        $student->org_enable_enroll = $org->enable_enroll;

        return $student;
    }

    /**
     * 我的培训
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=3c7327f2b93020
     *
     * @param string $orgSid 机构sid
     * @return array
     */
    public function myTrain(string $orgSid): array
    {
        /** @var User $user */
        $user = auth()->user();

        /** @var Org $org */
        $org = Org::whereSid($orgSid)->publicFields()->first();

        if (!$org) {
            throw new NotFoundHttpException('机构不存在。');
        }

        $data = ['org' => $org, 'trains' => []];
        $enrollments = Enrollment::query()
            ->with([
                'classroom',
                'resource' => fn ($query) => $query->publicFields()
            ])
            ->where('user_id', $user->id)
            ->where('org_id', $org->id)
            ->whereIn('status', Enrollment::studyStatuses())
            ->orderBy('started_at', 'desc')
            ->get();

        if ($enrollments->isEmpty()) {
            return $data;
        }

        $topicIds = [];
        /** @var Enrollment $enrollment */
        foreach ($enrollments as $enrollment) {
            if ($enrollment->resource?->topic) {
                $topicIds[] = $enrollment->resource->topic->id;
            }
        }

        if (!empty($topicIds)) {
            $topicIds = UserOwnTopic::query()
                ->whereIn('topic_id', $topicIds)
                ->where('user_id', $user->id)
                ->where(function ($query) {
                    $query->whereNull('expired_at')->orWhere('expired_at', '>', Carbon::now());
                })
                ->pluck('topic_id')
                ->toArray();
        }

        foreach ($enrollments as $enrollment) {
            if (!$enrollment->resource || $enrollment->classroom?->status == OrgClass::STATUS_FINISHED) {
                continue;
            }

            // train 变量默认值
            $enrollSid = $enrollment->sid;
            $topic = null;
            $course = null;
            $exam = null;
            $coursePack = null;

            // clone 防止同一个课程数据覆盖
            $resource = clone $enrollment->resource;

            if ($enrollment->type == Enrollment::TYPE_COURSE) {
                $resource->detailAppend(['enrollment' => $enrollment]);
                $resource->orgAppend($enrollment->org_id, $enrollment->id);
                $resource->study_record_url = $enrollment->study_record_url;
                $resource->course_name = $resource->content->title;

                list($chapterIds, $sectionIds) = CourseService::orgCourseChapSecIds($org->id, $enrollment->resource_id);
                $resource->chapters_count = count($chapterIds);
                $resource->sections_count = count($sectionIds);

                // 题库
                if ($resource->topic && in_array($resource->topic->id, $topicIds)) {
                    $resource->load(['topic' => fn ($query) => $query->publicFields()]);
                    $resource->topic?->subjectCount();
                    $resource->topic?->setHidden(['id', 'amount', 'course_category_id']);
                    $topic = $resource->topic;
                    $resource->makeHidden(['topic']);
                }

                // 课程
                $enrollment->resource = $resource;
                $course = $enrollment->resource->setHidden(['content_id', 'topic_id']);
                // 考试信息
                $currentExam= OrgClassService::currentExam($user, $enrollment, $this->platform ?? 'miniProgram');
                $exam = $currentExam ?: null;
            } elseif ($enrollment->type == Enrollment::TYPE_TOPIC) {
                $resource->setHidden(['id', 'amount', 'course_category_id'])->subjectCount();
                $enrollment->resource = $resource;
                $topic = $enrollment->resource;
            } elseif ($enrollment->type == Enrollment::TYPE_COURSE_PACK) {
                $resource->detailAppend(['enrollment' => $enrollment]);
                $resource->orgAppend($enrollment->org_id, $enrollment->id);
                $resource->study_record_url = $enrollment->study_record_url;
                $resource->course_pack_name = $resource->content->title;

                // 题库
                if ($resource->topic && in_array($resource->topic->id, $topicIds)) {
                    $resource->load(['topic' => fn ($query) => $query->publicFields()]);
                    $resource->topic?->subjectCount();
                    $resource->topic?->setHidden(['id', 'amount', 'course_category_id']);
                    $topic = $resource->topic;
                    $resource->makeHidden(['topic']);
                }

                // 课程包
                $enrollment->resource = $resource;
                $coursePack = $enrollment->resource->setHidden(['content_id', 'topic_id']);

                // 考试信息
                $currentExam = OrgClassService::currentExam($user, $enrollment, $this->platform ?? 'miniProgram');
                $exam = $currentExam ?: null;
            }

            $data['trains'][] = [
                'enroll_sid' => $enrollSid,
                'course' => $course,
                'course_pack' => $coursePack,
                'topic' => $topic,
                'exam' => $exam,
            ];
        }

        return $data;
    }

    /**
     * 我的学习
     * @see
     *
     * @param string $orgSid 机构sid
     * @return array
     */
    public function myStudy(string $orgSid): array
    {
        /** @var User $user */
        $user = auth()->user();

        /** @var Org $org */
        $org = Org::whereSid($orgSid)->publicFields()->first();

        if (!$org) {
            throw new NotFoundHttpException('机构不存在。');
        }

        $enrollments = Enrollment::query()
            ->with([
                'classroom',
                'resource' => fn ($query) => $query->publicFields()
            ])
            ->where('user_id', $user->id)
            ->where('org_id', $org->id)
            ->whereIn('status', Enrollment::studyStatuses())
            ->orderBy('started_at', 'desc')
            ->get();
        $data = ['org' => $org, 'trains' => []];

        if ($enrollments->isEmpty()) {
            return $data;
        }

        /** @var Enrollment $enrollment */
        foreach ($enrollments as $k => $enrollment) {
            if (!$enrollment->resource) {
                continue;
            }

            $data['trains'][$k]['enroll_sid'] = $enrollment->sid;
            $resource = clone $enrollment->resource;

            if ($enrollment->type == Enrollment::TYPE_COURSE) {
                $resource->detailAppend(['enrollment' => $enrollment]);
                $resource->orgAppend($enrollment->org_id, $enrollment->id);
                $resource->study_record_url = $enrollment->study_record_url;
                $resource->course_name = $resource->content->title;
                $enrollment->resource = $resource;
                $data['trains'][$k]['course'] = $enrollment->resource->setHidden(['content_id', 'topic_id']);
                $data['trains'][$k]['course_pack'] = null;
            } elseif ($enrollment->type == Enrollment::TYPE_COURSE_PACK) {
                $resource->detailAppend(['enrollment' => $enrollment]);
                $resource->orgAppend($enrollment->org_id, $enrollment->id);
                $resource->study_record_url = $enrollment->study_record_url;
                $resource->course_pack_name = $resource->content->title;
                $enrollment->resource = $resource;
                $data['trains'][$k]['course_pack'] = $enrollment->resource->setHidden(['content_id', 'topic_id']);
                $data['trains'][$k]['course'] = null;
            }
        }

        return $data;
    }

    /**
     * 报名表结构
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=1a52b463930ef
     *
     * @param string $orgSid 机构sid
     * @return EnrollmentForm
     */
    public function structure(string $orgSid): EnrollmentForm
    {
        $orgId = Org::decodeSid($orgSid);

        /** @var EnrollmentForm $form */
        $form = EnrollmentForm::publicFields()->where('org_id', $orgId)->first()?->setHidden(['id', 'org_id']);

        if (!$form) {
            throw new NotFoundHttpException('报名表不存在');
        }
        $form->org_sid = $orgSid;

        return $form;
    }

    /**
     * 报名
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=3408cd3f93003
     *
     * @param string $orgSid 机构sid
     * @param Request $request
     * @return string[]
     * @throws \Throwable
     */
    public function store(string $orgSid, Request $request)
    {
        $params = $this->validate($request, [
            'name' => 'required|string|max:20',
            'phone' => 'required|string',
            'code' => 'required|string',
            'id_card_number' => 'required|string',
            'id_card_font' => 'required|string', // todo 修改字段为 id_card_front
            'id_card_back' => 'required|string',
            'extras' => 'array',
            'class_sid' => 'string'
        ]);

        $pattern = '/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}(\d|X|x)$/';
        if (!preg_match($pattern, $params['id_card_number'])) {
            throw new BadRequestHttpException('身份证号码不正确。');
        }

        /** @var Org $org */
        $org = Org::whereSid($orgSid)->publicFields()->first();
        if (!$org) {
            throw new NotFoundHttpException('机构不存在。');
        }

        if (isset($params['class_sid'])) {
            $classId = OrgClass::whereSid($params['class_sid'])->where('org_id', $org->id)->value('id');
            if (!$classId) {
                throw new NotFoundHttpException('机构班级不存在。');
            }
        }

        // 表单验证
        if (isset($params['extras'])) {
            EnrollmentFormService::validateForm($org->id, $params['extras']);
        }

        if (!PhoneCodeService::verify($params['phone'], $params['code'], SmsTemplate::LOGIN)) {
            throw new BadRequestHttpException('验证码不正确或已过期。');
        }

        $userId = User::query()->where('phone', $params['phone'])->value('id');
        if (!$userId) {
            $user = User::create($params['phone']);
            $userId = $user->id;
        }

        $student = StudentService::check(
            $org->id,
            $userId,
            $params['name'],
            $params['phone'],
            $params['id_card_number'],
            $params['id_card_font'], // todo 修改字段为 id_card_front
            $params['id_card_back'],
            $params['extras'] ?? [],
        );

        if (!$student->wasRecentlyCreated) {
            $params['id_card_front'] = $params['id_card_font'];
            StudentService::update($student->id, $org->id, $params);
        }

        // 存在机构报名配置ID
        if ($request->input('org_enroll_config_id', 0)) {
            app(EnrollService::class)->create([
                'org_id' => $org->id,
                'user_id' => $userId,
                'student_id' => $student->id,
                'enroll_config_id' => $request->input('org_enroll_config_id'),
            ]);
        }

        return ['message' => 'success'];

        // 报名不需要开班相关操作
        // return EnrollmentService::create($org->id, $userId, $student->id, $classId ?? 0);
    }

    /**
     * 修改个人信息
     *
     * @param string $sid
     * @param Request $request
     * @return Student
     */
    public function update(string $sid, Request $request): Student
    {
        $params = $this->validate($request, [
            'extras' => 'required|array',
        ]);

        $orgId = Org::whereSid($sid)->value('id');

        if (!$orgId) {
            throw new NotFoundHttpException('机构不存在。');
        }

        /** @var Student $student */
        $student = Student::query()
            ->where('user_id', Auth::id())
            ->where('org_id', $orgId)
            ->first();

        if (!$student) {
            throw new NotFoundHttpException("个人信息不存在。");
        }

        StudentService::update($student->id, $student->org_id, [
            'extras' => $params['extras'],
        ]);

        return $student;
    }

    /**
     * 获取身份证详情
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=5c54739f93081
     *
     * @param Request $request
     * @param AipAPI $baiDuOcr
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getCardInfo(Request $request, AipAPI $baiDuOcr): array
    {
        $params = $request->validate([
            'id_card' => 'required|string',
            'type' => 'required|string|in:front,back'
        ]);

        $url = AttachmentService::url($params['id_card']);

        if ($params['type'] == 'front') {
            $cardInfo = $baiDuOcr->idCardFront($url);
            $age = Helpers::getAgeByBirthday($cardInfo['birthday']);

            if ($age < 18) {
                throw new BadRequestHttpException('未满18岁，暂时无法报名。');
            }
        } else {
            $cardInfo = $baiDuOcr->idCardBack($url);
        }

        return ['card_info' => $cardInfo];
    }

    /**
     * 表单上传配置
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=428809ff93033
     *
     * @param string $orgSid 机构sid
     * @param Request $request
     * @return array
     */
    public function uploadConfig(string $orgSid, Request $request): array
    {
        $params = $request->validate([
            'field_id' => 'string',
            'type' => 'required_without:field_id|string|in:image'
        ]);

        $orgId = Org::decodeSid($orgSid);

        $fieldId = $params['field_id'] ?? null;
        $diskName = config('heguibao.storage.priv');
        if (!$fieldId) {
            return AttachmentService::getUploadForm($orgId, $params['type'], maxSizeKB: 1024*1024*8, diskName: $diskName);
        }

        $fields = EnrollmentForm::query()->where('org_id', $orgId)->value('fields');
        if (!$fields) {
            throw new NotFoundHttpException("表单项不存在");
        }

        $field = array_filter($fields, fn ($item) => $item['id'] == $params['field_id']);
        if (empty($field)) {
            throw new NotFoundHttpException("表单项{$params['field_id']}不存在");
        }

        $field = array_values($field)[0];
        if (!in_array($field['type'], [EnrollmentForm::TYPE_PHOTO, EnrollmentForm::TYPE_PIC, EnrollmentForm::TYPE_FILE])) {
            throw new BadRequestHttpException("非上传表单类型");
        }

        if ($field['type'] == EnrollmentForm::TYPE_FILE) {
            $ext = $field['config']['ext'];
            if (is_array($ext)) {
                if (in_array('*', $ext)) {
                    $fileTypes = '*';
                } else {
                    $fileTypes = $ext;
                }
            } else {
                $fileTypes = $ext;
            }
        } else {
            $fileTypes = 'image';
        }
        if ($field['type'] == EnrollmentForm::TYPE_PHOTO) {
            $maxSizeKB = 10240;
        } else {
            $maxSizeKB = $field['config']['size'] ?? '2048';
        }


        return AttachmentService::getUploadForm("org_id:$orgId", $fileTypes, maxSizeKB: $maxSizeKB, diskName: $diskName);
    }

    /**
     * 学时证明
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=18d14860f93014
     *
     * @param string $sid
     * @param Request $request
     * @return array
     */
    public function hourCert(string $sid, Request $request): array
    {
        $params = $request->validate([
            'hash' => 'required|string'
        ]);

        /** @var Enrollment $enroll */
        $enroll = Enrollment::whereSid($sid)
            ->with([
                'org' => fn($query) => $query->select(['id', 'name'])
            ])
            ->first();

        if (!$enroll) {
            throw new NotFoundHttpException("学员 $sid 不存在。");
        }

        $idCardNumber = $enroll->getIdCardNumber();
        $hash = md5("$enroll->id,$enroll->org_id,$idCardNumber,$sid,hour-cert");

        if ($params['hash'] != $hash) {
            throw new BadRequestHttpException("验签失败。");
        }

        //基础信息
        $items = HourCertExporter::getBaseData($enroll);

        //二维码
        $qrCode = new QrCode($enroll->hour_cert_url);
        $writer = new GifWriter();
        $result = $writer->write($qrCode);

        $items[] = ValueItem::imageDataURI('qr_code', $result->getDataUri());

        return ReplaceService::convert($items);
    }

    /**
     * 获取学时证明图片地址
     *
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=2a0b0828f9300f
     */
    public function hourCertImage(string $sid)
    {
        /** @var Enrollment $enroll */
        $enroll = Enrollment::whereSid($sid)
            ->where('user_id', auth()->id())
            ->first();

        if (!$enroll) {
            throw new BadRequestHttpException('参数错误');
        }

        if (!$enroll->hour_cert_image_url) {
            throw new BadRequestHttpException('未满足学时证明生成条件');
        }

        return [
            'image_url' => $enroll->hour_cert_image_url
        ];
    }

    /**
     * 学习记录
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=1916885a39303f
     *
     * @param string $sid
     * @param Request $request
     * @return array
     */
    public function studyRecord(string $sid, Request $request): array
    {
        $params = $request->validate([
            'hash' => 'required|string'
        ]);

        /** @var Enrollment $enroll */
        $enroll = Enrollment::whereSid($sid)
            ->with([
                'org' => fn($query) => $query->select(['id', 'name'])
            ])
            ->first();

        if (!$enroll) {
            throw new NotFoundHttpException("学员 $sid 不存在。");
        }

        $student = $enroll->getStudent();
        $idCardNumber = $student->id_card_number;
        $hash = md5("$enroll->id,$enroll->org_id,$idCardNumber,$sid,study-record");

        if ($params['hash'] != $hash) {
            throw new BadRequestHttpException("验签失败。");
        }

        $items = [
            ValueItem::text('number', $enroll->number),
            ValueItem::text('name', $student->name),
            ValueItem::text('phone', $student->phone),
            ValueItem::text('id_card_number', $student->id_card_number),
            ValueItem::image('photo', $student->photo, private: true),
            ValueItem::text('course_name', $enroll->getResourceName() ?: '无'),
            ValueItem::text('course_started_at', $enroll->started_at?->format('Y-m-d H:i:s') ?: '无'),
            ValueItem::text('company', $student->work_unit ?: '')
        ];

        $record = ReplaceService::convert($items);

        if (in_array($enroll->type, [Enrollment::TYPE_COURSE_PACK, Enrollment::TYPE_COURSE]) && $enroll->resource_id > 0) {
            // 获取课程/课程包机构配置的章节信息
            if ($enroll->type == Enrollment::TYPE_COURSE_PACK) {
                $chapterSections = CoursePack::getCoursesResourceTree($enroll->org_id, $enroll->resource_id, true);
            } else {
                $chapterSections = CourseService::getCoursesResourceTree($enroll->org_id, [$enroll->resource_id], true);
            }

            $courseIds = $chapterSections->keys()->toArray();

            /** @var Collection<ContentCourseProgress> $progresses */
            $progresses = ContentCourseProgressService::getCoursesProgress($enroll->user_id, $courseIds, $enroll->org_id, $enroll->id);

            // 抓拍照片记录
            $capturePictures = LearnCaptureService::coursesCapturePhotos($enroll->id, $courseIds);
            $courses = [];

            foreach ($chapterSections as $courseId => $chapterSection) {
                $chapters = [];
                $content = Content::query()->find($courseId);

                foreach ($chapterSection as $chapter) {
                    $sections = [];

                    if ($chapter['sections']) {
                        foreach ($chapter['sections'] as $section) {
                            /** @var ContentCourseProgress|null $progress */
                            $progress = $progresses->get($section['id']);

                            $sections[] = [
                                'sid' => $progress?->section->sid,
                                'type' => '节',
                                'name' => $section['sn'] . ' ' . $section['name'],
                                'course_hour' => $section['hour'],
                                'start_time' => $progress?->created_at->format('Y-m-d H:i:s') ?? '无',
                                'end_time' => $progress?->updated_at->format('Y-m-d H:i:s') ?? '无',
                                'progress' => ContentCourseProgress::progressStatus($progress),
                                'capture_pictures' => isset($capturePictures[$section['id']]) ? array_column($capturePictures[$section['id']], 'photo_url') : []
                            ];
                        }
                    }

                    $chapters[] = [
                        'type' => '章',
                        'name' => $chapter['sn'] . ' ' . $chapter['name'],
                        'sections' => $sections
                    ];
                }

                $courses[] = [
                    'type' => '课程',
                    'name' => $content->title,
                    'chapters' => $chapters
                ];
            }
        }

        $studyRecords['courses'] = $courses ?? [];
        $record['study_records'] = $studyRecords;

        //是否可下载（只有在当前用户自己浏览且达到生成学时证明图片条件时可下载）
        $record['downloadable'] = auth()->id() == $enroll->user_id && $enroll->exam_passed;

        return $record;
    }

    /**
     * 人像图片生成证件照
     *
     * @param Request $request
     * @param AIGCP $meitu
     * @return array
     */
    public function toIDPhoto(Request $request, AIGCP $meitu): array
    {
        $params = $request->validate([
            'photo' => 'required|string'
        ]);

        $photo = $params['photo'];
        $url = AttachmentService::url($photo);
        $cutUrl = $meitu->aiCut($url)['url'];

        try {
            // 下载图片
            $download = AttachmentService::download($cutUrl);
            $sourceFile = $download['local_path'];

            // GD库 画白底
            list($filename, $destFile) = AttachmentService::tmpFile('jpg', 'org_e_photo_');
            $result = Helpers::fillTransparentBackground($sourceFile, $destFile);

            if (!$result) {
                throw new BadRequestHttpException('证件照生成失败');
            }
        } catch (\Exception $e) {
            Log::error("照片透明合成白底图失败：" . $e->getMessage());
            throw new BadRequestHttpException('证件照生成失败');
        }

        $diskName = config('heguibao.storage.priv');
        return AttachmentService::upload($destFile, $diskName, $filename);
    }



}
