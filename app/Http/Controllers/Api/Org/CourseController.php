<?php

namespace App\Http\Controllers\Api\Org;

use App\Http\Controllers\Api\Controller;
use App\Models\Org;
use App\Models\Org\Course;

class CourseController extends Controller
{
    /**
     * 机构课程列表
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=5d78a63f930a6
     *
     * @param string $orgSid 机构sid
     * @return array
     */
    public function index(string $orgSid): array
    {
        $orgId = Org::decodeSid($orgSid);

        $courses = Course::publicFields()
            ->with(['contentCourse' => fn ($query) => $query->publicFields()])
            ->where('org_id', $orgId)
            ->where('status', Course::STATUS_VISIBLE)
            ->latest('id')
            ->get()
            ?->setHidden(['id', 'org_id', 'course_id']);

        $list = [];
        foreach ($courses as $course) {
            $course->contentCourse->ownAppend();
            if ($course->content) {
                $list[] = [
                    'current_section' => $course->contentCourse->current_section,
                    'sid' => $course->content->sid,
                    'name' => $course->content->title,
                    'cover_src' => $course->content->cover_src,
                    'learning_count' => $course->contentCourse?->learning_count ?? 0,
                    'price_sell' => number_format($course->price_sell, 2, '.', '')
                ];
            }
        }

        return $list;
    }
}
