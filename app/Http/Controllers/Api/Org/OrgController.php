<?php

namespace App\Http\Controllers\Api\Org;

use App\Http\Controllers\Api\Controller;
use App\Models\Org;
use App\Models\Org\Enrollment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * 机构信息控制器
 */
class OrgController extends Controller
{
    /**
     * 我的机构列表
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=3b2562c8b9300f
     *
     * @return Collection
     */
    public function index(): Collection
    {
        $orgIds = Enrollment::query()->where('user_id', Auth::id())->pluck('org_id');

        if ($orgIds->isEmpty()) {
            throw new NotFoundHttpException('我的机构列表不存在。');
        }

        return Org::query()->publicFields()->whereIn('id', $orgIds)->get();
    }

    /**
     * 机构信息
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=37e667f370d00a
     */
    public function show(string $sid)
    {
        $org = Org::whereSid($sid)->publicFields()->first();
        if (!$org) {
            throw new NotFoundHttpException('机构不存在。');
        }
        return $org;
    }
}
