<?php

namespace App\Http\Controllers\Api\Org;

use App\Http\Controllers\Api\Controller;
use App\Jobs\WuXiHuiShanPeiXunInvoiceJob;
use App\Models\Org;
use App\Models\Org\Enroll;
use App\Models\Org\EnrollOperateRecord;
use App\Services\Common\OrderService;
use App\Services\Org\EnrollConfigService;
use App\Services\Org\EnrollOperateRecordService;
use App\Services\Org\EnrollService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Sqids\Sqids;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * 机构报名
 */
class EnrollController extends Controller
{
    /**
     * 机构报名首页
     * @param string $orgSid
     * @return array
     */
    public function index(string $orgSid)
    {
        /** @var Org $org */
        $org = Org::whereSid($orgSid)->publicFields()->first();
        if (!$org) {
            throw new NotFoundHttpException('机构不存在。');
        }
        // TODO::判断机构是否开通课程报名
        if (!$org->enable_enroll) {
            throw new NotFoundHttpException('机构暂未开通报名系统');
        }

        $baseUrl = 'https://hgb-pc.shiwusuo100.com/';
        if (app()->isLocal()) {
            $baseUrl = 'http://localhost:5182/';
        } elseif (app()->environment('testing')) {
            $baseUrl = 'https://hgb-pc.test.pp.cc/';
        }
        return [
            'logo' => $org->logo_url,
            'bg_img' => $org->enroll_bg_img_url,
            'org_name' => $org->alias,
            'invoiceEnabled' => $org->merchant_config['invoice_enabled'] ?? false,
            'enrollRecord' => app(EnrollService::class)->getEnrollListByOrgIdAndUserId($org->id, Auth::id()),
            'enrollConfig' => app(EnrollConfigService::class)->getListByOrgId($org->id)
                ->load('enrolls')
                ->map(function ($item) use ($org, $baseUrl) {
                    return [
                        'title' => $item->title,
                        'url' => $baseUrl . $org->sid . '/apply?org_enroll_config_id=' . $item->id,
                        'is_apply' => $item->enrolls->where('enroll_config_id', $item->id)->where('user_id', Auth::id())->where('status', '<>', Enroll::STATUS_REFUNDED)->count(),
                    ];
                })->toArray(),
        ];
    }

    /**
     * 报名支付
     * @param int $id
     * @return \App\Models\Order\Order
     */
    public function createOrder(int $id)
    {
        $enroll = app(EnrollService::class)->getEnrollById($id);

        // 判断机构是否可以进行支付
        $isEnableEnroll = $enroll?->org->enable_enroll;
        if (!$isEnableEnroll) {
            throw new \InvalidArgumentException('机构暂未开通报名系统');
        }
        $mch_id = $enroll?->org?->merchant_config['mch_id'] ?? 0;
        if (empty($mch_id)) {
            throw new \InvalidArgumentException('机构商户支付信息未配置');
        }
        $isEnable = $enroll?->org?->merchant_config['enabled'] ?? false;
        if (!$isEnable) {
            throw new \InvalidArgumentException('机构商户支付信息未启用');
        }

        if ($enroll->status != Enroll::STATUS_PENDING_PAYMENT) {
            throw new \InvalidArgumentException('报名信息状态异常');
        }

        // 指定微信支付模式以及子商户ID
        $extend = [
            'org_id' => $enroll->org_id,
            'wechat_pay_mode' => 'service', // 指定机构报名订单使用微信支付，服务商模式
            'sub_mch_id' => (new Sqids(minLength: 6))->encode([$mch_id]), // 加密后机构商户id
        ];

        return OrderService::create(Auth::id(), $enroll->enrollConfig, $enroll->enrollConfig->amount, $extend);
    }

    /**
     * 申请退款
     * @param int $id
     * @param Request $request
     * @return void
     */
    public function applyRefund(int $id, Request $request)
    {
        $params = $request->validate([
            'reason' => 'required|string'
        ]);
        $enroll = app(EnrollService::class)->getEnrollById($id);
        if ($enroll->status != Enroll::STATUS_PAID) {
            throw new \InvalidArgumentException('报名信息状态异常');
        }
        $enroll->status = Enroll::STATUS_REFUNDING;
        $enroll->save();
        app(EnrollOperateRecordService::class)->create(
            $enroll->id,
            EnrollOperateRecord::TYPE_APPLY_REFUND,
                0,
            $params['reason']
        );
    }

    /**
     * 撤销退款
     * @param int $id
     * @return void
     */
    public function cancelRefund(int $id)
    {
        $enroll = app(EnrollService::class)->getEnrollById($id);
        if ($enroll->status != Enroll::STATUS_REFUNDING) {
            throw new \InvalidArgumentException('报名信息状态异常');
        }
        $enroll->status = Enroll::STATUS_REFUND_CLOSED;
        $enroll->save();
        app(EnrollOperateRecordService::class)->create(
            $enroll->id,
            EnrollOperateRecord::TYPE_CANCEL_REFUND,
            0,
            '用户撤销申请退款'
        );
    }

    /**
     * 退款详情
     * @param int $id
     * @return array
     */
    public function refundDetail(int $id)
    {
        $enroll = app(EnrollService::class)->getEnrollById($id);
        return [
            'status' => $enroll->status,
            'status_text' => $enroll->status === Enroll::STATUS_REFUNDING ? '等待处理' : '退款关闭',
            'order_no' => $enroll?->order->order_no ?? '-',
            'amount' => $enroll?->order->payment_amount ?? '-',
            'title' => $enroll?->enrollConfig->title ?? '-',
            'reason' => $enroll?->operateRecords->where('type', EnrollOperateRecord::TYPE_APPLY_REFUND)->last()?->remark ?? '-',
            'records' => $enroll->operateRecords->where('type', '>=', EnrollOperateRecord::TYPE_APPLY_REFUND)->map(function ($item) {
                return [
                    'type' => $item->type,
                    'type_label' => $item->typeLabel,
                    'remark' => $item->remark,
                    'created_at' => $item->created_at->toDateTimeString(),
                ];
            })->toArray()

        ];
    }

    /**
     * 判断用户是否在机构下报名
     * @param string $orgId
     * @return array
     */
    public function enrollStatus(string $orgId)
    {
        $org = Org::whereSid($orgId)->publicFields()->first();
        if (!$org) {
            throw new NotFoundHttpException('机构不存在。');
        }

        $isEnrolled = Enroll::query()
            ->where('org_id', $org->id)
            ->where('user_id', Auth::id())
            ->whereIn('status', [Enroll::STATUS_PAID, Enroll::STATUS_REFUNDING, Enroll::STATUS_REFUND_CLOSED, Enroll::STATUS_REFUNDED])
            ->exists();

        return [
            'is_enrolled' => $isEnrolled,
        ];
    }

    /**
     * 开发票
     * @param int $id
     * @return \stdClass
     */
    public function invoice(int $id)
    {
        $enroll = app(EnrollService::class)->getEnrollById($id);
        if (!$enroll?->org?->merchant_config['invoice_enabled']) {
            throw new NotFoundHttpException('该机构未开启开具发票功能');
        }
        if ($enroll->is_invoiced) {
            throw new NotFoundHttpException('发票已开具，请查看短信');
        }
        dispatch(new WuXiHuiShanPeiXunInvoiceJob($id));
        return $this->ok();
    }
}
