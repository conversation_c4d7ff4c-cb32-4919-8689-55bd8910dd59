<?php

namespace App\Http\Controllers\Api;

use App\Services\User\TipService;
use Illuminate\Http\Request;

class TipController extends Controller
{

    /**
     * 获取当前用户的指定小提示状态
     */
    public function view(Request $request)
    {
        $params = $request->validate([
            'names' => 'required|string'
        ]);

        $tips = explode(',', $params['names']);
        $data = [];

        foreach ($tips as $t) {
            $data[$t] = TipService::has(auth()->user()->id, $t);
        }

        return $data;
    }

    /**
     * 设置当前用户的小提示
     */
    public function store(Request $request)
    {
        $params = $request->validate([
            'name' => 'required|string|min:1|max:128',
            'valid' => ['regex:/^(\d+|td|(\d+[ymwdhis])+)$/']
        ]);

        TipService::set(auth()->user()->id, $params['name'], $params['valid'] ?? 0);

        return new \stdClass();
    }

    /**
     * 删除用户指定的小提示
     */
    public function delete(Request $request)
    {
        $params = $request->validate([
            'name' => 'required|string|min:1|max:128'
        ]);

        TipService::remove(auth()->user()->id, $params['name']);

        return new \stdClass();
    }

}
