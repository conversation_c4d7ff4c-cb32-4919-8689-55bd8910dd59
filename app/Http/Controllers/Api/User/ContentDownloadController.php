<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Api\Controller;
use App\Models\Cms\Content;
use App\Models\User\UserContentDownload;
use App\Models\User\UserOwnContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ContentDownloadController extends Controller
{
    /**
     * 用户下载列表
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        $params = $this->validate($request, [
            'type' => 'string|in:' . implode(',', array_values(Content::$typeLabels))
        ]);

        $type = $params['type'] ?? '';
        $builder = UserOwnContent::query()
            ->select(['*', DB::raw("date_format(created_at,'%Y年%m月') AS created_date")])
            ->where('user_id', Auth::id())
            ->where('classify', 'material')
            ->with('content', function ($query) {
                $query->with('resource')->withOwnState(Auth::id());
            });
        if (!empty($type)) {
            $builder->whereHas('content', fn($query) => $query->where('type', array_search($type, Content::$typeLabels)));
        }
        $contents = $builder->orderByDesc('created_at')->get();
        $tempContentIds = [];
        $list = [];
        foreach ($contents as $item) {
            if (!in_array($item['content']['sid'], $tempContentIds)) {
                if (!empty($type)) {
                    $list[] = $item->content;
                } else {
                    $list[] = $item;
                }
                $tempContentIds[] = $item['content']['sid'];
            }
        }

        return $list;
    }
}
