<?php

namespace App\Http\Controllers\Api\User;

use App\Core\Enums\BusinessType;
use App\Http\Controllers\Api\Controller;
use App\Models\Order\Order;
use App\Models\User\UserCreditLog;
use App\Services\Common\OrderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CreditLogController extends Controller
{
    /**
     * 列表
     * @return void
     */
    public function index()
    {
        $typeMap = [UserCreditLog::TYPE_RECHARGE, UserCreditLog::TYPE_CONSUME];
        $params = \request()->validate([
            'type' => 'string|in:' . implode(',', $typeMap)
        ]);

        $userId = auth()->id();
        $builder = UserCreditLog::query()->with(['user' => function($query){
            $query->select(['id', 'nickname']);
        }])->where('user_id', $userId);

        if (!empty($params['type'])) {
            $builder->where('type', $params['type']);
        }

        $builder->orderBy('created_at', 'desc');

        $page = $builder->cursorPaginate();

        foreach ($page as $k => &$v){
            $v->makeHidden(['user_id', 'business_id']);
            $v->action_label = $v->business_type->modelLabel();
        }
        $page = $this->cursorPaginateToArray($page);
        return $page;

    }

    /**
     * 积分充值
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=d9e517b5-d9fd-4515-9a3e-09f3a9c60b12
     *
     * @param Request $request
     * @return Order
     */
    public function createOrder(Request $request): Order
    {
        $params = $this->validate($request, [
            'amount' => 'required|integer|min:1'
        ], [
            'amount.required' => '充值金额不能为空',
            'amount.integer' => '充值金额只能是整数',
            'amount.min' => '充值金额最小值为1元'
        ], ['amount' => '充值金额']);

        $userId = Auth::id();

        return OrderService::create($userId, new UserCreditLog(), $params['amount']);
    }
}
