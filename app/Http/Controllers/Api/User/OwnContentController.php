<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Api\Controller;
use App\Models\Cms\Category;
use App\Models\Cms\Content;
use App\Models\User\UserOwnContent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class OwnContentController extends Controller
{
    /**
     * 获取已购课程
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=*************-4478-98bc-1b9a46da38b5
     *
     * @param string $cateId
     */
    public function index(string $cateId)
    {
        if (!in_array($cateId, [Category::CLASSIFY_COURSE, Category::CLASSIFY_COURSE_PACK])) {
            throw new BadRequestHttpException('分类有误');
        }

        $uid = Auth::id();

        $userOwnContentIds = UserOwnContent::query()
            ->where('user_id', $uid)
            ->where('classify', $cateId)
            ->orderByDesc('id')
            ->groupBy('content_id')
            ->pluck(DB::raw('max(id) as id'));
        if ($userOwnContentIds->isEmpty()) {
            return [];
        }

        //通用的已购列表数据机制，通过 content 及 resource 关联，content 的 publicFields() 限制作用域，以及 resource 的 own() 作用域控制输出
        $list = UserOwnContent::query()
            ->where('classify', $cateId)
            ->where('user_id', $uid)
            ->whereIn('id', $userOwnContentIds->toArray())
            ->with([
                'content' => fn ($query) => $query->publicFields()->whereIn('status', [Content::STATUS_NORMAL, Content::STATUS_HIDE]),
                'content.resource' => fn ($query) => $query->own($uid)
            ])
            ->get();
        optional($list)->append(['is_expiration']);
        $list = $list->map(function ($item) {
            if ($item->content) {
                $item->content->is_expiration = $item->is_expiration;
                $item->content->expiration = Carbon::parse($item->expired_at)->format("Y-m-d H:i");
            }
            return $item;
        });
        $list = $list->pluck('content')
            ->filter()
            ->values()
            ->each(function ($item) {
                $item->resource?->ownAppend();
            });
        return $list;
    }
}
