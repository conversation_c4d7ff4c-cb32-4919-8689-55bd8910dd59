<?php

namespace App\Http\Controllers\Api\Cms;

use App\Http\Controllers\Api\Controller;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourseProgress;
use App\Models\Cms\ContentCourseSection;
use App\Models\Org;
use App\Models\Org\Enrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ContentCourseProgressController extends Controller
{
    /**
     * 获取进度
     *
     * @param string $contentId
     * @param string $sectionId
     * @param Request $request
     * @return array|int[]
     */
    public function show(string $contentId, string $sectionId, Request $request): array
    {
        $params = $request->validate([
            'org_sid' => 'string',
            'enroll_sid' => 'string'
        ]);

        $orgId = isset($params['org_sid']) ? Org::decodeSid($params['org_sid']) : 0;
        $enrollId = isset($params['enroll_sid']) ? Enrollment::decodeSid($params['enroll_sid']) : 0;

        $userId = Auth::id();
        if (!$userId) {
            return ['pos' => 0];
        }

        $pos = ContentCourseProgress::query()
            ->where('user_id', $userId)
            ->where('content_id', Content::decodeSid($contentId))
            ->where('section_id', ContentCourseSection::decodeSid($sectionId))
            ->where('org_id', $orgId)
            ->where('enroll_id', $enrollId)
            ->value('pos');
        if (!$pos) {
            return ['pos' => 0];
        }

        return ['pos' => $pos];
    }
}
