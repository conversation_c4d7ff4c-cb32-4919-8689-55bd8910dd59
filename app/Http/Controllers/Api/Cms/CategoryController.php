<?php

namespace App\Http\Controllers\Api\Cms;

use App\Http\Controllers\Api\Controller;
use App\Models\Cms\Category;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Collection;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class CategoryController extends Controller
{
    /**
     * 分类列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=4b8e95f9-15e0-415e-bd89-06cea49b0372
     *
     * @param Request $request
     * @return Collection
     */
    public function index(Request $request): Collection
    {
        $params = $this->validate($request, [
            'pid' => 'string',
            'classify' => 'string',
            'sub' => 'integer',
            'name' => 'string'
        ]);

        $pid = isset($params['pid']) ? Category::decodeSid($params['pid']) : 0;

        $builder = Category::query()
            ->select(['id', 'name'])
            ->where('pid', $pid)
            ->where('visible', Category::VISIBLE);

        if (isset($params['classify'])) {
            $builder->where('classify', $params['classify']);
        }

        if (isset($params['name'])) {
            $builder->where('name', $params['name']);
        }

        $list = $builder->orderByDesc('sort')->get();

        if (!isset($params['sub'])) {
            return $list;
        }

        $pidList = collect($list)->pluck('id')->toArray();
        $subList = Category::query()
            ->select(['id', 'pid', 'name'])
            ->whereIn('pid', $pidList)
            ->where('visible', Category::VISIBLE)
            ->orderByDesc('sort')
            ->get()
            ->append(['sid', 'logo_src']);
        if ($subList->isEmpty()) {
            return $list;
        }

        $tmpList = [];
        foreach ($subList as $item) {
            $tmpList[$item->pid][] = [
                'sid' => $item['sid'],
                'name' => $item['name'],
                'logo_src' => $item['logo_src']
            ];
        }

        foreach ($list as &$item) {
            if (isset($tmpList[$item->id])) {
                $item->sub = $tmpList[$item->id];
            }
        }
        unset($item);

        return $list;
    }

    /**
     * 获取同级和上级分类
     *
     * @param Request $request
     * @return array
     */
    public function supCategory(Request $request): array
    {
        $params = $this->validate($request, [
            'category_id' => 'required|string',
            'classify' => 'required|string'
        ]);

        $category = Category::whereSid($params['category_id'])
            ->where('classify', $params['classify'])
            ->where('visible', Category::VISIBLE)
            ->first();
        if (!$category) {
            throw new NotFoundHttpException("分类不存在");
        }

        $categories = Category::query()
            ->select(['id', 'name'])
            ->where('pid', $category->pid)
            ->where('classify', $params['classify'])
            ->where('visible', Category::VISIBLE)
            ->get();

        $supCategories = [];
        $pid = $category->pid;
        while ($pid > 0) {
            $subCategory = Category::publicFields()->where('id', $pid)->where('visible', Category::VISIBLE)->first();
            if (!$subCategory) {
                break;
            }
            $pid = $subCategory->pid;
            $supCategories[] = $subCategory->toArray();
        }
        $supCategories = array_reverse($supCategories);

        $subCategories = Category::publicFields()->where('pid', $category->id)->where('visible', Category::VISIBLE)->get();

        return [
            'categories' => $categories,
            'supCategories' => $supCategories,
            'subCategories' => $subCategories
        ];
    }
}
