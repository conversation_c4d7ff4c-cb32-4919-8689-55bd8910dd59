<?php

namespace App\Http\Controllers\Api\Cms;

use App\Core\Enums\BusinessType;
use App\Events\SearchEvent;
use App\Http\Controllers\Api\Controller;
use App\Http\Resources\ContentCollection;
use App\Models\Cms\Category;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseProgress;
use App\Models\Cms\ContentCourseSection;
use App\Models\Cms\ContentRelation;
use App\Models\Cms\Contract\ContentResourceInterface;
use App\Models\Order\Order;
use App\Models\Org;
use App\Models\Org\Enrollment;
use App\Models\Train\Topic;
use App\Models\User;
use App\Models\User\UserAttitude;
use App\Models\User\UserContentDownload;
use App\Services\Cms\CategoryService;
use App\Services\Cms\ContentCourseProgressService;
use App\Services\Cms\ContentCourseService;
use App\Services\Cms\ContentDocService;
use App\Services\Cms\ContentService;
use App\Services\Cms\SearchService;
use App\Services\Common\OrderService;
use App\Services\Org\CoursePackService;
use App\Services\Org\CourseService;
use App\Services\Org\OrgClassService;
use App\Services\Stat\DailyOverviewService;
use App\Services\Stat\DailyPromoterService;
use App\Services\User\OwnContentService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ContentController extends Controller
{
    /**
     * 内容列表（支持分页（游标）、分类筛选、文本搜索）
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=93d5e877-0c0e-483c-b017-9537e60e9400
     *
     * @param string $cateId 分类ID（可以是大分类，也可是具体分类）
     * @param Request $request
     * @return array|ContentCollection
     */
    public function index(string $cateId, Request $request): array|ContentCollection
    {
        $params = $this->validate($request, [
            'keywords' => 'string|max:50',
            'list_rows' => 'integer',
            'next_cursor' => 'string',
            'sid' => 'string',
            'release_at' => 'string',
            'type' => 'string|in:' . implode(',', Content::getSearchTypes())
        ]);

        $keywords = $params['keywords'] ?? '';
        $listRows = $params['list_rows'] ?? 20;
        $scrollId = $params['next_cursor'] ?? '';
        $type = $params['type'] ?? '';
        $sid = $params['sid'] ?? '';
        $releaseAt = $params['release_at'] ?? '';
        $data = ['next_cursor' => '', 'data' => [], 'category' => []];

        $subCateBuilder = Category::query()->where('visible', Category::VISIBLE);
        $categoryIds = CategoryService::getCategoryIds(['classify', '=', $cateId]);
        if (empty($categoryIds)) {
            $categoryPath = CategoryService::getPathBySid($cateId);
            if (empty($categoryPath)) {
                // 搜索关键词统计
                event(new SearchEvent($keywords));
                return $data;
            }
            $categoryIds = CategoryService::getCategoryIds(['path', 'like', $categoryPath . '%']);
            $subCateBuilder->where('pid', Category::decodeSid($cateId));
        } else {
            $subCateBuilder->where('classify', $cateId)->where('pid', 0);
        }

        // 查询条件
        $query = ['bool' => ['must' => [['term' => ['status' => Content::STATUS_NORMAL]]]]];
        if (!empty($categoryIds)) {
            $query['bool']['must'][] = ['terms' => ['category_id' => $categoryIds]];
        }

        // 关键词
        if (!empty($keywords)) {
            $query['bool']['must'][] = ['multi_match' => ['query' => $keywords, 'type' => 'cross_fields', 'fields' => ['title^5', 'intro']]];
            $number = filter_var($keywords, FILTER_SANITIZE_NUMBER_INT);
            if (!empty($number)) {
                $query['bool']['should'][] = ['regexp' => ['title' => ['value' => ".*$number.*", 'boost' => 5]]];
            }
        }

        // 类型
        if (!empty($type)) {
            if ($type != Content::$typeLabels[Content::TYPE_VIDEO]) {
                $query['bool']['must'][] = ['bool' => ['should' => [
                    ['match_phrase' => ['type' => ['query' => $type]]],
                    ['match_phrase' => ['doc_type' => ['query' => $type]]]
                ]]];
            } else {
                $query['bool']['must'][] = ['bool' => ['should' => [
                    ['match_phrase' => ['type' => ['query' => $type]]]
                ]]];
            }
        }

        if (!empty($releaseAt)) {
//            $query['bool']['must'][] = ['range' => ['release_at' => ['lte' => Carbon::parse($releaseAt)->toDateTimeString()]]];
        }

        if (config('heguibao.hidden_videos')) {
            $query['bool']['must'][] = ['terms' => ['type' => [Content::$typeLabels[Content::TYPE_DOC], Content::$typeLabels[Content::TYPE_RICH_TEXT]]]];
        }

        $sort = !empty($keywords) ? ['_score' => 'desc'] : ['release_at' => ['order' => 'desc']];
        // 获取es数据
        $elasticData = SearchService::getEsData(index: (new Content())->searchableAs(), query: $query, sort: $sort, listRows: $listRows, scrollId: $scrollId);
        // 获取es数据source
        $sources = SearchService::getEsSource($elasticData);
        if (empty($sources)) {
            // 搜索关键词统计
            event(new SearchEvent($keywords));
            return $data;
        }

        $userId = Auth::id();
        $contentIds = array_column($sources, 'id');

        // 包含当前内容和之后的内容
        if (!empty($sid)) {
            $contentIds = array_slice($contentIds, array_search(Content::decodeSid($sid), $contentIds));
        }

        $builder =  ContentService::getContentListBuilder($userId);
        $builder->whereIn('id', $contentIds);
        if (empty($keywords)) {
            $builder->orderByDesc('release_at');
        } else {
            $builder->orderByRaw("field(id," . implode(',', $contentIds) . ")");
        }
        $contents = $builder->get()->append('business_type')->toArray();
        $data['data'] = $contents;
        $data['next_cursor'] = $elasticData['_scroll_id'] ?: '';

        // 获取当前分下的子分类
        $categories = $subCateBuilder->pluck('name', 'id')->toArray();
        foreach ($categories as $categoryId => $categoryName) {
            $data['category'][] = [
                'sid' => Category::encodeId($categoryId),
                'name' => $categoryName
            ];
        }

        // 统计搜索数据
        DailyOverviewService::increment('search');

        // 搜索关键词统计
        event(new SearchEvent($keywords));

        $data['data'] = new ContentCollection($data['data']);

        return $data;
    }

    /**
     * 最新内容列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=e38760f1-8bd7-4299-83e3-574536ee9678
     *
     * @param string $cateId 分类ID
     * @param Request $request
     * @return ContentCollection
     */
    public function recent(string $cateId, Request $request): ContentCollection
    {
        $params = $this->validate($request, [
            'type' => 'string|in:' . implode(',', array_values(Content::$typeLabels))
        ]);

        $builder = ContentService::getContentListBuilder(Auth::id());
        if (isset($params['type'])) {
            $builder->where('type', array_search($params['type'], Content::$typeLabels));
        }

        $cateIds = CategoryService::getCategoryIds(['classify', '=', $cateId]);
        if (!empty($cateIds)) {
            $builder->whereIn('category_id', $cateIds);
        } else {
            $builder->where('category_id', Category::decodeSid($cateId));
        }

        $list = $builder->orderByDesc('release_at')->limit(20)->get()->append('business_type');

        return new ContentCollection($list);
    }

    /**
     * 热门内容列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=47ab980c-1901-4830-bc53-52b15b6dc697
     *
     * @param string $cateId
     * @param Request $request
     * @return ContentCollection
     */
    public function hot(string $cateId, Request $request): ContentCollection
    {
        $params = $this->validate($request, [
            'type' => 'string|in:' . implode(',', array_values(Content::$typeLabels))
        ]);
        $userId = Auth::id();
        $builder = ContentService::getContentListBuilder($userId);
        if (isset($params['type'])) {
            $builder->where('type', array_search($params['type'], Content::$typeLabels));
        }

        $cateIds = CategoryService::getCategoryIds(['classify', '=', $cateId]);
        if (!empty($cateIds)) {
            $builder->whereIn('category_id', $cateIds);
        } else {
            $builder->where('category_id', Category::decodeSid($cateId));
        }

        $hots = [];
        $viewContent = $builder->orderByDesc('views')->first()->append('business_type')->toArray();
        if ($viewContent) {
            $hots[] = $viewContent;
        }

        $now = Carbon::now();
        $beforeWeek = Carbon::now()->subWeek();

        $downloads = UserContentDownload::query()
            ->select(['content_id', \DB::raw('count(content_id) as count_content')])
            ->whereBetween('created_at', [$beforeWeek, $now])
            ->groupBy('content_id')
            ->orderByDesc('count_content')
            ->limit(2)
            ->get();
        if ($downloads->isNotEmpty()) {
            if (!empty($hots)) {
                $hotSids = collect($hots)->pluck('sid')->toArray();
                $hotIds = [];
                foreach ($hotSids as $hotSid) {
                    $hotIds[] = Content::decodeSid($hotSid);
                }
            }
            $downloadContentIds = array_diff(collect($downloads)->pluck('content_id')->toArray(), $hotIds);
            if (!empty($downloadContentIds)) {
                $builder = ContentService::getContentListBuilder($userId);
                if (isset($params['type'])) {
                    $builder->where('type', array_search($params['type'], Content::$typeLabels));
                }
                $downloadContents = $builder->whereIn('id', $downloadContentIds)
                    ->orderByDesc('release_at')
                    ->orderByDesc('id')
                    ->get()
                    ->append('business_type');
                $hots = array_merge($hots, $downloadContents->toArray());
            }
        }

        $attitudes = UserAttitude::query()
            ->select(['business_id', \DB::raw('count(business_id) as count_content')])
            ->where('attitude', UserAttitude::ATTITUDE_LIKE)
            ->where('business_type', BusinessType::CmsMaterial)
            ->whereBetween('created_at', [$beforeWeek, $now])
            ->groupBy('business_id')
            ->orderByDesc('count_content')
            ->limit(2)
            ->get();
        if ($attitudes->isNotEmpty()) {
            if (!empty($hots)) {
                $hotSids = collect($hots)->pluck('sid')->toArray();
                $hotIds = [];
                foreach ($hotSids as $hotSid) {
                    $hotIds[] = Content::decodeSid($hotSid);
                }
            }
            $attitudeIds = array_diff(collect($attitudes)->pluck('business_id')->toArray(), $hotIds);
            if (!empty($attitudeIds)) {
                $builder = ContentService::getContentListBuilder($userId);
                if (isset($params['type'])) {
                    $builder->where('type', array_search($params['type'], Content::$typeLabels));
                }
                $attitudeContents = $builder->whereIn('id', $attitudeIds)
                    ->orderByDesc('release_at')
                    ->orderByDesc('id')
                    ->get()
                    ->append('business_type');
                $hots = array_merge($hots, $attitudeContents->toArray());
            }
        }

        if (empty($hots) || count($hots) < 5) {
            $hotSids = collect($hots)->pluck('sid')->toArray();
            $hotIds = [];
            foreach ($hotSids as $hotSid) {
                $hotIds[] = Content::decodeSid($hotSid);
            }
            $materials = ContentService::getContentListBuilder($userId)
                ->orderByDesc('release_at')
                ->orderByDesc('id')
                ->limit(20)
                ->get()
                ->append('business_type');
            $materialIds = array_diff(collect($materials)->pluck('id')->toArray(), $hotIds);
            if (!empty($materialIds)) {
                $builder = ContentService::getContentListBuilder($userId);
                if (isset($params['type'])) {
                    $builder->where('type', array_search($params['type'], Content::$typeLabels));
                }
                $attitudeContents = $builder->whereIn('id', $materialIds)
                    ->orderByDesc('release_at')
                    ->orderByDesc('id')
                    ->get()
                    ->append('business_type');
                $hots = array_slice(array_merge($hots, $attitudeContents->toArray()), 0, 5);
            }
        }

        return new ContentCollection($hots);
    }

    /**
     * 推荐资料列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=a9e5b69e-997d-4beb-8540-ecb2c71c9739
     *
     * @param string $cateId 分类ID
     * @param Request $request
     * @return ContentCollection
     */
    public function recommend(string $cateId, Request $request): ContentCollection
    {
        $params = $this->validate($request, [
            'sub_cate_id' => 'string',
            'list_rows' => 'integer',
            'type' => 'string|in:' . implode(',', array_values(Content::$typeLabels))
        ]);

        $listRows = $params['list_rows'] ?? 5;
        $userId = Auth::id();

        $builder = ContentService::getContentListBuilder($userId)->whereNotNull('recommend_at');

        if (in_array($cateId, Content::$typeLabels)) {
            $cateIds = CategoryService::getCategoryIds(['classify', '=', $cateId]);
            if ($cateIds) {
                $builder->whereIn('category_id', $cateIds);
            } else {
                $builder->where('type', array_search($cateId, Content::$typeLabels));
            }
        } else {
            $cateId = Category::decodeSid($cateId);

            $category = Category::query()
                ->where('id', $cateId)
                ->publicFields()
                ->where('visible', Category::VISIBLE)
                ->first();

            if ($category) {
                if (in_array($category->name, Category::appointCategory())) {
                    $categoryPath = isset($params['sub_cate_id']) ? CategoryService::getPathBySid($params['sub_cate_id']) : $category->path;
                    if (empty($categoryPath)) {
                        throw new NotFoundHttpException("分类路径不存在");
                    }
                    $categoryIds = CategoryService::getCategoryIds(['path', 'like', $categoryPath . '%']);
                    if (empty($categoryIds)) {
                        throw new NotFoundHttpException("分类不存在");
                    }
                    $builder = ContentService::getContentListBuilder($userId)->whereIn('category_id', $categoryIds);

                    if (isset($params['type'])) {
                        $builder->where('type', array_search($params['type'], Content::$typeLabels));
                    }
                } else {
                    $builder->where('category_id', $category->id);
                }
            }
        }

        if (isset($params['type'])) {
            $builder->where('type', array_search($params['type'], Content::$typeLabels));
        }

        $list = $builder->orderByDesc('recommend_at')->limit($listRows)->get()->append('business_type');

        return new ContentCollection($list);
    }

    /**
     * 内容详情
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=4b7bb98
     *
     * @param string $contentId 内容ID
     * @param Request $request
     * @return Content
     */
    public function show(string $contentId, Request $request): Content
    {
        $params = $request->validate([
            'org_sid' => 'string',
            'enroll_sid' => 'string'
        ]);

        /** @var Content $content */
        $content = Content::whereSid($contentId)
            ->whereIn('status', [Content::STATUS_NORMAL, Content::STATUS_HIDE])
            ->when(config('heguibao.hidden_videos'), fn($query) => $query->whereIn('type', [Content::TYPE_DOC, Content::TYPE_RICH_TEXT]))
            ->publicFields()
            ->addSelect('admin_id', 'status')
            ->first();

        if (!$content) {
            throw new NotFoundHttpException('内容不存在');
        }

        $userId = Auth::id();
        $content->makeHidden('status');

        //机构信息处理
        $orgId = !empty($params['org_sid']) ? Org::decodeSid($params['org_sid']) : 0;
        $enrollment = null;

        if (!empty($params['enroll_sid'])) {
            //不能在非登录状态下调用 enroll
            if (!$userId) {
                throw new AccessDeniedHttpException('您尚未登录，无法获取学习数据。');
            }

            $enrollment = Enrollment::whereSid($params['enroll_sid'])->where('user_id', $userId)->first();

            if (!$enrollment) {
                throw new BadRequestHttpException('获取学习数据失败');
            }

            $orgId = $enrollment->org_id;

        } elseif ($orgId && $userId) {
            //在从机构列表进入相关课程内容时，此时应聚集于一个学习中的 enroll
            $enrollment = Enrollment::query()->where('user_id', $userId)->where('org_id', $orgId)->where('status', Enrollment::STATUS_LEARNING)->first();
        }

        $content->forUser($userId, $enrollment?->id ?? 0)->append(['attitude', 'favorite', 'business_type', 'download', 'purchase']);

        if ($orgId) {
            if ($content->getBusinessType() == BusinessType::CmsCourse && Org\Course::query()->where('course_id', $content->id)->exists()) {
                $content->load(['resource' => fn($query) => $query->org($orgId, $content->id)]);

                list($chapterIds, $sectionIds) = CourseService::orgCourseChapSecIds($orgId, $content->id);
                $content->resource->chapters_count = count($chapterIds);
                $content->resource->sections_count = count($sectionIds);

            } else {
                $content->load(['resource' => fn($query) => $query->detail($content->download)]);
            }

            $content->resource->detailAppend(['enrollment' => $enrollment, 'content' => $content]);
            $content->resource->orgAppend($orgId, $enrollment?->id ?? 0);
        } else {
            $content->load(['resource' => fn($query) => $query->detail($content->download)]);
            $content->resource->detailAppend(['content' => $content]);
        }

        if ($content->getBusinessType() == BusinessType::CmsCourse) {
            $content->course_process = ContentCourseProgressService::progress($userId, $content->id);
        }

        if ($orgId > 0 && $content->getBusinessType() == BusinessType::CmsCoursePack) {
            $content->resource->hour = CoursePackService::calCoursePackHour($orgId, $content->id);
        }

        if ($orgId > 0 && in_array($content->getBusinessType(), [BusinessType::CmsCourse, BusinessType::CmsCoursePack])) {
            $content->resource->charageAmountAppend($orgId);
            $content->charge_amount = $content->resource->charage_amount;
            $content->topic_id = '';

            if ($userId) {
                // 获取关联题库
                $topic_id = $content->resource->topic_id;

                if ($topic_id && $userId) {
                    $content->topic_id = Topic::encodeId( $topic_id);
                }
            }

            $content->resource->setHidden(['topic_id', 'content_id']);
        }

        // 统计内容浏览数
        $content->increment('views');

        // 资料浏览统计
        DailyOverviewService::increment('content_view');
        DailyPromoterService::increment($content->admin_id);

        return $content;
    }

    /**
     * 相关资源
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=f37fe791-2d63-40b2-83b9-c62cddcb0d1a
     *
     * @param string $contentId 内容ID
     * @return Collection
     */
    public function relation(string $contentId): Collection
    {
        $contentIds = ContentRelation::query()
            ->where('content_id', Content::decodeSid($contentId))
            ->orderByDesc('created_at')
            ->limit(100)
            ->pluck('related_id')
            ->toArray();
        if (empty($contentId)) {
            return Collection::empty();
        }

        return ContentService::getContentListBuilder()
            ->whereIn('id', $contentIds)
            ->limit(10)
            ->orderByDesc('release_at')
            ->get()
            ->append('business_type');
    }

    /**
     * 积分购买
     *
     * @param string $contentId 内容ID
     * @return Content
     */
    public function buyCredit(string $contentId): Content
    {
        $content = ContentService::getContentById($contentId);

        if (!$content) {
            throw new NotFoundHttpException("资料不存在");
        }

        $userId = Auth::id();
        if (OwnContentService::checkPurchase($userId, [$content->id])) {
            throw new BadRequestHttpException("已购买过该资料，无需重复购买");
        }

        if (!in_array($content->view_limit, [Content::VIEW_LIMIT_CREDIT, Content::VIEW_LIMIT_CREDIT_AMOUNT])) {
            throw new BadRequestHttpException("支付类型错误");
        }

        try {
            return ContentService::buyContentByCredit($userId, $content);
        } catch (\Throwable $e) {
            if ($e->getCode() == 403) {
                throw new AccessDeniedHttpException(User::getUserCredit($userId) ?: '');
            } else {
                throw new BadRequestHttpException($e->getMessage());
            }
        }
    }

    /**
     * 创建订单
     *
     * @param string $contentId 内容ID
     * @param Request $request
     * @return Order
     */
    public function createOrder(string $contentId, Request $request): Order
    {
        $params = $request->validate([
            'org_sid' => 'string',
            'enroll_sid' => 'string'
        ]);

        $orgSid = $params['org_sid'] ?? null;
        $content = ContentService::getContentById($contentId);

        if (!$content) {
            throw new NotFoundHttpException("资料不存在");
        }

        $userId = Auth::id();
        if (OwnContentService::checkPurchase($userId, [$content->id])) {
            throw new BadRequestHttpException("已购买过该资料，无需重复购买");
        }

        if (!in_array($content->view_limit, [Content::VIEW_LIMIT_AMOUNT, Content::VIEW_LIMIT_CREDIT_AMOUNT])) {
            throw new BadRequestHttpException("支付类型错误");
        }

        $extend = null;
        $amount = $content->charge_amount;

        if ($orgSid) {
            $orgId = Org::whereSid($orgSid)->value('id');

            if (!$orgId) {
                throw new BadRequestHttpException("机构不存在");
            }

            $extend = ['org_id' => $orgId];

            if (in_array($content->getBusinessType(), [BusinessType::CmsCourse, BusinessType::CmsCoursePack])) {
                $content->resource->charageAmountAppend($orgId);
                $amount = $content->resource->charage_amount;
            }
        }

        return OrderService::create($userId, $content, $amount, extend: $extend);
    }
}
