<?php

namespace App\Http\Controllers\Api\Cms;

use App\Http\Controllers\Api\Controller;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCourseDoc;
use App\Models\Cms\ContentCourseProgress;
use App\Models\Cms\ContentCourseProgressBuffer;
use App\Models\Cms\ContentCourseSection;
use App\Models\Org;
use App\Models\Org\Enrollment;
use App\Models\Train\Topic;
use App\Services\Cms\ContentCourseProgressService;
use App\Services\Cms\ContentCourseSectionService;
use App\Services\Cms\ContentDocService;
use App\Services\Org\CourseService;
use App\Services\Org\LearnCaptureService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ContentCourseSectionController extends Controller
{
    /**
     * 视频章节详情
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=4bc0bcd
     *
     * @param string $contentId 内容ID
     * @param string $sectionId 视频章节-节ID
     * @param Request $request
     * @return Content
     */
    public function show(string $contentId, string $sectionId, Request $request): Content
    {
        $params = $request->validate([
            'org_sid' => 'string'
        ]);

        $orgId = isset($params['org_sid']) ? Org::decodeSid($params['org_sid']) : null;
        $sectionId = ContentCourseSection::decodeSid($sectionId);

        // 详情
        $content = Content::whereSid($contentId)
            ->publicFields()
            ->whereIn('status', [Content::STATUS_NORMAL, Content::STATUS_HIDE])
            ->where('type', Content::TYPE_COURSE)
            ->first();

        if (!$content) {
            throw new NotFoundHttpException("内容不存在");
        }

        $builder = ContentCourse::publicFields()
            ->with('docs', fn ($query) => $query->orderByDesc('sort'))
            ->where('content_id', $content->id);
        if ($orgId) {
            $course = $builder->org($orgId, $content->id, 'detailFields')->first();
            $content->resource->orgAppend($orgId, 0);// todo 停用接口，复用这里需要传参，不能用0
            $content->charge_amount = $content->resource->charage_amount;
            $course->chapters->each(function (&$chapter) {
                foreach ($chapter->sections as &$section) {
                    // 关联单视频需改变视频相关字段输出
                    if ($section->video) {
                        $section->duration = $section->video->duration;
                        $section->filepath = $section->video->filepath;
                        $section->extend = $section->video->extend;
                    }
                    unset($section->video);
                }
            });
        } else {
            $course = $builder->with('chapters', function ($query) {
                $query->publicFields()->where('status', ContentCourseChapter::STATUS_SHOW)->orderByDesc('sort');
            })->first();
        }

        if (!$course) {
            throw new NotFoundHttpException("课程不存在");
        }

        $course->docs->makeHidden(['id', 'content_id']);

        $userId = Auth::id();
        $content->forUser($userId)->append(['attitude', 'download', 'favorite', 'business_type']);

        foreach ($course->docs as &$doc) {
            $doc->file_type = ContentDocService::getFileType($doc->filepath);
            if (!$content->download) {
                $doc->filepath = '';
                $doc->filepath_src = '';
            }
        }

        $chapterIds = collect($course->chapters)->pluck('id');
        $sections = ContentCourseSection::detailFields()
            ->with('video')
            ->where('content_id', $content->id)
            ->whereIn('chapter_id', $chapterIds)
            ->where('status', ContentCourseSection::STATUS_SHOW)
            ->orderByDesc('sort')
            ->get();
        if ($sections->isEmpty()) {
            throw new NotFoundHttpException("章节课程不存在");
        }

        if (!in_array($sectionId, collect($sections)->pluck('id')->toArray())) {
            throw new NotFoundHttpException("当前章节不存在");
        }

        $sectionArr = [];
        foreach ($sections as &$section) {
            // 关联单视频需改变视频相关字段输出
            if ($section->video) {
                $section->duration = $section->video->duration;
                $section->filepath = $section->video->filepath;
                $section->extend = $section->video->extend;
            }
            unset($section->video);
            $sectionArr[$section->chapter_id][] = $section;
        }
        unset($section);

        $tryViewCount = 0;
        foreach ($course->chapters as &$chapter) {
            if (isset($sectionArr[$chapter->id])) {
                $orgId ?? $chapter->sections = $sectionArr[$chapter->id];
                foreach ($chapter->sections as $section) {
                    if (!$content->download) {// 未付费
                        if ($tryViewCount < $course->try_view_count) {// 在试看数量内
                            $section->see = 2;
                            $tryViewCount++;
                        } else {
                            $section->see = 0;
                            $section->video_src = "";
                            $section->filepath = "";
                            $section->filepath_src = "";
                            $section->extend = [];
                        }
                    } else {
                        $section->see = 1;
                    }
                    $section->current = $sectionId == $section->id ? 1 : 0;
                }
                //todo 此处只是作为兼容，后续全面上线后需要删除此代码
                $chapter->section = $chapter->sections;
            }
        }
        unset($chapter);

        $content->course = $course;

        // 获取关联题库
        $topic = Topic::query()->where('course_content_id', $content->id)->first();
        if ($topic) {
            $content->topic_id = $topic->sid;
        }

        return $content;
    }

    /**
     * 视频章节详情
     *
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=25b4a35e793204
     * @param string $contentId
     * @param string $sectionId
     * @param Request $request
     */
    public function v2Show(string $contentId, string $sectionId, Request $request)
    {
        $params = $request->validate([
            'org_sid' => 'string',
            'enroll_sid' => 'string'
        ]);

        $orgId = isset($params['org_sid']) ? Org::decodeSid($params['org_sid']) : null;
        $sectionId = ContentCourseSection::decodeSid($sectionId);

        if (isset($params['enroll_sid'])) {
            $enrollment = Enrollment::whereSid($params['enroll_sid'])->first();
            if (!$enrollment) {
                throw new BadRequestHttpException("学员报名信息不存在");
            }
        } else {
            $enrollment = null;
        }

        // 详情
        $content = Content::whereSid($contentId)
            ->publicFields()
            ->with('resource', fn ($query) => $query->publicFields())
            ->whereIn('status', [Content::STATUS_NORMAL, Content::STATUS_HIDE])
            ->where('type', Content::TYPE_COURSE)
            ->first();

        if (!$content) {
            throw new NotFoundHttpException("内容不存在");
        }

        // 课程当前章节
        $section = ContentCourseSection::detailFields()
            ->with('video')
            ->where('content_id', $content->id)
            ->where('id', $sectionId)
            ->where('status', ContentCourseSection::STATUS_SHOW)
            ->first();

        if (!$section) {
            throw new NotFoundHttpException("章节课程不存在");
        }

        if ($section->video) {
            $section->duration = $section->video->duration;
            $section->filepath = $section->video->filepath;
            $section->extend = $section->video->extend;
        }

        $userId = Auth::id();
        $content->forUser($userId, $enrollment?->id ?? 0)->append(['attitude', 'download', 'favorite', 'business_type']);

        // 课程拍照二维码
        if ($content->download && $enrollment) {
            $scene = LearnCaptureService::getSceneByDetail($enrollment, $content->id, $sectionId);
            if (!empty($scene)) {
                throw new NotAcceptableHttpException("需要抓拍才能继续学习", headers: ['X-Scene' => $scene]);
            }
        }

        $course = ContentCourse::publicFields()
            ->where('content_id', $section->content_id)
            ->with('chapters', function ($query) {
                $query->publicFields()
                    ->with(['sections' => function ($query) {
                        $query->publicFields()->where('status', ContentCourseSection::STATUS_SHOW)->orderbyDesc('sort');
                    }])
                    ->where('status', ContentCourseChapter::STATUS_SHOW)
                    ->orderByDesc('sort');
            })
            ->first();

        // 获取当前节在课程章节的位置
        $tryViewSectionIds = [];
        foreach ($course->chapters as $chapter) {
            foreach ($chapter->sections as $chapterSection) {
                if (count($tryViewSectionIds) < $content->resource->try_view_count) {
                    $tryViewSectionIds[] = $chapterSection->id;
                }
            }
        }

        if (!$content->download) {// 未付费
            if (in_array($sectionId, $tryViewSectionIds)) {
                $section->see = 2;
            } else {
                $section->see = 0;
                $section = ContentCourseSectionService::setEmptySrc($section);
            }
        } else {
            $section->see = 1;
        }

        $section->progress = '正在学习';

        if ($userId) {
            $pos = ContentCourseProgress::query()
                ->where('user_id', $userId)
                ->where('content_id', $content->id)
                ->where('section_id', $sectionId)
                ->where('org_id', $enrollment ? $enrollment->org_id : 0)
                ->where('enroll_id', $enrollment ? $enrollment->id : 0)
                ->value('pos') ?? 0;
        } else {
            $pos = 0;
        }

        $section->pos = $pos;

        return $section;
    }

    /**
     * 更新视频进度
     *
     * @param Request $request
     * @param string $sectionId 视频章节-节ID
     */
    public function update(Request $request, string $sectionId)
    {
        $params = $this->validate($request, [
            'type' => 'required|string|in:play,update',
            'view_position' => 'required|integer|min:0',
            'enroll_sid' => 'string'
        ]);

        $user = Auth::user();

        if (isset($params['enroll_sid'])) {
            $enrollment = Enrollment::whereSid($params['enroll_sid'])
                ->where('user_id', $user->id)
                ->first();

            //参数不能乱传
            if (!$enrollment) {
                throw new BadRequestHttpException('请求参数错误');
            }
        } else {
            $enrollment = null;
        }

        // 避免跨端播放
        ContentCourseProgressService::avoidCrossPlay($user);

        $section = ContentCourseSection::whereSid($sectionId)
            ->with('video')
            ->where('status', ContentCourseSection::STATUS_SHOW)
            ->first();

        if (!$section) {
            throw new NotFoundHttpException("视频不存在");
        }

        // 检查是否要抓拍
        if ($enrollment) {
            $scene = LearnCaptureService::getSceneByProgress($enrollment, $section->content_id, $section->id);
            if (!empty($scene)) {
                throw new NotAcceptableHttpException("需要抓拍才能继续学习", headers: ['X-Scene' => $scene]);
            }
        }

        if ($params['type'] == 'play') {
            $section->increment('play_count');
        }

        $orgId = $enrollment ? $enrollment->org_id : 0;
        $enrollId = $enrollment ? $enrollment->id : 0;
        $condition = [
            'user_id' => $user->id,
            'section_id' => $section->id,
            'org_id' => $orgId,
            'enroll_id' => $enrollId
        ];


        $lockKey = "progress_lock:{$user->id}:{$section->id}:{$orgId}:{$enrollId}";
        $lock = \Cache::lock($lockKey, 3); // 3秒自动释放

        if ($lock->get()) {
            try {
                /** @var ContentCourseProgress $progress */
                $progress = ContentCourseProgress::where($condition)->first();

                if ($progress) {
                    $sectionDuration = $section->actual_duration;

                    if ($params['type'] == 'update') {
                        $progress->increment('duration', ContentCourseProgress::$playDuration);

                        $validDuration = $progress->valid_duration + ContentCourseProgress::$playDuration;
                        $progress->valid_duration = $validDuration >= $sectionDuration ? $sectionDuration - 1 : $validDuration;
                    }

                    $finished = ContentCourseProgress::playComplete($progress->valid_duration, $params['view_position'], $sectionDuration);

                    if (!$progress->finished) {
                        $progress->finished = $finished;
                    }
                } else {
                    $progress = new ContentCourseProgress();
                    $progress->user_id = $user->id;
                    $progress->content_id = $section->content_id;
                    $progress->chapter_id = $section->chapter_id;
                    $progress->section_id = $section->id;
                    $progress->org_id = $orgId;
                    $progress->enroll_id = $enrollId;
                    $progress->duration = $params['type'] == 'update' ? ContentCourseProgress::$playDuration : 0;
                    $progress->finished = false;
                }

                // 小节首次完成，注意 isDirty() 必须在 save() 前调用
                $finishedHere = $progress->finished && $progress->isDirty('finished');
                
                $progress->pos = $params['view_position'];
                $progress->updated_at = Carbon::now();

                if ($finishedHere) {
                    $section->increment('play_complete_count');
                    $progress->valid_duration++;
                }

                $progress->save();

                $buffer = ContentCourseProgressBuffer::query()->firstOrNew([
                    'org_id' => $progress->org_id,
                    'user_id' => $progress->user_id,
                    'content_id' => $progress->content_id,
                    'enroll_id' => $progress->enroll_id,
                    'section_id' => $progress->section_id,
                ]);

                if ($params['type'] == 'update') {
                    $buffer->increment('update_count');
                }

                $buffer->save();

                return ['message' => 'success'];

            } finally {
                $lock->release();
            }
        } else {
            // 获取锁失败，说明有并发请求正在处理，忽略本次请求直接返回成功
            logger()->info("获取锁失败，忽略本次请求", ['lockKey' => $lockKey]);
            return ['message' => 'success'];
        }
    }

    /**
     * 下载课程资料
     *
     * @param string $contentId 内容sid
     * @param string $docId 课程资料sid
     * @return string[]
     */
    public function docDownload(string $contentId, string $docId): array
    {
        $content = Content::whereSid($contentId)->first();
        if (!$content) {
            throw new NotFoundHttpException("内容不存在");
        }

        $doc = ContentCourseDoc::whereSid($docId)->first();
        if (!$doc) {
            throw new NotFoundHttpException("课程资料不存在");
        }

        $doc->increment('download_count');

        return ['message' => 'success'];
    }
}
