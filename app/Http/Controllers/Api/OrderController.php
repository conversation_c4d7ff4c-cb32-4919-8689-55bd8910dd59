<?php

namespace App\Http\Controllers\Api;

use App\Models\Order\Order;
use App\Services\User\CreditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OrderController extends Controller
{
    /**
     * 订单列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=034d46d1-724e-4eaf-8a2e-b7947c310921
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        $params = $this->validate($request, [
            'list_rows' => 'integer',
            'next_cursor' => 'string'
        ]);

        $listRows = $params['list_rows'] ?? 20;
        $user_id = Auth::id();
        $orders = Order::query()
            ->with('resource', function ($query) {
                $query->publicFields();
            })
            ->orderByDesc('created_at')
            ->where('user_id', $user_id)
            ->where('status', Order::STATUS_PAID)
            ->cursorPaginate(perPage: $listRows, cursor: $params['next_cursor'] ?? null);

        $list = $this->cursorPaginateToArray($orders);
        if (!empty($list['data'])) {
            foreach ($list['data'] as &$item) {
                if ($item['business_type'] == 'credit') {
                    $item['credit'] = CreditService::calcAmountCredit($item['total_amount']);
                }
            }
        }

        return $list;
    }

    /**
     * 订单详情
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=1bf67bd6b6c196
     */
    public function show($orderNo)
    {
        $userId = Auth::id();
        $order = Order::query()
            ->where('user_id', $userId)
            ->where('order_no', $orderNo)
            ->first();

        if (!$order){
            throw new NotFoundHttpException("订单不存在");
        }

        return $order;
    }


    /**
     * 删除订单
     * @param $orderNo
     * @return array
     */
    public function del($orderNo)
    {
        $uid = Auth::id();
        $order = Order::query()
            ->where('user_id', $uid)->where('order_no', $orderNo)->first();
        if (!$order){
            throw new NotFoundHttpException("订单不存在");
        }
        $order->delete();
        return [];
    }
}
