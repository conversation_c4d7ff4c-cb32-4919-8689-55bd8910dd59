<?php

namespace App\Http\Controllers\Api\Ers;

use App\Http\Controllers\Api\Controller;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\Industry;
use App\Services\Ers\ProjectService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class IndustryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): array
    {
        $params = $this->validate($request, [
            'project_id' => 'required|integer',
            'enterprise_id' => 'required|integer'
        ]);

        $project = ProjectService::getProject($params['project_id']);

        if (!$project) {
            throw new NotFoundHttpException("项目不存在");
        }

        $builder = FormProjectForm::query()->where('project_id', $project->id);
        if ($params['enterprise_id'] > 0) {
            $builder->where('enterprise_id', $params['enterprise_id']);
        }
        $industryIds = $builder->get()->pluck('industry_id')->unique()->toArray();
        if (empty($industryIds)) {
            return [];
        }

        return Industry::publicFields()
            ->whereIn('id', $industryIds)
            ->orderBy('sort')
            ->get()
            ->toArray();
    }
}
