<?php

namespace App\Http\Controllers\Api\Ers;

use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Models\Ers\SolutionOrderDownload;
use App\Services\Ers\Modules\SolutionDownloadModule;
use App\Services\Ers\ServiceOrderStepService;
use Illuminate\Http\Request;

class SolutionOrderDownloadController extends ServiceOrderStepController
{
    /**
     * 创建工单方案下载数据
     *
     * @param string $sid
     * @param int $flowStepId
     * @param Request $request
     * @return array
     */
    public function store(string $sid, int $flowStepId, Request $request): array
    {
        $params = $this->validate($request, [
            'email' => 'required|email'
        ]);

        $order = $this->getOrder($sid);
        $orderStep = $this->getStep($order, $flowStepId, SolutionDownloadModule::configure()->t);

        // 创建工单方案下载数据
        $download = new SolutionOrderDownload();
        $download->order_id = $order->id;
        $download->order_step_id = $orderStep->id;
        $download->email = $params['email'];
        $download->save();

        $orderStep->data_id = $download->id;
        $orderStep->status = ServiceOrderStep::STATUS_ADMIN_PENDING;
        $orderStep->save();

        $order->status = ServiceOrder::STATUS_ADMIN_PENDING;
        $order->save();

        ServiceOrderStepService::forward($order, $orderStep);

        return $orderStep->toStepArray() + [
                'solution_download' => SolutionDownloadModule::convertOrderStepData($order, $orderStep),
            ];
    }

    /**
     * 获取工单方案下载数据
     *
     * @param string $sid
     * @param int $flowStepId
     * @return array
     */
    public function show(string $sid, int $flowStepId): array
    {
        $order = $this->getOrder($sid);
        $orderStep = $this->getStep($order, $flowStepId, SolutionDownloadModule::configure()->t);

        return $orderStep->toStepArray() + [
                'solution_download' => SolutionDownloadModule::convertOrderStepData($order, $orderStep),
            ];
    }
}
