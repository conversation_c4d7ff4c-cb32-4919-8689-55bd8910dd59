<?php

namespace App\Http\Controllers\Api\Ers;

use App\Http\Controllers\Api\Controller;
use App\Models\Ers\FlowStep;
use App\Models\Ers\FormOrderForm;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\FormProjectInput;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Services\Common\AttachmentService;
use App\Services\Ers\FormOrderDataService;
use App\Services\Ers\FormProjectFormService;
use App\Services\Ers\FormProjectInputService;
use App\Services\Ers\Modules\FormModule;
use App\Services\Ers\ProjectService;
use App\Services\Ers\ServiceOrderService;
use App\Services\Ers\ServiceOrderStepService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ServiceOrderFormController extends Controller
{

    /**
     * 预览指定选项表单项
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=21113dd6f6c24f
     */
    public function preview(Request $request)
    {
        $params = $this->validate($request, [
            'project_id' => 'int|required',
            'industry_id' => 'int|required',
            'enterprise_id' => 'int|required'
        ]);

        /** @var FormProjectForm $form */
        $form = FormProjectForm::query()
            ->where('project_id', $params['project_id'])
            ->where('industry_id', $params['industry_id'])
            ->where('enterprise_id', $params['enterprise_id'])
            ->first();

        if (!$form) {
            throw new NotFoundHttpException('该表单项不存在。');
        }

        $desc = [];

        foreach ($form->inputs as $input) {
            $intro = match ($input->type) {
                'select', 'checkbox' => '选择'.$input->title,
                'image', 'files' => '上传'.$input->title,
                default => '填写'.$input->title
            };

            if ($intro && $input->desc) {
                $intro .= " ({$input->desc})";
            }

            $desc[] = $intro;
        }

        return compact('desc');
    }

    /**
     * 获取某个项目的表单结构
     */
    public function inputs(Request $request)
    {
        $params = $this->validate($request, [
            'project_id' => 'int|required',
            'step_id' => 'int|required',
            'industry_id' => 'int',
            'enterprise_id' => 'int'
        ]);

        $project = ProjectService::getProject($params['project_id']);

        if (!$project) {
            throw new NotFoundHttpException('项目不存在');
        }

        $step = FlowStep::query()
            ->where('id', $params['step_id'])
            ->where('flow_id', $project->flow_id)
            ->where('module', FormModule::configure()->t)
            ->first()
            ?->makeHidden(['flow_id', 'created_at', 'updated_at']);

        if (!$step) {
            throw new NotFoundHttpException('参数不正确。');
        }

        $inputs = FormModule::projectInputs($project->id, $params['step_id'], $project->is_bind_category, $params['industry_id'] ?? 0, $params['enterprise_id'] ?? 0);

        return $step->toArray() + [
            'form' => ['inputs' => $inputs]
        ];
    }

    /**
     * 表单流程提交数据
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=16b48fb8b6c054
     *
     * @param string $sid 工单sid
     * @param int $flowStepId 流程ID
     * @param Request $request
     * @return array
     * @throws \Throwable
     */
    public function store(string $sid, int $flowStepId, Request $request)
    {
        $params = $this->validate($request, [
            'inputs' => 'required|array|min:1',
        ]);

        $userId = Auth::id();

        $order = ServiceOrderService::getOrder($sid, $userId);
        if (!$order) {
            throw new NotFoundHttpException("工单不存在");
        }

        // 获取项目表单
        $projectForm = FormProjectFormService::getProjectForm($order->project_id, $flowStepId, $order->industry_id, $order->enterprise_id);
        if (!$projectForm) {
            throw new NotFoundHttpException("项目表单不存在");
        }

        // 项目表单项验证
        FormProjectInputService::validateInputs($params['inputs'], $projectForm);

        DB::beginTransaction();

        /** @var ServiceOrderStep $orderStep */
        $orderStep = $order->steps
            ->where('module', FormModule::configure()->t)
            ->where('step_id', $flowStepId)
            ->first();

        // 创建/更新工单表单
        /** @var FormOrderForm $orderForm */
        $orderForm = FormOrderForm::query()->updateOrCreate([
            'order_id' => $order->id,
            'project_form_id' => $projectForm->id,
            'order_step_id' => $orderStep->id
        ]);

        // 创建/更新工单表单项数据
        FormOrderDataService::createdOrUpdatedFormOrderData($params['inputs'], $projectForm, $orderForm);

        // 更新工单流程状态
        $orderStep->status = ServiceOrderStep::STATUS_ADMIN_PENDING;
        $orderStep->data_id = $orderForm->id;
        $orderStep->last_user_handled_at = now();
        $orderStep->save();

        ServiceOrderStepService::forward($order, $orderStep);

        DB::commit();

        // 格式化输出
        $inputs = FormModule::convertOrderStepData($order, $orderStep);

        return $orderStep->toStepArray() + ['form' => ['inputs' => $inputs]];
    }

    /**
     * 获取工单表单流程
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=16aaaa84b6c022
     *
     * @param string $sid 工单ID
     * @param int $flowStepId 流程ID
     * @return array
     */
    public function show(string $sid, int $flowStepId): array
    {
        $userId = Auth::id();

        $order = ServiceOrderService::getOrder($sid, $userId);
        if (!$order) {
            throw new NotFoundHttpException("工单不存在");
        }

        /** @var ServiceOrderStep $step */
        $step = $order->steps
            ->where('module', FormModule::configure()->t)
            ->where('step_id', $flowStepId)
            ->first();
        if (!$step) {
            throw new NotFoundHttpException("工单流程不存在");
        }

        $inputs = FormModule::convertOrderStepData($order, $step);

        return $step->toStepArray() + [
            'form' => ['inputs' => $inputs]
        ];
    }

    /**
     * 上传配置项
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=1bd383103e212b
     *
     * @param Request $request
     * @return array
     */
    public function uploadConfig(Request $request)
    {
        $params = $this->validate($request, [
            'input_id' => 'required|string'
        ]);

        $input = FormProjectInput::find($params['input_id']);
        if (!$input) {
            throw new NotFoundHttpException("项目表单项不存在");
        }

        if (!in_array($input->type, [FormProjectInput::TYPE_IMAGE, FormProjectInput::TYPE_FILE])) {
            throw new BadRequestHttpException("非上传表单类型");
        }

        $diskName = config('heguibao.storage.priv');
        $maxSizeKB = $input->options['size'];
        if ($input->type == FormProjectInput::TYPE_FILE) {
            $ext = $input->options['ext'];
            if (is_array($ext)) {
                if (in_array('*', $ext)) {
                    $fileTypes = '*';
                } else {
                    $fileTypes = $ext;
                }
            } else {
                $fileTypes = $ext;
            }
        } else {
            $fileTypes = $input->type;
        }

        return AttachmentService::getUploadForm(Auth::id(), $fileTypes, maxSizeKB: $maxSizeKB, diskName: $diskName);
    }
}
