<?php

namespace App\Http\Controllers\Api\Ers;

use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Services\Ers\Modules\SolutionPreviewModule;
use App\Services\Ers\ServiceOrderStepService;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class SolutionOrderPreviewController extends ServiceOrderStepController
{
    /**
     * 确认预览方案
     *
     * @param string $sid
     * @param int $flowStepId
     * @return array
     */
    public function store(string $sid, int $flowStepId): array
    {
        $order = $this->getOrder($sid);
        $orderStep = $this->getStep($order, $flowStepId, SolutionPreviewModule::configure()->t);

        if (!$orderStep->data_id || !$orderStep->data) {
            throw new BadRequestHttpException('尚不可确认预览，还未到该流程。');
        }

        // 更新工单流程状态
        $orderStep->status = ServiceOrderStep::STATUS_FINISH;
        $orderStep->last_user_handled_at = now();
        $orderStep->finished_by = ServiceOrderStep::FINISHED_BY_USER;
        $orderStep->save();

        // 推进工单步骤
        ServiceOrderStepService::forward($order, $orderStep);

        // 获取下一流程
        $next = ServiceOrderStepService::nextStep($orderStep);

        return $orderStep->toStepArray() + [
            'solution_previews' => SolutionPreviewModule::convertOrderStepData($order, $orderStep),
            'next' => $next?->toStepArray(),
        ];
    }

    /**
     * 预览方案
     *
     * @param string $sid
     * @param int $flowStepId
     * @return array
     */
    public function show(string $sid, int $flowStepId): array
    {
        $order = $this->getOrder($sid);
        $orderStep = $this->getStep($order, $flowStepId, SolutionPreviewModule::configure()->t);

        if (!$orderStep->data_id || !$orderStep->data) {
            throw new BadRequestHttpException('尚不可预览，还未到该流程。');
        }

        return $orderStep->toStepArray() + [
            'solution_previews' => SolutionPreviewModule::convertOrderStepData($order, $orderStep)
        ];
    }
}
