<?php

namespace App\Http\Controllers\Api\Ers;

use App\Http\Controllers\Api\Controller;
use App\Models\Ers\FlowStep;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\Project;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Services\Ers\FlowStepService;
use App\Services\Ers\ServiceOrderService;
use App\Services\Ers\ServiceOrderStepService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ServiceOrderController extends Controller
{
    /**
     * 创建工单
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=16699b4876c008
     */
    public function store(Request $request)
    {
        $params = $this->validate($request, [
            'project_id' => 'required|integer',
            'industry_id' => 'integer',
            'enterprise_id' => 'integer'
        ]);

        /** @var Project $project */
        $project = Project::query()->find($params['project_id']);
        if (!$project) {
            throw new NotFoundHttpException("该服务不存在");
        }

        $steps = FlowStep::query()->where('flow_id', $project->flow_id)->orderBy('step')->get();
        if ($steps->isEmpty()) {
            throw new NotFoundHttpException("服务流程不存在");
        }

        $industryId = $params['industry_id'] ?? 0;
        $enterpriseId = $params['enterprise_id'] ?? 0;
        $builder = FormProjectForm::query()->where('project_id', $project->id);
        if ($project->is_bind_category) {
            $builder->where('industry_id', $industryId)->where('enterprise_id', $enterpriseId);
        }
        $form = $builder->exists();
        if (!$form) {
            throw new NotFoundHttpException("项目表单不存在");
        }

        $userId = Auth::id();

        // 创建工单
        $order = ServiceOrderService::createOrder($userId, $project, $industryId, $enterpriseId, $steps);

        // 获取工单流程
        $flows = ServiceOrderService::getFlows($order, withSubFlows: true);

        return ['sid' => $order->sid, 'flows' => $flows];
    }

    /**
     * 工单详情
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=16d3509636c145
     *
     * @param string $sid
     * @param Request $request
     * @return ServiceOrder
     */
    public function show(string $sid, Request $request): ServiceOrder
    {
        $params = $this->validate($request, [
            'project_id' => 'required|integer'
        ]);

        $userId = Auth::id();

        $order = ServiceOrder::whereSid($sid)
            ->where('user_id', $userId)
            ->where('project_id', $params['project_id'])
            ->with([
                'industry' => fn($query) => $query->publicFields(),
                'enterprise' => fn($query) => $query->publicFields(),
                'project' => fn($query) => $query->publicFields(),
            ])
            ->first()
            ?->makeHidden(['project_id', 'flow_id', 'steps', 'user']);

        if (!$order) {
            throw new NotFoundHttpException("该工单不存在");
        }

        $order->flows = ServiceOrderService::getFlows($order, true, true);

        return $order;
    }
}
