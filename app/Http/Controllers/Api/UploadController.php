<?php

namespace App\Http\Controllers\Api;

use App\Libs\Filesystem\LocalUploadAdapter;
use App\Libs\Filesystem\UploadAdapterFactory;
use App\Services\Common\AttachmentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class UploadController extends Controller
{

    public function store(Request $request)
    {
        $localUploadAdapter = new LocalUploadAdapter();
        $data = $localUploadAdapter->response($request, 'callback');

        Cache::set('uploaded-file:' . $data['key'], [
            'filename' => $data['filename'],
            'size' => $data['size'],
            'mime' => $data['mime']
        ], 3600);

        return $data;
    }

    /**
     * 获取上传的文件信息
     *
     * 这个不是标准接口，主要用于上传文件后跳转信息或回调信息的获取。
     *
     * 上传文件的回跳必须与其它路由地址不同，不能光使用 GET 请求方式区分，
     * 比如在小程序下当上传七牛配置 returnUrl 回跳时，小程序会保持之前上传的 POST 方式来回调路由，而网页等其它端则没此问题，
     * 所以必须要靠路由地址来区分，且路由允许 GET、POST、PUT 多种方式，以兼容小程序端。
     */
    public function info(Request $request)
    {
        //这里只验证 GET 参数，不能用 Laravel 验证器
        $disk = $request->get('disk', '');
        $type = $request->get('type');

        if (!$type || !in_array($type, ['callback', 'return'])) {
            throw new BadRequestHttpException('Type 参数错误');
        }

        $uploadAdapter = UploadAdapterFactory::create($disk ?: null);

        if ($uploadAdapter instanceof LocalUploadAdapter) {
            throw new BadRequestHttpException('Unavailable for local disk.');
        } else {
            $data = $uploadAdapter->response($request, $type);
        }

        if (!str_starts_with($data['key'], AttachmentService::TMP_DIR)) {
            throw new BadRequestHttpException('不能获取该 key 的信息。');
        }

        $key = $disk . ':' . $data['key'];

        Cache::set('uploaded-file:' . $key, [
            'filename' => $data['filename'],
            'size' => $data['filesize'],
            'mime' => $data['mime']
        ], 3600);

        return [
            'url' => $url ?? AttachmentService::url($uploadAdapter->getDisk(), $data['key']),
            'filename' => $data['filename'],
            'key' => $disk . ':' . $data['key'],
            'size' => $data['filesize'],
            'mime' => $data['mime']
        ];
    }

}
