<?php

namespace App\Http\Controllers\Api;

use App\Libs\AsyncTasks\AsyncTaskType;
use App\Libs\AsyncTasks\QiniuVideoTranscodeTask;
use App\Models\AsyncTask;
use App\Services\Common\AsyncTaskService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Overtrue\Flysystem\Qiniu\QiniuAdapter;

/**
 * 七牛视频转码/持久化任务回调
 */
class QiniuCallbackController extends Controller
{

    public function transcode(Request $request)
    {
        if (!$this->verifyCallback($request)) {
            Log::error("七牛回调验证失败：{$request->getContent()}", [
                'headers' => $request->header()
            ]);
            return ['error'=>'回调验证失败!'];
        }

        $ret = $request->post();

        /** @var AsyncTask $taskModel */
        $taskModel = AsyncTask::query()
            ->where('task_id', $ret['id'])
            ->where('type', AsyncTaskType::QiniuVideoTranscode)
            ->first();

        if (!$taskModel) {
            Log::error("七牛回调任务不存在", ['task_id'=>$ret['id']]);
            return ['error'=>'回调任务不存在!'];
        }

        try {
            AsyncTaskService::checkCallback($taskModel, fn() => QiniuVideoTranscodeTask::taskStatusCheck($ret) ? $ret : false);
        } catch (\Throwable $e) {
            Log::error("七牛任务处理异常：{$e}", ['task_id'=>$ret['id']]);
            return ['error'=>$e->getMessage()];
        }

        return ['error'=>false];
    }

	/**
	 * 检查七牛回调是否通过
	 *
	 * @return bool
	 */
	private function verifyCallback(Request $request)
	{
        /** @var QiniuAdapter $adapter */
        $adapter = Storage::disk(QiniuVideoTranscodeTask::DISK_NAME)->getAdapter();
        $auth = $adapter->getAuthManager();

		//验证回调请求
		$authorization = $request->header('Authorization');
		$contentType = $request->header('Content-Type');
		$requestBody = file_get_contents('php://input');

        $currentUrl = app()->isLocal() ? 'http://requestbin.cn/15n960i1' : $request->fullUrl();

        $auth->options['disableQiniuTimestampSignature'] = true;
        $sign = $auth->authorizationV2($currentUrl, $request->getMethod(), $requestBody, $request->header('content-type'));

        if ($sign['Authorization'] !== $authorization) {
			Log::error("七牛回调验证失败: {CONTENT_TYPE=$contentType, AUTHORIZATION=$authorization, URL=$currentUrl, BODY=$requestBody}");
			return false;
        }

		return true;
	}

}
