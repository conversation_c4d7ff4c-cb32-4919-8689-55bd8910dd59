<?php

namespace App\Http\Controllers\Api\Qa;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Http\Controllers\Api\Controller;
use App\Models\Qa\Answer;
use App\Models\Qa\Question;
use App\Models\User;
use App\Services\Cms\SearchService;
use App\Services\Common\AttachmentService;
use App\Services\HuaWei\ModerationService;
use App\Services\Qa\QuestionService;
use App\Services\Qa\TagService;
use App\Services\User\TipService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class QuestionController extends Controller
{

    /**
     * 问题列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=ca8b5caf-45e9-43c3-a703-a5d269d8d657
     * @return array
     */
    public function questionsIndex()
    {
        $params = $this->validate(request(), [
            'list_rows' => 'integer',
            'next_cursor' => 'string',
            'tag_id' => 'integer'
        ]);

        $scrollId = $params['next_cursor'] ?? '';
        $listRows = 20;
        $data = ['next_cursor' => '', 'data' => []];

        // 查询条件
        $query = [
            'bool' => [
                'filter' => [
                    [
                        'term' => [
                            'status' => Question::STATUS_NORMAL
                        ]
                    ]
                ]
            ]
        ];

        if (!empty($params['tag_id'])) {
            $query['bool']['filter'][] = [
                'term' => [
                    'tag_ids' => $params['tag_id']
                ]
            ];
        }

        $sort = [
            'updated_at' => [
                'order' => 'desc'
            ],
            'id' => [
                'order' => 'desc'
            ]
        ];

        // return $query;

        $elasticData = SearchService::getEsData((new Question())->searchableAs(), query: $query, sort: $sort, listRows: $listRows, scrollId: $scrollId);

        // 获取es数据source
        $sources = SearchService::getEsSource($elasticData);
        if (empty($sources)) {
            return $data;
        }

        $data['next_cursor'] = $elasticData['_scroll_id'];
        $questionIds = array_column($sources, 'id');
        $uid = Auth::id();
        $page = Question::query()
            ->select(['id', 'content', 'answer_count', 'anonymous', 'user_id', 'images', 'created_at', 'updated_at'])
            ->where('status', Question::STATUS_NORMAL)
            ->withCount(['attitudes' => function ($query) use ($uid) {
                $query->where('attitude', User\UserAttitude::ATTITUDE_LIKE);
            }])
            ->with('user', function ($query) {
                $query->select(['id', 'nickname', 'avatar']);
            })
            ->withLikeState($uid)
            ->with('tags', fn($query) => $query->publicFields(true))
            ->orderByDesc('updated_at')
            ->whereIn('id', $questionIds);

        $page = $page->get();
        foreach ($page as $k => &$v) {
            $v->append('sid');
            $v->makeHidden(['user_id']);
        }
        $page->makeHidden('images')->append('image_urls');

        $data['data'] = $page;
        return $data;
    }

    /**
     * 我的提问
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=e9bfe757-77a9-48ed-b587-1666e0f37f06
     * @return array
     * @throws ServiceException
     */
    public function myQuestionsIndex()
    {
        $uid = \Auth::id();
        $page = Question::query()
            ->where('status', Question::STATUS_NORMAL)
            ->orderByDesc('id')
            ->orderByDesc('created_at')
            ->where('user_id', $uid)
            ->select(['id', 'title', 'content', 'answer_count', 'created_at']);
        $page = $page->cursorPaginate();
        foreach ($page as $k => &$v) {
            $v->isAnswer = false;
            if (TipService::has($uid, "tip-answer-details-" . $v->id)) {
                $v->isAnswer = true;
            }

            $v->append('sid');
        }
        TipService::remove($uid, "tip-answer-me-page");
        $page = $this->cursorPaginateToArray($page);
        return $page;
    }

    /**
     * 搜索问题
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=51dc8df3-8cba-4262-984c-a6e16902a324
     */
    public function search()
    {
        $params = request()->validate([
            'content' => ['required', 'string'],
            'scroll_id' => 'string'
        ]);
        $scrollId = $params['scroll_id'] ?? '';
        $keyword = $params['content'] ?? '';
        $listRows = 20;

        $count = 0;

        // 查询条件
        $query = [];

        $query['bool']['must'][$count]['multi_match']['query'] = $keyword;
        $query['bool']['must'][$count]['multi_match']['type'] = "cross_fields";
        $query['bool']['must'][$count]['multi_match']['fields'] = ['content'];

        $data = ['scroll_id' => '', 'data' => []];

        $elasticData = SearchService::getEsData(index: (new Question())->searchableAs(), query: $query, listRows: $listRows, scrollId: $scrollId);

        $data['scroll_id'] = $elasticData['_scroll_id'];

        $sources = SearchService::getEsSource($elasticData);
        $sources = collect($sources);

        $contentIds = $sources->pluck('id');

        $uid = Auth::id();
        $page = Question::query()
            ->select(['id', 'content', 'answer_count', 'anonymous', 'user_id', 'images', 'created_at', 'updated_at'])
            ->where('status', Question::STATUS_NORMAL)
            ->withCount(['attitudes' => function ($query) use ($uid) {
                $query->where('attitude', User\UserAttitude::ATTITUDE_LIKE);
            }])
            ->with('user', function ($query) {
                $query->select(['id', 'nickname', 'avatar']);
            })
            ->with('tags', fn($query) => $query->publicFields(true))
            ->orderByDesc('updated_at')
            ->whereIn('id', $contentIds);

        $page->withLikeState($uid);

        $page = $page->get();
        foreach ($page as $k => &$v) {
            $v->append('sid');
            $v->makeHidden(['user_id']);
            //$v->is_like = $v->attitude ? true : false;
        }
        $page->append('image_urls');

        $data['data'] = $page;
        return $data;

    }

    /**
     * 创建问题
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=870e4da2-79cc-418a-9d29-18cd71c63048
     * @return Question
     */
    public function store()
    {
        $params = request()->validate([
            'content' => ['string', 'required', 'max:1000'],
            'title' => ['string', 'max:50', 'min:6'],
            'is_anonymous' => ['required', 'boolean'],
            'images' => ['json']
        ]);

        $user = Auth::user();

        if ($user->status == User::STATUS_MUTE) {
            throw new AccessDeniedHttpException('您已被禁言。');
        }

        $question = new Question([
            'user_id' => $user->id,
            'reply_at' => now(),
            'title' => $params['title'] ?? '',
            'content' => $params['content'],
            'anonymous' => $params['is_anonymous'] ? 1 : 0
        ]);

        // 请求华为云内容审核接口
        $res = app(ModerationService::class)->contentCheck($question->title . ' ' . $question->content);
        if ($res['result']['suggestion'] != 'pass') {
            throw new BadRequestHttpException('您输入的内容中包含敏感词汇，请重新检查并修改，确保内容合规后再试');
        }

        $question->status = $res['result']['suggestion'] == 'pass' ? Question::STATUS_NORMAL : Question::STATUS_REJECTED;

        DB::beginTransaction();

        try {
            //因为后续还有关联的数据要更新，此时不便直接同步到搜索引擎，当相应关联处理完成后再推送
            $question->withoutSyncingToSearch(fn() => $question->save());

            //从内容中匹配中话题/标签
            $tagNames = TagService::matchTagsFromText($params['content']);

            //生成标签关联
            if ($tagNames) {
                $tags = TagService::retrieveTags($tagNames);
                foreach ($tags as $tag) {
                    $tag->addRelation($question->id);
                }
            }

            DB::commit();

            //临时图片存储并没有事务保证，所以图片的处理不要放进事务中，否则会造成文件被移到正式区却没有记录
            $uploadedImages = !empty($params['images']) ? json_decode($params['images'], true) : [];

            if ($uploadedImages) {
                $images = [];
                foreach ($uploadedImages as $uploadKey) {
                    $file = AttachmentService::store($uploadKey, 'question', BusinessType::Question, $question->id);
                    $images[] = [
                        'path' => $file->path,
                        'id' => $file->id
                    ];
                }
                $question->images = $images;
                $question->withoutSyncingToSearch(fn() => $question->save());
            }

            $question->searchable();
            QuestionService::questionCredit($user->id, $question->id);
            return $question;

        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error("创建问答出错，原因：" . $e);
            throw new BadRequestHttpException($e->getMessage());
        }
    }

    /**
     * 问题详情
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=977af296-1c73-40e9-b21e-12e7e806a89b
     */
    public function show($questionId, Request $request)
    {
        /** @var Question $question */
        $question = Question::whereSid($questionId)
            ->where('status', Question::STATUS_NORMAL)
            ->first();

        if (!$question) {
            throw new NotFoundHttpException('问题不存在');
        }

        $uid = Auth::id();
        $token = $this->visitorToken ?: $uid ?: $request->getClientIp();

        QuestionService::updateViews($question, $token);

        // HEAD 用于其它途径的更新阅读计数，但不会返回数据
        //为了减轻 HEAD 无用的逻辑，上方的代码尽可能少的加载数据
        if ($request->method() == 'HEAD') {
            return $this->ok();
        }

        $question->load([
            'tags' => fn($query) => $query->publicFields(true),
            'attitude' => fn($query) => $query->where('user_id', $uid)->where('attitude', User\UserAttitude::ATTITUDE_LIKE),
        ]);

        $question->loadCount([
            'attitudes' => fn($query) => $query->where('attitude', User\UserAttitude::ATTITUDE_LIKE)
        ]);

        if ($question->anonymous) {
            $user = new User();
            $user->nickname = '匿名用户';
            $user->uuid = '';
            $user->avatar = '';
            $question->user = $user;
        } else {
            $question->load([
                'user' => fn($query) => $query->select(['id', 'uuid', 'nickname', 'avatar'])
            ]);
        }

        $question->is_like = $question->attitude ? true : false;
        $question->append('image_urls');

        if ($uid && TipService::has($uid, "tip-answer-details-" . $question->id)) {
            TipService::remove($uid, "tip-answer-details-" . $question->id);
        }

        return $question;
    }

    /**
     * 删除问题
     * @return void
     */
    public function delete($sid)
    {
        $uid = auth()->id();
        $question = Question::whereSid($sid)->where('user_id', $uid)->first();
        if (!$question) {
            throw new NotFoundHttpException('问题不存在');
        }

        Answer::query()
            ->where('question_id', $question->id)
            ->delete();

        $question->delete();
        return [];
    }

    /**
     * 提问上传 Token
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=2b9c639b-baac-4b17-97c0-93835fc89818
     */
    public function uploadForm()
    {
        $diskName = config('heguibao.storage.pub');
        return AttachmentService::getUploadForm('question', 'image', 1024 * 10, $diskName);
    }

}
