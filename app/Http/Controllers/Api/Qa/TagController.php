<?php

namespace App\Http\Controllers\Api\Qa;

use App\Http\Controllers\Api\Controller;
use App\Models\Qa\Question;
use App\Models\Qa\Tag;
use App\Models\Qa\TagRelation;
use Elasticsearch\Client;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class TagController extends Controller
{

    /**
     * 标签列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=9a138681-487f-4560-80fc-3d9fa6de6ed0
     */
    public function index(Request $request)
    {
        $params = $this->validate($request, [
            'q' => 'string',
            'recommend' => 'boolean',
            'cursor' => 'string'
        ]);

        $recommend = $params['recommend'] ?? false;
        $q = isset($params['q']) ? trim($params['q']) : '';

        /** @var \Illuminate\Database\Eloquent\Builder $query */
        $query = Tag::query()
            ->publicFields()
            ->orderByDesc('recommend_at')
            ->orderByDesc('id');

        if ($recommend) {
            $query->whereNotNull('recommend_at');
        }

        if ($q) {
            $query->where('name', 'like', "%$q%");
        }

        return $this->cursorPaginateToArray(
            $query->cursorPaginate(20, ['id'], cursor: $params['cursor'])
        );
    }

    /**
     * 标签详情
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=f609e581-0204-4f08-b92e-e986d8279009
     */
    public function show($id)
    {
        $tag = Tag::query()
            ->where('id', $id)
            ->first();

        if (!$tag) {
            throw new NotFoundHttpException('话题不存在');
        }

        $query = Question::search(
            '',
            fn(Client $elastic, $_, $searchParams) => $elastic->search([
                'index' => $searchParams['index'],
                'body' => [
                    'query' => [
                        'bool' => [
                            'filter' => [
                                [
                                    'term' => [
                                        'tag_ids' => $id
                                    ]
                                ],
                                [
                                    'term' => [
                                        'status' => Question::STATUS_NORMAL
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'aggs' => [
                        'total_views' => [
                            'sum' => [
                                'field' => 'views'
                            ]
                        ]
                    ]
                ],
                'size' => 0
            ])
        );

        $data = $query->raw();
        $tag->views = $data['aggregations']['total_views']['value'];
        $tag->total = $data['hits']['total']['value'];

        return $tag;
    }

}
