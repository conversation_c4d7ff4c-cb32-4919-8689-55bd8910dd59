<?php

namespace App\Http\Controllers\Api\Train;

use App\Http\Controllers\Api\Controller;
use App\Models\Org;
use App\Models\Org\Enrollment;

/**
 * 我的班级
 */
class ClassesController extends Controller
{

    /**
     * 我的班级列表（在指定机构下）
     */
    public function myClasses($sid)
    {
        $orgId = Org::decodeSid($sid);

        $enrollments = Enrollment::query()
            ->select(['id', 'class_id'])
            ->where('user_id', auth()->id())
            ->where('org_id', $orgId)
            ->with('classroom', fn($query) => $query->select(['id', 'name', 'type', 'start_at', 'end_at']))
            ->get();

        return $enrollments->pluck('classroom');
    }

}
