<?php

namespace App\Http\Controllers\Api;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Cms\Content;
use App\Models\Cms\ContentDoc;
use App\Models\Cms\Special;
use App\Models\Invitation;
use App\Models\Qa\Answer;
use App\Models\Qa\Question;
use App\Models\Train\Subject;
use App\Models\Train\Topic;
use App\Models\User\UserFavorite;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class FavoriteController extends Controller
{
    /**
     * 我的收藏列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=971d44d5-1cf1-4187-87bc-7d8b8065955e
     * @return array
     */
    public function index()
    {
        $params = request()->validate([
            'business_type' => ['string'],
        ]);
        $uid = \Auth::id();
        $favorite = UserFavorite::query()
            ->where('user_id', $uid)
            ->whereIn('business_type', [BusinessType::CmsMaterial, BusinessType::CmsNews, BusinessType::CmsCourse, ])
            ->orderByDesc('id');
        if (!empty($params['business_type'])){
            $favorite->where('business_type', $params['business_type']);
        }
        $favorite->with(['resource'=>function($query){
            //$query->favorite();//模型作用域
        }]);
        $page = $favorite->cursorPaginate();
        foreach ($page as $k => &$v) {
            if (empty($v->resource)) {
                unset($page[$k]);
                continue; // 跳过后续处理
            }
            $v->content_doc = ContentDoc::query()->where('content_id', $v->resource->id)->first(['page_count', 'format']);
            if (empty($v->content_doc)) {
                unset($page[$k]);
                continue; // 跳过后续处理
            }
            $v->makeHidden(['business_id', 'id', 'user_id']);
            $v->resource->append('sid');
        }
        $paginate = $page->toArray();
        $paginate['data'] =  array_values($paginate['data']);
        return [
            'data' => $paginate['data'],
            'next_cursor' => $paginate['next_cursor'],
        ];
    }


    /**
     * 添加收藏列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=2b01adc3-e89d-4a21-a909-1b8be576582f
     * @return array
     * @throws ServiceException
     */
    public function store($businessType, $businessId)
    {
        try{
            $business = BusinessType::from($businessType);
        } catch (\ValueError $e) {
            throw new BadRequestHttpException($businessType."数据错误");
        }
        $query = $business->modelClass();
        $businessId = $query::decodeSid($businessId);

        $uid = \Auth::id();
        $data = $query::query()->where('id', $businessId)->first();
        if (!$data){
            throw new ServiceException("保存失败，内容不存在");
        }

        $favorite = UserFavorite::query()
            ->where('business_type', $businessType)
            ->where('business_id', $data->id)
            ->where('user_id', $uid)
            ->first();
        if ($favorite){
            throw new BadRequestHttpException("保存失败，已收藏");
        }
        UserFavorite::query()->create(['business_type' => $businessType, 'business_id'=> $data->id, 'user_id' => $uid]);
        return [];
    }

    /**
     * 收藏删除
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=4da2da5c-35f0-44c7-a428-fbec7a080b7e
     * @return void
     */
    public function destroy($businessType, $businessId)
    {
        try{
            $business = BusinessType::from($businessType);
        } catch (\ValueError $e) {
            throw new ServiceException($businessType."数据错误");
        }
        $query = $business->modelClass();
        $businessId = $query::decodeSid($businessId);

        $uid = \Auth::id();

        $favorite = UserFavorite::query()
            ->where('business_type', $businessType)
            ->where('business_id', $businessId)
            ->where('user_id', $uid)
            ->first();
        if ($favorite){
            $favorite->delete();
        }
        return [];
    }
}
