<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\Controller;
use App\Services\User\VisitorService;
use Illuminate\Http\Request;

class VisitorController extends Controller
{

    /**
     * 初始化访客 Token
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=c7a6e63a-30a6-4c5d-a75b-e0ae9a4947e1
     */
    public function index(Request $request)
    {
        $visitor = VisitorService::linkVisitor($this->visitorToken, auth()->id());

        return [
            'token' => $visitor->token
        ];
    }

}
