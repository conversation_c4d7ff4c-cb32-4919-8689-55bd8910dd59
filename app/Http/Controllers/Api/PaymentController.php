<?php

namespace App\Http\Controllers\Api;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Order\Order;
use App\Models\Order\Payment;
use App\Models\Order\Refund;
use App\Models\Org\Enroll;
use App\Models\Org\EnrollOperateRecord;
use App\Services\Common\PaymentService;
use App\Services\Org\EnrollOperateRecordService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ServerRequestInterface;
use Sqids\Sqids;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Yansongda\Artful\Contract\LoggerInterface;
use Yansongda\Pay\Pay;

class PaymentController extends Controller
{

    /**
     * 发起支付、创建支付单
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=62a1d22c-0e29-43fa-92e4-9f0188b97c08
     */
    public function store(Request $request)
    {
        $params = $this->validate($request, [
            'order_no' => 'string|required',
            'platform' => 'string|required|in:wechat',
            'client' => 'string|required|in:mp',
            'extra.openid' => 'string',
        ]);

        $userId = auth()->id();

        $order = Order::query()
            ->where('order_no', $params['order_no'])
            ->where('user_id', $userId)
            ->first();

        if (!$order) {
            throw new BadRequestHttpException('订单不存在或无法操作该订单。');
        }

        try {
            $payment = PaymentService::payment($order, $userId, $params['platform'], $params['client'], $params['extra'] ?? []);
            return $payment;
        } catch (ServiceException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
    }

    /**
     * 获取支付单信息
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=4729ccce-ef99-45ec-bb45-fd0096eef0b8
     */
    public function show(string $outTradeNo)
    {
        $payment = Payment::query()
            ->where('out_trade_no', $outTradeNo)
            ->where('user_id', auth()->id())
            ->first();

        if (!$payment) {
            throw new NotFoundHttpException('支付单不存在或无法操作该支付单。');
        }

        return $payment;
    }

    /**
     * 支付回调
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=75d0a038-eb2a-4d66-ab49-00077545a103
     */
    public function notify(ServerRequestInterface $request, string $platform)
    {
        $logger = Log::channel('pay');

        switch ($platform) {
            case 'wechat':
                $pay = PaymentService::wechat();
                try {
                    $result = $pay->callback($request);
                } catch (\Throwable $e) {
                    Log::channel('pay')->error('验证回调失败: '.$e::class.': '.$e->getMessage(), $request->getParsedBody());
                    throw new BadRequestHttpException($e->getMessage());
                }

                switch ($result['event_type']) {
                    case 'TRANSACTION.SUCCESS':
                        try {
                            PaymentService::complete(
                                $result['resource']['ciphertext']['out_trade_no'],
                                $result['resource']['ciphertext']['transaction_id'],
                                bcdiv($result['resource']['ciphertext']['amount']['payer_total'], '100', 2),
                                Carbon::parse($result['resource']['ciphertext']['success_time'])
                            );
                        } catch (ServiceException $e) {
                            $logger->error('支付处理失败: '.$e::class.': '.$e->getMessage(), $result->toArray());
                            throw new BadRequestHttpException($e->getMessage());
                        } catch (\Throwable $e) {
                            $logger->error('支付处理失败: '.$e::class.': '.$e->getMessage(), $result->toArray());
                            throw $e;
                        }
                        break;

                    case 'REFUND.SUCCESS':
                        break;

                    default:
                        $logger->error('notify:'.$platform.':未知的事件类型', $result->toArray());
                }

                return $pay->success();

            case 'alipay':
                break;

            default:
                return $this->error('路由不存在', [], 404);
        }
    }

    /**
     * 支付回调-服务商模式
     */
    public function notifyService(ServerRequestInterface $request, string $platform, string $encode_mch_id)
    {
        if (empty($encode_mch_id)) {
            throw new BadRequestHttpException('缺少必要参数');
        }
        $logger = Log::channel('pay');

        switch ($platform) {
            case 'wechat':
                $subMchId = (new Sqids(minLength: 6))->decode($encode_mch_id)[0];
                $wechatServicePayConfig = PaymentService::getWechatServicePayConfig($subMchId);
                Pay::config($wechatServicePayConfig);
                Pay::set(LoggerInterface::class, Log::channel('pay'));
                $pay = Pay::wechat();
                try {
                    $result = $pay->callback($request);
                    $logger->debug('微信支付（服务商模式）-回调数据: ', $result->toArray());
                } catch (\Throwable $e) {
                    Log::channel('pay')->error('微信支付（服务商模式）验证回调失败: '.$e::class.': '.$e->getMessage(), $request->getParsedBody());
                    throw new BadRequestHttpException($e->getMessage());
                }

                switch ($result['event_type']) {
                    case 'TRANSACTION.SUCCESS':
                        try {
                            PaymentService::complete(
                                $result['resource']['ciphertext']['out_trade_no'],
                                $result['resource']['ciphertext']['transaction_id'],
                                bcdiv($result['resource']['ciphertext']['amount']['payer_total'], '100', 2),
                                Carbon::parse($result['resource']['ciphertext']['success_time'])
                            );
                        } catch (ServiceException $e) {
                            $logger->error('微信支付（服务商模式）支付处理失败: '.$e::class.': '.$e->getMessage(), $result->toArray());
                            throw new BadRequestHttpException($e->getMessage());
                        } catch (\Throwable $e) {
                            $logger->error('微信支付（服务商模式）支付处理失败: '.$e::class.': '.$e->getMessage(), $result->toArray());
                            throw $e;
                        }
                        break;

                    case 'REFUND.SUCCESS':
                        try {
                            // 获取退款回调数据
                            $refundData = $result['resource']['ciphertext'];
                            $outTradeNo = $refundData['out_trade_no']; // 原订单号
                            $refundNo = $refundData['out_refund_no']; // 退款单号
                            $refundAmount = bcdiv($refundData['amount']['refund'], '100', 2); // 退款金额（分转元）

                            // 根据支付单号查找订单
                            $payment = Payment::query()
                                ->where('out_trade_no', $outTradeNo)
                                ->first();

                            if (!$payment) {
                                $logger->error('退款回调：找不到对应的支付记录', ['out_trade_no' => $outTradeNo]);
                                break;
                            }

                            $order = Order::find($payment->order_id);
                            if (!$order) {
                                $logger->error('退款回调：找不到对应的订单记录', ['payment_id' => $payment->id]);
                                break;
                            }

                            // 更新支付单状态为已退款
                            $payment->status = Payment::STATUS_REFUNDED;
                            $payment->save();

                            // 更新订单状态为已退款
                            $order->status = Order::STATUS_REF_DONE;
                            $order->save();

                            // 更新退款记录状态
                            $refund = Refund::query()
                                ->where('order_id', $order->id)
                                ->where('refund_no', $refundNo)
                                ->first();

                            if ($refund) {
                                $refund->status = 1; // 已退款
                                $refund->finished_at = now();
                                $refund->save();
                            }

                            // 创建退款成功操作记录
                            if ($order->business_type === BusinessType::Enroll) {
                                $enroll = Enroll::query()
                                    ->where('org_id', $order->org_id)
                                    ->where('user_id', $order->user_id)
                                    ->where('enroll_config_id', $order->business_id)
                                    ->first();
                                app(EnrollOperateRecordService::class)->create(
                                    $enroll->id,
                                    EnrollOperateRecord::TYPE_REFUND_SUCCESS,
                                    0,
                                    '退款金额：' . $refundAmount
                                );
                            }

                            $logger->info('退款成功处理完成', [
                                'order_no' => $order->order_no,
                                'refund_no' => $refundNo,
                                'refund_amount' => $refundAmount
                            ]);

                        } catch (\Throwable $e) {
                            $logger->error('退款回调处理失败: '.$e::class.': '.$e->getMessage(), $result->toArray());
                        }
                        break;

                    default:
                        $logger->error('notify:'.$platform.':未知的事件类型', $result->toArray());
                }

                return $pay->success();

            case 'alipay':
                break;

            default:
                return $this->error('路由不存在', [], 404);
        }
    }
}
