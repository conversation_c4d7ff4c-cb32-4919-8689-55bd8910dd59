<?php

namespace App\Http\Controllers\Api;

use App\Libs\Wechat\EasyWechatFactory;
use Grafika\Grafika;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class WechatController extends Controller
{
    public function template()
    {
        $templateIds = config('easywechat.mp.template_ids');

        return array_values($templateIds);
    }

    /**
     * 微信二维码
     * @return array|mixed[]
     * @throws \EasyWeChat\Kernel\Exceptions\BadResponseException
     * @throws \Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function shareQrCode()
    {
        $editor = Grafika::createEditor(['Gd']);

        $user = Auth()->user();
        $qrCode = EasyWechatFactory::shareCode("/pages/selftest/index");
        $filePath = storage_path('app/public').'/qrcode.png';
        Storage::disk('public')->put("qrcode.png", $qrCode);
        $editor->open($image, $filePath);
        $editor->crop($image, 375, 370, 'top-left', 0, 0);

        $editor->open($bigimg, public_path('static').'/selftest_share.png');
        $editor->blend($bigimg, $image, 'normal', 1.0, 'top-left', 200, 487);
        $name = "qr_code_share/qrcode_punish_".$user->uuid.'.png';
        $qrcodeName = storage_path('app/public').'/'.$name;
        $editor->save($bigimg, $qrcodeName);
        $imageData = file_get_contents($qrcodeName);

        // 转换为 base64
        $base64 = base64_encode($imageData);
        // 删除生成的文件
        unlink($qrcodeName);
        unlink($filePath);
        return [
            'imgUrl' => 'data:image/'.pathinfo($qrcodeName, PATHINFO_EXTENSION).';base64,'.$base64
        ];
    }

}
