<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\User\OwnContentController;
use App\Models\Cms\Category;
use App\Models\Cms\Special;
use App\Models\Qa\Tag;
use App\Models\SettingBooth;
use App\Models\Train\Subject;
use App\Models\Train\Test;
use App\Models\Train\TestSubject;
use App\Models\Train\Topic;
use App\Models\User\UserOwnTopic;
use App\Models\User\UserProperty;
use App\Services\Common\AttachmentService;
use App\Services\Topic\TopicService;
use App\Services\User\TipService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class PageController extends Controller
{
    /**
     * 首页
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=728848c2-f51f-4c12-8edb-05b99e900f3e
     * @return array []
     */
    public function home()
    {
        $list = SettingBooth::query()
            ->where('enable', SettingBooth::ENABLE)
            ->select(['id', 'name', 'image', 'url', 'type'])
            ->orderByDesc('sort')
            ->get()
            ->append('image_url');

        $slides = $list->where('type', SettingBooth::TYPE_SL);
        $kk = $list->where('type', SettingBooth::TYPE_KK);

        $searchCategories = Category::query()
            ->select(['id', 'name'])
            ->where('classify', Category::CLASSIFY_MATERIAL)
            ->where('pid', 0)
            ->orderByDesc('sort')
            ->limit(5)
            ->get()
            ->makeHidden(['pid', 'path']);
        $kk = collect($kk);
        return [
            'slides' => $slides,
            'kk' => $kk,
            'search_categories' => $searchCategories,
        ];
    }

    /**
     * 我的
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=0256843a-7570-4955-b5a2-f00189a97f20
     * @return []
     */
    public function me()
    {

        $uid = auth()->id();
        $answerTip = !$uid ? false : TipService::has($uid, "tip-answer-me-page");

        $userProperty = UserProperty::get($uid, ['nickname_edited_at', 'avatar_edited_at']);
        $creditTask = [];
        $heGuiBao = config("heguibao");
        if (!empty($heGuiBao['invitation']['reward_credit'])) {
            $creditTask[] = [
                'name' => "推荐好友",
                "url" => "/pages/me/invite",
                "image" => \Storage::disk('public')->url("color_icon/aixin.png"),
                'isTabBar' => false,
                "intro" => [
                    '推荐好友',
                    "每邀请一个好友注册 +{$heGuiBao['invitation']['reward_credit']}积分/位"
                ],
                "go" => '去邀请'
            ];
        }
        if (!empty($heGuiBao['update_nickname_avatar']['reward_credit'])) {
            if (!$userProperty->nickname_edited_at || !$userProperty->avatar_edited_at) {
                $creditTask[] = [
                    'name' => "编辑昵称,头像",
                    "url" => "/pages/me/setting",
                    "image" => \Storage::disk('public')->url("color_icon/aixin.png"),
                    'isTabBar' => false,
                    "intro" => [
                        "编辑昵称、头像",
                        "完成编辑后 +{$heGuiBao['update_nickname_avatar']['reward_credit']}"
                    ],
                    "go" => '去完成'
                ];
            }
        }
        if (!empty($heGuiBao['share']['reward_credit'])) {
            $reward_credit = $heGuiBao['share']['reward_credit'] * $heGuiBao['share']['limit_per_day'];
            $creditTask[] = [
                'name' => "分享内容",
                "url" => "/pages/document/index",
                "image" => "",
                'isTabBar' => true,
                "intro" => [
                    '分享内容',
                    "分享内容给好友，上限{$reward_credit}积分/天"
                ],
                "go" => '去完成'
            ];
        }
        if (!empty($heGuiBao['question']['reward_credit'])) {
            $reward_credit = $heGuiBao['question']['reward_credit'] * $heGuiBao['question']['limit_per_day'];
            $creditTask[] = [
                'name' => "安全动态发布",
                "url" => "/pages/ask/home",
                "image" => "",
                'isTabBar' => false,
                "intro" => [
                    '发表动态',
                    "分享优质内容，上限{$reward_credit}积分/天"
                ],
                "go" => '去完成'
            ];
        }

        return [
            'credit_task' => $creditTask,
            'answer_tip' => $answerTip
        ];
    }

    /**
     * 问答
     * @return []
     */
    public function qa()
    {
        $uid = auth()->id();

        $answerTip = !$uid ? false : TipService::has($uid, "tip-answer-me-page");

        $hotTags = Tag::query()
            ->whereNotNull('recommend_at')
            ->orderByDesc('recommend_at')
            ->limit(10)
            ->publicFields()
            ->get();

        return [
            'answer_tip' => $answerTip,
            'hot_tags' => $hotTags
        ];
    }

    /**
     * 培训考试
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=5fe9ba38-1e44-4a44-8d9d-eef1a9ede064
     * @return array []
     */
    public function train()
    {
        $params = request()->validate([
            'topic_id' => ['string'],
        ]);

        $uid = auth()->id();
        $isInitOpen = false;

        $userProperty = UserProperty::get($uid, ['current_topic_id']);
        if (!$userProperty->current_topic_id) {
            $isInitOpen = true;
        }
        $topicId = $userProperty->current_topic_id;
        $isBuy = false;

        $buyTopicId = TopicService::whetherPurchase($uid);

        $topicList = collect();
        if ($topicId) {
            $topic = Topic::query()->where('id', $topicId)->first(['id', 'name']);
            if (!$topic) {
                $topic = Topic::query()->orderByDesc('sort')->first(['id', 'name']);
            }
        } else {
            $topic = Topic::query()->orderByDesc('sort')->first(['id', 'name']);
        }

        if (count($buyTopicId)) {
            $isBuy = true;
            $topicList = Topic::query()
                ->whereIn('id', $buyTopicId->toArray())
                ->select(['id', 'name'])
                ->withCount(['subjects'])
                ->where('id', '<>', $topic?->id)
                ->orderByDesc('sort')->limit(3)->get();
        }
        $recTopicList = Topic::query()
            ->select(['id', 'name'])
            ->withCount(['subjects'])
            ->orderByDesc('sort')
            ->limit(3)->get();

        $data = Test::query()->where('user_id', $uid)
            ->where('topic_id', $topicId)
            ->select([
                \DB::raw("SUM( subject_completed_count ) as subject_completed_count"),
                \DB::raw("SUM( subject_correct_count ) as subject_correct_count"),
            ])->first();
        $subjectCount = Subject::query()->where('topic_id', $topic->id)->count();

        $completedCount = TestSubject::query()
            ->where('topic_id', $topic->id)
            ->where('option_id', '<>', 0)
            ->where('user_id', $uid)
            ->select(['subject_id'])
            ->distinct()->count();

        $compRate = 0;
        $right = 0;

        if ($completedCount) {
            if ($completedCount >= $subjectCount) {
                $compRate = 100;
            } else {
                $compRate = bcmul($completedCount / $subjectCount, 100, 2);
                // 判断小数部分是否为 .00，如果是则取整数部分
                if (str_contains($compRate, '.00')) {
                    $compRate = (int)$compRate;
                }
            }
        }

        if ($data->subject_completed_count && $data->subject_correct_count) {
            $right = bcmul($data->subject_correct_count / $data->subject_completed_count, 100, 2);
            // 判断小数部分是否为 .00，如果是则取整数部分
            if (str_contains($right, '.00')) {
                $right = (int)$right;
            }
        }

        $isPurchased = UserOwnTopic::query()->where('user_id', $uid)->where('topic_id', $topicId)->exists();
        $examinationDate = "2025-10-26 00:00:00";
        $diffInDays = Carbon::parse()->diffInDays($examinationDate);

        return [
            'subject_count' => $data->subject_completed_count ?? 0, //总练习题数
            'comp_rate' => $compRate,
            'right' => $right,
            'topic_id' => $topicId ? Topic::encodeId($topicId) : 0,
            'topic_list' => $topicList,
            'topic' => $topic,
            'name' => '中级注册安全工程师题库',
            'is_buy' => $isBuy,
            'rec_topic_list' => $recTopicList,
            'me_own_content' => collect(app(OwnContentController::class)->index('course'))->take(3),
            'course_category_id' => $topic->course_category_id,
            'diff_in_days' => $diffInDays,
            'is_purchased' => $isPurchased,
            'is_init_open' => $isInitOpen,
        ];
    }

    /**
     * 用户设置配置
     * @return array
     */
    public function userProfile()
    {
        $user = \Auth::user();

        $diskName = config('heguibao.storage.pub');
        return AttachmentService::getUploadForm($user->uuid, 'image', 1024 * 5, $diskName);
    }

    /**
     * 资料页
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=abbe7e34-7894-40d2-a7f1-be33e852ed60
     *
     * @return array
     */
    public function material(): array
    {
        $categories = Category::publicFields()
            ->where('visible', Category::VISIBLE)
            ->where('classify', Category::CLASSIFY_MATERIAL)
            ->where('hot_position', '>', 0)
            ->orderBy('hot_position')
            ->limit(7)
            ->get();

        $specials = Special::publicFields()
            ->withCount('contents')
            ->where('status', Special::STATUS_NORMAL)
            ->orderByDesc('recommend_at')
            ->limit(5)
            ->get();

        $appointCategories = Category::publicFields()
            ->where('visible', Category::VISIBLE)
            ->where('classify', Category::CLASSIFY_MATERIAL)
            ->where('pid', 0)
            ->whereIn('name', Category::appointCategory())
            ->orderByDesc('created_at')
            ->get();

        return [
            'categories' => $categories,
            'specials' => $specials,
            'appoint_categories' => $appointCategories
        ];
    }

    /**
     * 充值页面配置
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=5a436320-2d19-4b7b-ab69-df2de63eef19
     *
     * @return array
     */
    public function recharge(): array
    {
        $creditPackages = [
            ['price' => 10, 'credit' => 100],
            ['price' => 20, 'credit' => 200],
            ['price' => 50, 'credit' => 500]
        ];

        return [
            'credit_packages' => $creditPackages,
            'credit_multiple' => config('heguibao.recharge.credit_multiple')
        ];
    }

    /**
     * 题库购买价格配置
     * @return array[]
     */
    public function topicBuy()
    {
        $params = request()->validate([
            'org_sid' => 'string',
            'topic_sid' => 'string',
        ]);

        if (!empty($params['org_sid']) && !empty($params['topic_sid'])) {
            $org = \App\Models\Org::whereSid($params['org_sid'])->first();

            if (!$org) {
                throw new NotFoundHttpException("机构不存在");
            }

            $topic = \App\Models\Train\Topic::whereSid($params['topic_sid'])->first();

            if (!$topic) {
                throw new NotFoundHttpException("题库不存在");
            }

            $orgTopic = \App\Models\Org\Topic::query()->where('topic_id', $topic->id)->where('org_id', $org->id)->first();
            if (!$orgTopic) {
                throw new NotFoundHttpException("机构题库不存在");
            }

            $amountList = [
                'month' => [
                    'bays' => 30,
                    'amount' => $orgTopic->price_sell_30,
                    'type' => 'month'
                ],
                'month60' => [
                    'bays' => 60,
                    'amount' => $orgTopic->price_sell_60,
                    'type' => 'month60'
                ]
            ];
        } else {
            $amountList = Topic::amount();
        }
        return [
            'amount_list' => $amountList
        ];
    }

    /**
     * 搜索列表类型
     *
     * @return array
     */
    public function listType(): array
    {
        $types = [
            ['name' => '全部格式', 'type' => ''],
            ['name' => 'PPT', 'type' => 'ppt'],
            ['name' => 'PDF', 'type' => 'pdf'],
            ['name' => 'WORD', 'type' => 'word'],
            ['name' => 'EXCEL', 'type' => 'excel']
        ];
        if (!config('heguibao.hidden_videos')) {
            array_push($types, ['name' => '视频', 'type' => 'video']);
        }

        return $types;
    }

}
