<?php

namespace App\Http\Controllers\Org\Enroll;

use App\Http\Controllers\Org\Controller;
use App\Models\Org\EnrollConfig;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\EnrollConfigService;
use Illuminate\Http\Request;

class EnrollConfigController extends Controller
{
    public function index(Request $request)
    {
        $params = $request->validate([
            'title' => 'string',
        ]);
        $orgId = $this->auth()->org_id;
        $builder = EnrollConfig::query()
            ->where('org_id', $orgId)
            ->when(!empty($params['title']), fn($query) => $query->where('title', 'like', "%{$params['title']}%"))
            ->orderByDesc('id');

        return $this->apiPaginate($request, $builder);
    }

    public function store(Request $request)
    {
        $params = $request->validate([
            'id' => 'integer',
            'title' => 'required|string',
            'amount' => 'required|numeric',
            'sort' => 'integer',
        ]);
        $params['org_id'] = $this->auth()->org_id;
        $config = app(EnrollConfigService::class)->store($params);

        $auth = $this->auth();
        OperateLogService::create($auth->id, '创建机构报名课程配置', ['org_id' => $auth->org_id, 'params' => $params]);

        return $config->toArray();
    }

    public function update(Request $request, int $id)
    {
        $params = $request->validate([
            'title' => 'required|string',
            'amount' => 'required|numeric',
            'sort' => 'integer',
        ]);
        $params['id'] = $id;
        $params['org_id'] = $this->auth()->org_id;
        $config = app(EnrollConfigService::class)->store($params);

        $auth = $this->auth();
        OperateLogService::create($auth->id, '修改机构报名课程配置', ['org_id' => $auth->org_id, 'params' => $params]);

        return $config->toArray();
    }

    public function destroy(int $id): array
    {
        EnrollConfig::destroy($id);
        $auth = $this->auth();
        OperateLogService::create($auth->id, '删除机构报名课程配置', ['org_id' => $auth->org_id, 'id' => $id]);
        return $this->success();
    }
}
