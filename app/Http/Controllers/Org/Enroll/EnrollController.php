<?php

namespace App\Http\Controllers\Org\Enroll;

use App\Http\Controllers\Org\Controller;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\EnrollService;
use Illuminate\Http\Request;

class EnrollController extends Controller
{
    public function index(Request $request)
    {
        $params = $request->validate([
            'name' => 'string',
            'phone' => 'string',
            'id_card_number' => 'string',
            'title' => 'string',
            'status' => 'integer',
        ]);
        $orgId = $this->auth()->org_id;
        $builder =app(EnrollService::class)->getEnrollListByOrgId($orgId, $params);
        return $this->apiPaginate($request, $builder);
    }

    public function detail(int $id)
    {
        return app(EnrollService::class)->getEnrollById($id);
    }

    public function audit(Request $request, int $id)
    {
        $params = $request->validate([
            'status' => 'integer|int:1,2',
            'remark' => 'string'
        ]);
        app(EnrollService::class)->audit($id, $params['status'], $params['remark']);

        $auth = $this->auth();
        OperateLogService::create($auth->id, '审核机构报名信息', ['org_id' => $auth->org_id, 'params' => $params]);
        return $this->success();
    }
}
