<?php

namespace App\Http\Controllers\Org;

use App\Libs\Baidu\AipAPI;
use App\Models\Org\Enrollment;
use App\Models\Org\Student;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\StudentService;
use Illuminate\Http\Request;

class StudentController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'name' => 'string',
            'phone' => 'string',
            'enrollment_class_id' => 'integer',
            'enrollment_type' => 'string',
            'enrollment_resource_id' => 'integer',
            'enrollment_status' => 'integer',
            'created_at' => 'array'
        ]);

        $auth = $this->auth();

        $params['org_id'] = $auth->org_id;

        $builder = Student::query()->with([
            'enrollments' => function ($query) use ($params) {
                $query->orderBy('id', 'desc');
                foreach ($params as $key => $value) {
                    if ($value != '') {
                        switch ($key) {
                            case 'enrollment_class_id':
                                $query->where('class_id', $value);
                                break;
                            case 'enrollment_resource_id':
                                $type = $params['enrollment_type'];
                                $resourceId = $params['enrollment_resource_id'];
                                $query->where('type', $type)->where('resource_id', $resourceId);
                                break;
                            case 'enrollment_status':
                                $query->where('status', $value);
                                break;
                        }
                    }
                }
            },
            'enrollments.classroom',
            'enrollments.resource',
            'enrollments.student',
        ]);

        $this->builderWhere($builder, $params, ['id', 'org_id', 'name', 'phone']);
        $this->builderOrderBy($request, $builder);

        $enrollmentTable = (new Enrollment())->getTable();

        foreach ($params as $key => $value) {
            if ($value != '') {
                switch ($key) {
                    case 'enrollment_class_id':
                        $builder->whereIn('id', function ($query) use ($enrollmentTable, $value) {
                            $query->from($enrollmentTable)->select(['student_id'])->where('class_id', $value);
                        });
                        break;
                    case 'enrollment_resource_id':
                        $type = $params['enrollment_type'];
                        $resourceId = $params['enrollment_resource_id'];
                        $builder->whereIn('id', function ($query) use ($enrollmentTable, $type, $resourceId) {
                            $query->from($enrollmentTable)->select(['student_id'])->where('type', $type)->where('resource_id', $resourceId);
                        });
                        break;
                    case 'enrollment_status':
                        $builder->whereIn('id', function ($query) use ($enrollmentTable, $value) {
                            $query->from($enrollmentTable)->select(['student_id'])->where('status', $value);
                        });
                        break;
                }
            }
        }

        $data = $this->apiPaginate($request, $builder);

        /** @var Student $record */
        foreach ($data['records'] as $record) {
            $record->key = "student-$record->id";

            $record->enrollments->map(function (Enrollment $enrollment) {
                $enrollment->setHidden([]);

                $enrollment->resource = $enrollment->toResource();

                $enrollment->key = "enrollment-$enrollment->id";
            });
        }

        return $data;
    }

    public function store(Request $request): Student
    {
        $params = $request->validate([
            'user_id' => 'required|integer',
            'name' => 'required|string|max:20',
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'id_card_number' => 'required|string|max:18',
            'id_card_front' => 'required|string',
            'id_card_back' => 'required|string',
            'photo' => 'required|string'
        ]);

        $auth = $this->auth();

        $student = StudentService::create($auth->org_id, $params);

        $student->setHidden([]);

        OperateLogService::create($auth->id, '创建机构学员信息', ['id' => $student->id, 'org_id' => $auth->org_id, 'params' => $params]);

        return $student;
    }

    public function update(Request $request, int $id): Student
    {
        $params = $request->validate([
            'name' => 'string|max:20',
            'phone' => 'string|regex:/^1[3-9]\d{9}$/',
            'id_card_number' => 'string|max:18',
            'id_card_front' => 'string',
            'id_card_back' => 'string',
            'photo' => 'string',
            'work_unit' => 'string'
        ]);

        $auth = $this->auth();

        $student = StudentService::update($id, $auth->org_id, $params);

        $student->setHidden([]);

        OperateLogService::create($auth->id, '修改学员信息', ['id' => $id, 'org_id' => $auth->org_id, 'params' => $params]);

        return $student;
    }

    public function batchAssign(Request $request): array
    {
        $params = $request->validate([
            'student_ids' => 'required|array',
            'class_id' => 'integer|nullable',
            'subject_type' => 'in:course,topic,course_pack|nullable',
            'subject_id' => 'integer|nullable'
        ]);

        $auth = $this->auth();

        $classId = empty($params['class_id']) ? 0 : $params['class_id'];
        $subjectType = empty($params['subject_type']) ? '' : $params['subject_type'];
        $subjectId = empty($params['subject_id']) ? 0 : $params['subject_id'];

        StudentService::batchAssign($auth->org_id, $params['student_ids'], $classId, $subjectType, $subjectId);

        OperateLogService::create($auth->id, '机构学员分配班级', ['org_id' => $auth->org_id, 'params' => $params]);

        return $this->success();
    }
    // 导入学员身份证
    public function importIdCardPhoto(Request $request, AipAPI $baiDuOcr)
    {
        $request->validate([
            'file' => 'required|file|mimes:zip',
        ]);

        $orgId = $this->auth()->org_id;

        // 获取上传的ZIP文件
        $file = $request->file('file');

        $result = StudentService::importIdCardPhoto($orgId, $file, $baiDuOcr);

        // 记录操作日志
        OperateLogService::create($this->auth()->id, '导入学员身份证', [
                'org_id' => $orgId,
                'success' => $result['success'],
                'failed' => $result['failed']
        ]);

        return $result;
    }

    public function destroy(int $id): array
    {
        $orgId = $this->auth()->org_id;

        StudentService::remove($id, $orgId);

        OperateLogService::create($this->auth()->id, '删除学员信息', ['id' => $id, 'org_id' => $orgId]);

        return $this->success();
    }
}
