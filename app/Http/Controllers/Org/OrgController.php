<?php

namespace App\Http\Controllers\Org;

use App\Models\Org;
use App\Services\Org\Admin\OperateLogService;
use App\Services\OrgService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OrgController extends Controller
{
    public function show(): Org
    {
        /** @var Org $org */
        $org = Org::query()->where('id', $this->auth()->org_id)->first();

        if (!$org) {
            throw new NotFoundHttpException('机构不存在');
        }

        $org->setHidden([]);

        return $org;
    }

    public function update(Request $request): Org
    {
        $params = $request->validate([
            'name' => 'string',
            'alias' => 'string',
            'logo' => 'string',
            'business_license' => 'string',
            'business_scope' => 'string',
            'contact' => 'string',
            'service_qrcode' => 'string',
            'official_seal_image' => 'string',
            'area_code' => 'string',
            'area_text' => 'array',
            'enroll_bg_img' => 'string',
        ]);

        $org = OrgService::update($this->auth()->org_id, $params);

        OperateLogService::create($this->auth()->id, '修改机构信息', ['id' => $this->auth()->org_id, 'params' => $params]);

        $org->setHidden([]);

        return $org;
    }
}
