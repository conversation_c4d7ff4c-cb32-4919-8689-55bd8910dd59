<?php

namespace App\Http\Controllers\Org;

use App\Exceptions\ServiceException;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCourseSection;
use App\Models\Org\CourseSub;
use App\Services\Cms\ContentCourseChapterService;
use App\Services\Cms\ContentCourseService;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\CourseSubService;
use Illuminate\Http\Request;

class CourseSubController extends Controller
{
    public function getChapterSections(Request $request): array
    {
        $params = $request->validate([
            'course_id' => 'required|integer',
        ]);

        $admin = $this->auth();

        [$chapterIds, $sectionIds] = CourseSubService::getResourceIds($admin->org_id, $params['course_id']);

        $chapters = ContentCourseChapter::query()
            ->with([
                'sections' => function ($query) {
                    $query->where('status', ContentCourseSection::STATUS_SHOW)->orderByRaw('sort desc,id asc');
                },
                'sections.video'
            ])
            ->where('content_id', $params['course_id'])
            ->where('status', ContentCourseChapter::STATUS_SHOW)
            ->orderByRaw('sort desc,id asc')
            ->get();

        $list = ContentCourseChapterService::children($chapters);

        $contentCourse = ContentCourse::query()->where('content_id', $params['course_id'])->first();


        foreach ($list as &$item) {

            $item['status'] = in_array($item['id'], $chapterIds);
            $item['hour'] = $contentCourse->studyHour($item['duration']);
            $item['duration'] = 0;
            foreach ($item['children'] as &$child) {
                $child['status'] = in_array($child['id'], $sectionIds);
                $child['hour'] = $contentCourse->studyHour($child['duration']);

                if ($item['status'] && $child['status']) {
                    $item['duration'] += $child['duration'];
                }
            }
        }

        return ContentCourseChapterService::generateSerialNo($list);
    }

    public function operate(Request $request): array
    {
        $params = $request->validate([
            'course_id' => 'required|integer',
            'type' => 'required|in:' . implode(',', [CourseSub::TYPE_SECTION, CourseSub::TYPE_CHAPTER]),
            'resource_id' => 'required|integer',
            'operate' => 'required|in:open,close'
        ]);

        $admin = $this->auth();

        CourseSubService::operate($admin->org_id, $params['course_id'], $params['type'], $params['resource_id'], $params['operate']);

        OperateLogService::create($this->auth()->id, '章节操作', ['org_id' => $admin->org_id, 'params' => $params]);

        return $this->success();
    }
}
