<?php

namespace App\Http\Controllers\Org;

use App\Models\Cms\Content;
use App\Models\Org\CoursePack;
use App\Services\Org\Admin\OperateLogService;
use Illuminate\Http\Request;

class CoursePackController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'keyword' => 'string',
            'status' => 'integer'
        ]);

        $params['org_id'] = $this->auth()->org_id;

        if (isset($params['keyword']) && $params['keyword']) {
            if (preg_match('/^[1-9]\d{0,8}$/', $params['keyword'])) {
                $relationId = $params['keyword'];
            } else {
                $relationId = Content::query()
                    ->where('title', 'like', "%{$params['keyword']}%")
                    ->pluck('id')
                    ->toArray();

                if (empty($relationId)) {
                    $relationId = 0;
                }
            }

            $params['course_pack_id'] = $relationId;
        }

        $builder = CoursePack::query()->with(['contentCoursePack', 'contentCoursePack.content']);
        $this->builderWhere($builder, $params, ['status', 'org_id', 'course_pack_id']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }

    public function batchUpdate(Request $request): array
    {
        $params = $request->validate([
            'ids' => 'required|array',
            'status' => 'integer',
            'price_sell' => 'numeric',
        ]);

        $update = [];

        if (isset($params['status'])) {
            $update['status'] = $params['status'];
        }

        if (isset($params['price_sell'])) {
            $update['price_sell'] = $params['price_sell'];
        }

        $auth = $this->auth();

        CoursePack::query()
            ->whereIn('id', $params['ids'])
            ->where('org_id', $auth->org_id)
            ->update($update);

        OperateLogService::create($auth->id, '批量修改机构课程包信息', ['org_id' => $auth->org_id, 'params' => $params]);

        return $this->success();
    }
}
