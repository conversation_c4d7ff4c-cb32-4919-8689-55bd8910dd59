<?php
/**
 * ExportController.php class file.
 *
 * <AUTHOR>
 * @time 2025/2/26 17:57
 * @copyright 2025 pp.cc All Right Reserved
 */

namespace App\Http\Controllers\Org;

use App\Jobs\ProcessExportJob;
use App\Models\Org\Admin\Role;
use App\Models\Org\Export;
use App\Services\Org\Admin\AdminService;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Common\AttachmentService;
use App\Services\Org\Export\ExporterInterface;
use App\Services\Org\Export\ExportFactory;
use App\Services\Org\ExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ExportController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'type' => 'string',
            'status' => 'integer',
            'created_at' => 'array',
        ]);

        $admin = $this->auth();

        $params['org_id'] = $admin->org_id;

        // 教师只能看到自己的下载任务
        if (!AdminService::checkRole($admin, [Role::CODE_ADMIN, Role::CODE_AUDITOR])) {
            $params['admin_id'] = $admin->id;
        }

        $builder = Export::query()->with(['admin', 'attachment']);
        $this->builderWhere($builder, $params, ['type', 'status', 'admin_id', 'org_id', 'created_at']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }

    /**
     * 创建导出任务
     * @throws ValidationException
     */
    public function store(Request $request): array
    {
        $params = $request->validate([
            'type' => 'required|string|in:' . implode(',', [
                Export::TYPE_HOUR_CERT,
                Export::TYPE_STUDENT_ARCHIVE,
                Export::TYPE_HOUR_RECORD,
                Export::TYPE_TEST_PAPER,
                Export::TYPE_ORG_ENROLLMENT,
                Export::TYPE_ORG_ENROLLMENT_FORM,
                Export::TYPE_ORG_DOWNLOAD_PACK,
            ]),
            'extra' => 'nullable|array',
            'checked_ids' => 'nullable|array',
        ]);

        /** @var ExporterInterface::class $exporterClass */
        $exporterClass = ExportFactory::getHandlerClassByType($params['type']);
        $extra = Validator::validate($params['extra'] ?? [], $exporterClass::validateRules());

        if (!empty($params['checked_ids'])) {
            $extra['checked_ids'] = $params['checked_ids'];
        } else {
            $params['checked_ids'] = [];
        }

        $admin = $this->auth();
        $checkedId = count($params['checked_ids']) == 1 ? $params['checked_ids'][0] : 0;
        $handler = ExportFactory::create($params['type'], $admin->org_id, $checkedId, $extra);
        $descPre = count($params['checked_ids']) > 1 ? '批量导出' : '导出';

        // 创建导出任务
        $export = Export::query()->create([
            'org_id' => $admin->org_id,
            'type' => $params['type'],
            'desc' => $descPre . $handler->generateDesc(),
            'admin_id' => $admin->id,
            'status' => Export::STATUS_IN_PROGRESS,
            'extra' => $extra,
        ]);

        // 添加导出队列
        ProcessExportJob::dispatch($export->id);

        OperateLogService::create($admin->id, '创建下载记录', ['id' => $export->id, 'org_id' => $admin->org_id, 'params' => $params]);

        return $this->success();
    }

    public function destroy(int $id): array
    {
        $admin = $this->auth();

        ExportService::remove($id, $admin->org_id);

        OperateLogService::create($admin->id, '删除下载记录', ['id' => $id, 'org_id' => $admin->org_id]);

        return $this->success();
    }

    /**
     * 获取导出任务下载地址
     *
     * @param $id
     * @return array
     */
    public function download($id)
    {
        $export = Export::query()->with('attachment')->find($id);

        if (!$export) {
            throw new NotFoundHttpException('导出任务不存在');
        }

        if (!in_array($export->status, [Export::STATUS_COMPLETED, Export::STATUS_PARTLY_COMPLETED])) {
            throw new BadRequestHttpException('导出任务未完成，无法下载');
        }

        $url = AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $export->file, $export->attachment->filename);

        return ['url' => $url];
    }

}
