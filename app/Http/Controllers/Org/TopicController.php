<?php

namespace App\Http\Controllers\Org;

use App\Models\Org\Topic;
use App\Models\Train\Topic as TrainTopic;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\TopicService;
use App\Services\Topic\SubjectService;
use Illuminate\Http\Request;

class TopicController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'keyword' => 'string',
            'status' => 'integer'
        ]);

        $params['org_id'] = $this->auth()->org_id;

        if (isset($params['keyword']) && $params['keyword']) {
            if (preg_match('/^[1-9]\d{0,8}$/', $params['keyword'])) {
                $relationId = $params['keyword'];
            } else {
                $relationId = TrainTopic::query()
                    ->where('name', 'like', "%{$params['keyword']}%")
                    ->pluck('id')
                    ->toArray();

                if (empty($relationId)) {
                    $relationId = 0;
                }
            }

            $params['topic_id'] = $relationId;
        }

        $builder = Topic::query()->with(['topic' => function ($query) {
            $query->withCount(['subjects']);
        }]);
        $this->builderWhere($builder, $params, ['status', 'org_id', 'topic_id']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }

    public function update(Request $request, int $id): Topic
    {
        $params = $request->validate([
            'exam_time' => 'integer|min:60|max:180',
            'pass_score' => 'integer|min:60|max:100',
            'exam_config' => 'array'
        ], [], [
            'exam_time' => '考试时间',
            'pass_score' => '通过分数',
            'exam_config' => '考试配置',
        ]);

        $auth = $this->auth();

        $org = TopicService::update($id, $auth->org_id, $params);

        OperateLogService::create($auth->id, '修改机构题库信息', ['id' => $auth->org_id, 'params' => $params]);

        $org->setHidden([]);

        return $org;
    }

    public function batchUpdate(Request $request): array
    {
        $params = $request->validate([
            'ids' => 'required|array',
            'status' => 'integer',
            'price' => 'numeric',
            'price_60' => 'numeric',
        ]);

        $update = [];

        if (isset($params['status'])) {
            $update['status'] = $params['status'];
        }

        if (isset($params['price'])) {
            $update['price_sell_30'] = $params['price'];
        }
        if (isset($params['price_60'])) {
            $update['price_sell_60'] = $params['price_60'];
        }

        $auth = $this->auth();

        Topic::query()
            ->whereIn('id', $params['ids'])
            ->where('org_id', $auth->org_id)
            ->update($update);

        OperateLogService::create($auth->id, '批量修改机构题库信息', ['org_id' => $auth->org_id, 'params' => $params]);

        return $this->success();
    }

    public function getSubjectTypeCount(Request $request): array
    {
        $params = $request->validate([
            'topic_id' => 'required|integer',
        ]);

        return SubjectService::getTypeCount($params['topic_id']);
    }
}
