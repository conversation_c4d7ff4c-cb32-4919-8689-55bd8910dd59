<?php

namespace App\Http\Controllers\Org;

use App\Models\Org\BalanceRecord;
use Illuminate\Http\Request;

class BalanceRecordController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'type' => 'string',
            'enroll_id' => 'integer',
            'created_at' => 'array'
        ]);

        $params['org_id'] = $this->auth()->org_id;

        $builder = BalanceRecord::query();
        $this->builderWhere($builder, $params, ['id', 'type', 'enroll_id', 'org_id', 'created_at']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }
}
