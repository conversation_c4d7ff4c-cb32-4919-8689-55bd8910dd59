<?php

namespace App\Http\Controllers\Org;

use App\Models\Org\Admin\Admin;
use App\Models\Org\Admin\Role;
use App\Models\Org\OrgClass;
use App\Models\Org\Template;
use App\Services\Org\Admin\AdminService;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\CoursePackService;
use App\Services\Org\CourseService;
use App\Services\Org\OrgClassService;
use App\Services\Org\TopicService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ClassesController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'type' => [
                function ($attribute, $value, $fail) {
                    if (!is_string($value) && !is_array($value)) {
                        $fail($attribute .' 参数类型错误');
                    }
                }
            ],
            'name' => 'string',
            'status' => 'integer',
            'created_at' => 'array'
        ]);

        $admin = $this->auth();

        $params['org_id'] = $admin->org_id;

        // 教师只能看到自己的班级
        if (!AdminService::checkRole($admin, [Role::CODE_ADMIN, Role::CODE_AUDITOR])) {
            $params['manager_id'] = $admin->id;
        }

        $builder = OrgClass::query()->with(['manager', 'resource']);
        $this->builderWhere($builder, $params, ['status', 'type', 'manager_id', 'org_id', 'created_at']);
        $this->builderOrderBy($request, $builder);

        if (!empty($params['name'])) {
            $builder->where('name', 'like', "%{$params['keyword']}%");
        }

        $data = $this->apiPaginate($request, $builder);

        /** @var OrgClass $record */
        foreach ($data['records'] as $record) {
            $record->resource = $record->toResource();
        }

        return $data;
    }

    public function show(int $id): OrgClass
    {
        /** @var OrgClass $class */
        $class = OrgClass::query()->with(['manager', 'resource'])->where('org_id', $this->auth()->org_id)->find($id);

        if (!$class) {
            throw new NotFoundHttpException('班级不存在');
        }

        $class->resource = $class->toResource(true);

        return $class->setHidden([]);
    }

    public function store(Request $request): OrgClass
    {
        $params = $request->validate([
            'name' => 'required|string|max:20',
            'manager_id' => 'required|integer',
            'type' => 'required|string',
            'resource_id' => 'required|integer',
            'exam_enabled' => 'boolean',
            'exam_limit' => 'boolean',
            'exam_limit_count' => 'integer',
            'exam_condition' => 'string',
            'exam_mode' => 'string',
            'exam_at' => 'date|nullable',
            'face_capture_enabled' => 'boolean',
            'face_capture_count' => 'integer',
            'template_custom' => 'boolean',
            'template_hour_id' => 'integer',
            'template_archive_id' => 'integer',
            'start_at' => 'required|date',
            'end_at' => 'required|date',
        ]);

        $orgId = $this->auth()->org_id;

        $class = OrgClassService::create($orgId, $params);

        OperateLogService::create($this->auth()->id, '创建班级信息', ['id' => $class->id, 'org_id' => $orgId, 'params' => $params]);

        return $class;
    }

    public function update(Request $request, int $id): OrgClass
    {
        $params = $request->validate([
            'name' => 'string|max:20',
            'manager_id' => 'integer',
            'exam_enabled' => 'boolean',
            'exam_limit' => 'boolean',
            'exam_limit_count' => 'integer',
            'exam_condition' => 'string',
            'exam_mode' => 'string',
            'exam_at' => 'date|nullable',
            'face_capture_enabled' => 'boolean',
            'face_capture_count' => 'integer',
            'template_custom' => 'boolean',
            'template_hour_id' => 'integer',
            'template_archive_id' => 'integer',
            'start_at' => 'date',
            'end_at' => 'date',
        ]);

        $orgId = $this->auth()->org_id;

        $class = OrgClassService::update($id, $orgId, $params);

        OperateLogService::create($this->auth()->id, '修改班级信息', ['id' => $id, 'org_id' => $orgId, 'params' => $params]);

        return $class;
    }

    public function destroy(int $id): array
    {
        $orgId = $this->auth()->org_id;

        OrgClassService::remove($id, $orgId);

        OperateLogService::create($this->auth()->id, '删除班级信息', ['id' => $id, 'org_id' => $orgId]);

        return $this->success();
    }

    public function getOptions(): array
    {
        $admin = $this->auth();

        $topics = TopicService::getOrgTopics($admin->org_id);
        $courses = CourseService::getOrgCourses($admin->org_id);
        $coursePacks = CoursePackService::getOrgCoursePacks($admin->org_id);

        $managers = Admin::query()
            ->select(['id', 'real_name', 'username', 'phone'])
            ->where('org_id', $admin->org_id)
            ->where('status', Admin::STATUS_ENABLED)
            ->get()
            ->setHidden([])
            ->toArray();

        $templateHours = Template::query()
            ->where('org_id', $admin->org_id)
            ->where('type', Template::TYPE_HOUR_CERT)
            ->get()
            ->toArray();

        $templateArchives = Template::query()
            ->where('org_id', $admin->org_id)
            ->where('type', Template::TYPE_STUDENT_ARCHIVE)
            ->get()
            ->toArray();

        $check = false;
        if (!AdminService::checkRole($admin, [Role::CODE_ADMIN, Role::CODE_AUDITOR])) {
            $check = true;
        }

        foreach ($managers as &$manager) {
            $manager['real_name'] = $manager['real_name'] ?: $manager['username'];
        }

        return [
            'teacher' => $check,
            'manager_id' => $admin->id,
            'managers' => $managers,
            'topics' => $topics,
            'courses' => $courses,
            'course_packs' => $coursePacks,
            'template_hours' => $templateHours,
            'template_archives' => $templateArchives,
        ];
    }

    public function finished(Request $request): array
    {
        $params = $request->validate([
            'id' => 'required|integer',
        ]);

        $orgId = $this->auth()->org_id;

        OrgClassService::finished($params['id'], $orgId);

        OperateLogService::create($this->auth()->id, '班级结束', ['org_id' => $orgId, 'params' => $params]);

        return $this->success();
    }
}
