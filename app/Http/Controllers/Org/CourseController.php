<?php

namespace App\Http\Controllers\Org;

use App\Models\Cms\Content;
use App\Models\Org\Course;
use App\Services\Cms\ContentCourseChapterService;
use App\Services\Cms\ContentCourseService;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\CourseService;
use Illuminate\Http\Request;

class CourseController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'keyword' => 'string',
            'status' => 'integer'
        ]);

        $params['org_id'] = $this->auth()->org_id;

        if (isset($params['keyword']) && $params['keyword']) {
            if (preg_match('/^[1-9]\d{0,8}$/', $params['keyword'])) {
                $relationId = $params['keyword'];
            } else {
                $relationId = Content::query()
                    ->where('title', 'like', "%{$params['keyword']}%")
                    ->pluck('id')
                    ->toArray();

                if (empty($relationId)) {
                    $relationId = 0;
                }
            }

            $params['course_id'] = $relationId;
        }

        $builder = Course::query()->with(['contentCourse', 'contentCourse.content']);
        $this->builderWhere($builder, $params, ['status', 'org_id', 'course_id']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }

    public function batchUpdate(Request $request): array
    {
        $params = $request->validate([
            'ids' => 'required|array',
            'status' => 'integer',
            'price_sell' => 'numeric',
        ]);

        $update = [];

        if (isset($params['status'])) {
            $update['status'] = $params['status'];
        }

        if (isset($params['price_sell'])) {
            $update['price_sell'] = $params['price_sell'];
        }

        $auth = $this->auth();

        Course::query()
            ->whereIn('id', $params['ids'])
            ->where('org_id', $auth->org_id)
            ->update($update);

        OperateLogService::create($auth->id, '批量修改机构课程信息', ['org_id' => $auth->org_id, 'params' => $params]);

        return $this->success();
    }
    public function getChapterSections(Request $request, int $id): array
    {
        $admin = $this->auth();

        $list = CourseService::children($admin->org_id, $id);

        return ContentCourseChapterService::generateSerialNo($list);
    }

    public function operate(Request $request, int $id): array
    {
        $params = $request->validate([
            'type' => 'required|in:' . implode(',', [Course::TYPE_SECTION, Course::TYPE_CHAPTER]),
            'resource_id' => 'required|integer',
            'operate' => 'required|in:open,close'
        ]);

        $admin = $this->auth();

        CourseService::operate($admin->org_id, $id, $params['type'], $params['resource_id'], $params['operate']);

        OperateLogService::create($this->auth()->id, '章节操作', ['org_id' => $admin->org_id, 'params' => $params]);

        return $this->success();
    }
}
