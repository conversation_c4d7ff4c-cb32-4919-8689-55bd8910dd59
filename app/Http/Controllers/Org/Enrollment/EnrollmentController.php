<?php

namespace App\Http\Controllers\Org\Enrollment;

use App\Http\Controllers\Org\Controller;
use App\Libs\Baidu\AipAPI;
use App\Models\Org\Enrollment;
use App\Models\Org\Export;
use App\Models\Org\OrgClass;
use App\Services\Common\AttachmentService;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\CoursePackService;
use App\Services\Org\CourseService;
use App\Services\Org\EnrollCourseService;
use App\Services\Org\EnrollmentService;
use App\Services\Org\Export\ExportFactory;
use App\Services\Org\TopicService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Throwable;

class EnrollmentController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'class_id' => 'integer',
            'type' => 'string',
            'resource_id' => 'integer',
            'name' => 'string',
            'phone' => 'string',
            'id_card_number' => 'string',
            'status' => 'integer',
            'created_at' => 'array'
        ]);

        $params['org_id'] = $this->auth()->org_id;

        $query = EnrollmentService::getSearchQuery($params);
        $builder = $query->with(['classroom', 'resource', 'student', 'archive']);
        $this->builderOrderBy($request, $builder);

        if (isset($params['name']) && $params['name']) {
            $builder->whereHas('student', function($q) use ($params) {
                $q->where('name', 'like', '%' . $params['name'] . '%');
            });
        }

        if (isset($params['phone']) && $params['phone']) {
            $builder->whereHas('student', function($q) use ($params) {
                $q->where('phone', 'like', '%' . $params['phone'] . '%');
            });
        }

        $data = $this->apiPaginate($request, $builder);

        /** @var Enrollment $record */
        foreach ($data['records'] as $record) {
            $record->append(['hour_cert_url', 'study_record_url']);

            $record->student->setHidden([]);
            $record->student = $record->toStudent();
            $record->resource = $record->toResource();

            $record->hour = EnrollCourseService::getStudyHour($record);
        }

        return $data;
    }

    public function destroy(int $id): array
    {
        $orgId = $this->auth()->org_id;

        EnrollmentService::remove($id, $orgId);

        OperateLogService::create($this->auth()->id, '删除报名信息', ['id' => $id, 'org_id' => $orgId]);

        return $this->success();
    }

    public function getOptions(): array
    {
        $admin = $this->auth();

        $topics = TopicService::getOrgTopics($admin->org_id);
        $courses = CourseService::getOrgCourses($admin->org_id);
        $coursePacks = CoursePackService::getOrgCoursePacks($admin->org_id);

        $classAll = OrgClass::query()
            ->where('org_id', $admin->org_id)
            ->orderBy('id', 'desc')
            ->limit(100)
            ->get()
            ->setHidden([])
            ->toArray();

        $classPadding = OrgClass::query()
            ->where('org_id', $admin->org_id)
            ->whereIn('status', [OrgClass::STATUS_DEFAULT, OrgClass::STATUS_STARTING])
            ->get()
            ->setHidden([])
            ->toArray();

        return [
            'classes' => $classAll,
            'classes_padding' => $classPadding,
            'topics' => $topics,
            'courses' => $courses,
            'course_packs' => $coursePacks,
        ];
    }

    public function examRetake(int $id): Enrollment
    {
        $orgId = $this->auth()->org_id;

        $enrollment = EnrollmentService::examRetake($id, $orgId);

        OperateLogService::create($this->auth()->id, '学员重新考试', ['id' => $id, 'org_id' => $orgId]);

        return $enrollment;
    }

    /**
     * 批量分配/更换
     * @throws Throwable
     */
    public function batchAssign(Request $request): array
    {
        $params = $request->validate([
            'type' => 'required|in:assign,changeClass,changeSubject',
            'enrollment_ids' => 'required|array',
            'class_id' => 'integer|nullable',
            'subject_type' => 'in:course,topic,course_pack|nullable',
            'subject_id' => 'integer|nullable'
        ]);

        $auth = $this->auth();

        $classId = empty($params['class_id']) ? 0 : $params['class_id'];
        $subjectType = empty($params['subject_type']) ? '' : $params['subject_type'];
        $subjectId = empty($params['subject_id']) ? 0 : $params['subject_id'];

        EnrollmentService::batchAssign($auth->org_id, $params['enrollment_ids'], $classId, $subjectType, $subjectId, $params['type']);

        OperateLogService::create($auth->id, '机构报名信息批量分配', ['org_id' => $auth->org_id, 'params' => $params]);

        return $this->success();
    }

    /**
     * 批量移除
     * @throws Throwable
     */
    public function batchRemove(Request $request): array
    {
        $params = $request->validate([
            'type' => 'required|in:class,subject',
            'enrollment_ids' => 'required|array'
        ]);

        $auth = $this->auth();

        if ($params['type'] == 'class') {
            EnrollmentService::batchRemoveClass($auth->org_id, $params['enrollment_ids']);
        } 
        EnrollmentService::batchRemoveSubject($auth->org_id, $params['enrollment_ids']);

        OperateLogService::create($auth->id, '机构报名信息批量移除', ['org_id' => $auth->org_id, 'params' => $params]);

        return $this->success();
    }

    public function getIdCardInfo(Request $request, AipAPI $baiDuOcr): array
    {
        $params = $request->validate([
            'id_card_front' => 'required|string',
        ]);

        $url = AttachmentService::url($params['id_card_front']);

        $cardInfo = $baiDuOcr->idCardFront($url);

        return [
            'path' => $url,
            'card_info' => $cardInfo
        ];
    }

    // 导入学员信息
    public function importEnrollment(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls',
        ]);

        $orgId = $this->auth()->org_id;

        // 获取上传的Excel文件
        $file = $request->file('file');

        // 调用服务导入学员信息
        $result = EnrollmentService::importEnrollment($orgId, $file);

        // 记录操作日志
        OperateLogService::create($this->auth()->id, '导入学员信息', [
            'org_id' => $orgId,
            'success' => $result['success'],
            'failed' => $result['failed']
        ]);

        return $result;
    }

    // 导入学员照片
    public function importEnrollmentPhoto(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:zip',
        ]);

        $orgId = $this->auth()->org_id;

        // 获取上传的ZIP文件
        $file = $request->file('file');

        $result = EnrollmentService::importPhoto($orgId, $file);

        // 记录操作日志
        OperateLogService::create($this->auth()->id, '导入学员照片', [
                'org_id' => $orgId,
                'success' => $result['success'],
                'failed' => $result['failed']
        ]);

        return $result;
    }

    public function getTestPaper(Request $request)
    {
        $params = $request->validate([
            'id' => 'required|integer',
        ]);

        $handler = ExportFactory::create(Export::TYPE_TEST_PAPER, $this->auth()->org_id, $params['id'], []);

        return $handler->fetchData();
    }

    public function hourCertImage(Request $request): array
    {
        $params = $request->validate([
            'id' => 'required|integer',
        ]);

        /** @var Enrollment $enroll */
        $enroll = Enrollment::query()->where('org_id', $this->auth()->org_id)->find($params['id']);

        if (!$enroll) {
            throw new BadRequestHttpException('参数错误');
        }

        if (!$enroll->hour_cert_image_url) {
            throw new BadRequestHttpException('未满足学时证明生成条件');
        }

        return [
            'image_url' => $enroll->hour_cert_image_url
        ];
    }
}
