<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ContentCollection extends ResourceCollection
{
    /**
     * 应该应用的「数据」包装器。
     *
     * @var string|null
     */
    public static $wrap = null;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $list = $this->collection;

        foreach ($list as &$item) {
            if (isset($item['resource'])) {
                if (isset($item['resource']['filepath'])) {
                    $item['resource']['filepath'] = $item['own_content'] ? $item['resource']['filepath'] : '';
                }
                if (isset($item['resource']['filepath_src'])) {
                    $item['resource']['filepath_src'] = $item['own_content'] ? $item['resource']['filepath_src'] : '';
                }
                if (isset($item['resource']['video_src'])) {
                    $item['resource']['video_src'] = $item['own_content'] ? $item['resource']['video_src'] : '';
                }
            }
        }
        unset($item);

        return $list->toArray();
    }
}
