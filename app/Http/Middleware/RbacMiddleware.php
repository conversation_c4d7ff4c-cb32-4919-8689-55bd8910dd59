<?php

namespace App\Http\Middleware;

use App\Models\Admin\Admin;
use App\Services\Admin\PermissionService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;

class RbacMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        /** @var Admin $admin */
        $admin = auth('admin')->user();
        if ($admin) {
            if (!PermissionService::checkApiPermission($admin, $request->route()->uri, $request->method())) {
                throw new NotAcceptableHttpException('无权限执行该操作');
            }
        }

        return $next($request);
    }
}
