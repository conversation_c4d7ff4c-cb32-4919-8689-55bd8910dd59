<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class Authenticate extends Middleware
{

    public function handle($request, \Closure $next, ...$guards)
    {
        $this->authenticate($request, $guards);

        //Todo
        //try {
        //$auth = $this->auth->user();
        //    UserService::checkStatus($auth->id, $auth->status);
        //} catch (\Exception $e) {
        //    throw new AccessDeniedHttpException('', $e->getMessage());
        //}

        return $next($request);
    }

    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    //protected function redirectTo(Request $request): ?string
    //{
    //    return $request->expectsJson() ? null : route('login');
    //}

}
