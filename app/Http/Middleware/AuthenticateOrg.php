<?php

namespace App\Http\Middleware;

use App\Models\Org\Admin\Admin;
use App\Services\Org\Admin\AdminService;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

class AuthenticateOrg extends Middleware
{
    public function handle($request, \Closure $next, ...$guards)
    {
        $this->authenticate($request, ['org']);

        try {
            /** @var Admin $auth */
            $auth = $this->auth->user();
            AdminService::checkStatus($auth);
        } catch (\Exception $e) {
           throw new AccessDeniedHttpException($e->getMessage());
        }

        return $next($request);
    }
}
