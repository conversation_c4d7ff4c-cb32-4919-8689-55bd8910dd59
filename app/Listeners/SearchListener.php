<?php

namespace App\Listeners;

use App\Events\SearchEvent;
use App\Models\Stat\SearchStat;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class SearchListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(SearchEvent $event): void
    {
        if (empty($event->keyword)) {
            return;
        }

        $date = Carbon::now()->toDateString();

        $stat = SearchStat::query()
            ->where('date', $date)
            ->where('keyword', $event->keyword)
            ->first();

        if ($stat) {
            $stat->increment('search_count');
        } else {
            SearchStat::query()->updateOrInsert(
                ['date' => $date, 'keyword' => $event->keyword],
                ['search_count' => DB::raw('search_count + 1')]
            );
        }
    }
}
