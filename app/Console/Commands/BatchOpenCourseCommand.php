<?php

namespace App\Console\Commands;

use App\Models\Cms\Category;
use App\Models\Cms\ContentCourse;
use App\Models\User;
use App\Models\User\UserOwnContent;
use App\Models\User\UserOwnTopic;
use Carbon\Carbon;
use Illuminate\Console\Command;

class BatchOpenCourseCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:batch-open-course';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '批量开通课程';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        /*$phones = [
            '15319753830',
            '15877391689',
            '18042918713'
        ];

        $userIds = User::query()->whereIn('phone', $phones)->pluck('id')->toArray();

        $courseIds = [708, 1202, 710, 709, 1360, 1371, 1375];
        $topicIds = [1, 6, 7, 14];

        foreach ($userIds as $userId) {
            foreach ($courseIds as $courseId) {
                $ownContent = new UserOwnContent();
                $ownContent->user_id = $userId;
                $ownContent->content_id = $courseId;
                $ownContent->classify = Category::CLASSIFY_COURSE;
                $ownContent->save();
            }
            foreach ($topicIds as $topicId) {
                $ownTopic = new UserOwnTopic();
                $ownTopic->user_id = $userId;
                $ownTopic->topic_id = $topicId;
                $ownTopic->expired_at = NULL;
                $ownTopic->save();
            }
        }*/


/*        $phones2 = [
            '18235273794',
            '17868841812',
            '15525610603',
            '18810580515'
        ];

        $userIds2 = User::query()->whereIn('phone', $phones2)->pluck('id')->toArray();

        $courseIds2 = [1202, 1360, 1371, 1375];
        $topicIds2 = [1, 6, 7, 14];

        foreach ($userIds2 as $userId) {
            foreach ($courseIds2 as $courseId) {
                $ownContent = new UserOwnContent();
                $ownContent->user_id = $userId;
                $ownContent->content_id = $courseId;
                $ownContent->classify = Category::CLASSIFY_COURSE;
                $ownContent->save();
            }
            foreach ($topicIds2 as $topicId) {
                $ownTopic = new UserOwnTopic();
                $ownTopic->user_id = $userId;
                $ownTopic->topic_id = $topicId;
                $ownTopic->expired_at = NULL;
                $ownTopic->save();
            }
        }*/

        $phones3 = [
            17684853361 => [
                'courseIds' => [708, 1202, 710, 709],
                'topicIds' => [1, 6, 7, 14]
            ],
            13987170808 => [
                'courseIds' => [708, 1202, 710, 1104],
                'topicIds' => [1, 6, 7, 8]
            ]
        ];

        $users = User::query()->whereIn('phone', array_keys($phones3))->select(['id', 'phone'])->get();

        /** @var User $user */
        foreach ($users as $user) {
            if (isset($phones3[$user->phone])) {
                foreach ($phones3[$user->phone]['courseIds'] as $courseId) {
                    $ownContent = new UserOwnContent();
                    $ownContent->user_id = $user->id;
                    $ownContent->content_id = $courseId;
                    $ownContent->classify = Category::CLASSIFY_COURSE;
                    $ownContent->save();
                }

                foreach ($phones3[$user->phone]['topicIds'] as $topicId) {
                    $ownTopic = new UserOwnTopic();
                    $ownTopic->user_id = $user->id;
                    $ownTopic->topic_id = $topicId;
                    $ownTopic->expired_at = Carbon::now()->addMonths(6)->toDateTimeString();
                    $ownTopic->save();
                }
            }
        }
    }
}
