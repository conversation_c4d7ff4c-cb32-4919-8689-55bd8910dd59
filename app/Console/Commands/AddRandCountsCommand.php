<?php

namespace App\Console\Commands;

use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentDoc;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class AddRandCountsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'content:add-rand-counts {--min=1000} {--max=2000}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '为内容添加随机的叠加浏览下载等计数';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $min = $this->option('min');
        $max = $this->option('max');

        Content::query()
            ->where('views_add', 0)
            ->chunkById(100, function($rows) use ($min, $max) {
                /** @var Content[]|Collection $rows */
                foreach ($rows as $content) {
                    $content->views_add = rand($min, $max);
                    $content->save();
                    $this->info("内容 {$content->id} 叠加浏览计数设为 {$content->views_add}.");
                }
            });

        ContentDoc::query()
            ->where('download_count_add', 0)
            ->chunkById(100, function($rows) use ($min, $max) {
                /** @var ContentDoc[]|Collection $rows */
                foreach ($rows as $doc) {
                    $doc->download_count_add = rand($min, $max);
                    $doc->save();
                    $this->info("文档 {$doc->content_id} 叠加下载数设为 {$doc->download_count_add}.");
                }
            });

        ContentCourse::query()
            ->where('learning_count_add', 0)
            ->chunkById(100, function($rows) use ($min, $max) {
                /** @var ContentCourse[]|Collection $rows */
                foreach ($rows as $course) {
                    $course->learning_count_add = rand($min, $max);
                    $course->save();
                    $this->info("课程 {$course->content_id} 叠加在学人数设为 {$course->learning_count_add}.");
                }
            });
    }

}
