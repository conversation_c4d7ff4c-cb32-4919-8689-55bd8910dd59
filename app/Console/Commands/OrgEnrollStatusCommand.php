<?php
/**
 * OrgEnrollStatusCommand.php class file.
 *
 * <AUTHOR>
 * @time 2025/4/29 09:39
 * @copyright 2025 pp.cc All Right Reserved
 */

namespace App\Console\Commands;

use App\Models\Org\Enrollment;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class OrgEnrollStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'enrollment:expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '未分班开课学员过期状态处理';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $now = Carbon::now();
        $logger = Log::channel('task');

        // 查询开课已过期状态为学习中的学员
        $query = Enrollment::query()
            ->where('class_id', 0)
            ->where('status', Enrollment::STATUS_LEARNING)
            ->where('expired_at', '<=', $now);

        $query->chunkById(500, function ($enrollments) use ($logger) {
            $ids = $enrollments->pluck('id')->toArray();
            Enrollment::query()
                ->whereIn('id', $ids)
                ->update([
                    'status' => Enrollment::STATUS_EXPIRED,
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ]);

            $logger->info('更新学员过期状态成功:' . implode(',', $ids));

        }, 'id');
    }
}
