<?php

namespace App\Console\Commands;
use App\Models\Admin\Admin;
use App\Models\Admin\Role;
use App\Models\Admin\UserRole;
use App\Models\Org;
use App\Services\Stat\DailyOrgService;
use App\Services\Stat\DailyOverviewService;
use App\Services\Stat\DailyPromoterService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Query\Builder;

class StatisticCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'statistic {type=yesterday}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '每日数据统计';

    public function handle(): void
    {
        $type = $this->argument('type');

        switch ($type) {
            case 'yesterday':
                // 更新昨天数据

                // 平台统计
                DailyOverviewService::yesterday();

                // 服务商统计
                Admin::query()
                    ->where('status', Admin::STATUS_ENABLED)
                    ->chunkById(100, function ($admins) {
                        foreach ($admins as $admin) {
                            DailyPromoterService::yesterday($admin->id);
                        }
                    });

                // 机构统计
                Org::query()
                    ->chunkById(100, function ($list) {
                        foreach ($list as $item) {
                            DailyOrgService::yesterday($item->id);
                        }
                    });
                break;

            case 'today';
                // 更新今天数据
                $date = Carbon::now()->toDateString();

                // 平台统计
                DailyOverviewService::yesterday($date);

                // 服务商统计
                Admin::query()
                    ->where('status', Admin::STATUS_ENABLED)
                    ->chunkById(100, function ($admins) use ($date) {
                        foreach ($admins as $admin) {
                            DailyPromoterService::yesterday($admin->id, $date);
                        }
                    });

                // 机构统计
                Org::query()
                    ->chunkById(100, function ($list) use ($date) {
                        foreach ($list as $item) {
                            DailyOrgService::yesterday($item->id, $date);
                        }
                    });
                break;
        }
    }
}
