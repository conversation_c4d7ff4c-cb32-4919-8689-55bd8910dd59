<?php
namespace App\Console\Commands;

use App\Models\Org\OrgClass;
use App\Models\Org\Enrollment;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\OrgClassService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class OrgClassesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'classes {type=start}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '开班或结班级处理';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $now = Carbon::now();
        $endOfDay = $now->copy()->endOfDay(); // 今天最后一秒
        $logger = Log::channel('task');

        $type = $this->argument('type');

        switch ($type) {
            case 'start':
                // 获取需要开班的班级ID列表
                $classIds = OrgClass::query()
                    ->where('status', OrgClass::STATUS_DEFAULT)
                    ->where('start_at', '<=', $endOfDay)
                    ->pluck('id')
                    ->toArray();

                if (!empty($classIds)) {
                    // 更新班级状态
                    $classCount = OrgClass::query()
                        ->whereIn('id', $classIds)
                        ->update([
                            'status' => OrgClass::STATUS_STARTING,
                            'updated_at' => $now->toDateTimeString(),
                        ]);

                    // 更新报名记录状态
                    $enrollmentCount = Enrollment::query()
                        ->whereIn('class_id', $classIds)
                        ->where('status', Enrollment::STATUS_PENDING)
                        ->update([
                            'status' => Enrollment::STATUS_LEARNING,
                            'updated_at' => $now->toDateTimeString(),
                        ]);

                    $logger->info("开班处理完成", [
                        'execution_time' => $now,
                        'end_of_day' => $endOfDay,
                        'processed_classes' => $classCount,
                        'processed_enrollments' => $enrollmentCount
                    ]);
                }
                break;
            
            case 'finish':
                // 获取需要结班的班级列表
                $classes = OrgClass::query()
                    ->where('status', OrgClass::STATUS_STARTING)
                    ->where('end_at', '<=', $endOfDay)
                    ->select(['id', 'org_id'])
                    ->get();

                if ($classes->isNotEmpty()) {
                    $classIds = $classes->pluck('id')->toArray();
                    
                    // 更新班级状态
                    $classCount = OrgClass::query()
                        ->whereIn('id', $classIds)
                        ->update([
                            'status' => OrgClass::STATUS_FINISHED,
                            'actual_end_at' => $now->toDateTimeString(),
                            'updated_at' => $now->toDateTimeString(),
                        ]);

                    foreach ($classes as $class) {
                        OrgClassService::finished($class->id, $class->org_id);
                        OperateLogService::create(0, '班级结束', ['org_id' => $class->org_id, 'class_id' => $class->id, 'params' => '定时任务']);
                    }


                    $logger->info("结班处理完成", [
                        'execution_time' => $now,
                        'end_of_day' => $endOfDay,
                        'processed_classes' => $classCount,
                    ]);
                }
                break;
        }
    }
}