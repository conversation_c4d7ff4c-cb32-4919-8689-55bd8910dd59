<?php

namespace App\Console\Commands;

use App\Models\Admin\Menu;
use Illuminate\Console\Command;

class InterimCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:interim';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 初始化数据
        // $this->menus();
    }

    protected function menus()
    {
        // 菜单初始化
        // $menu = Menu::query()->firstOrCreate([
        //     'route_name'=>'org',
        //     'route_path' => '/org',
        //     'method' => 'GET',
        //     'type' => Menu::TYPE_MENU,
        // ], [
        //     'parent_id' => 0,
        //     'name' => '机构管理',
        //     'icon' => 'gg:organisation',
        //     'sort' => 11,
        //     'status' => 1,
        // ]);
        //
        // Menu::query()->firstOrCreate([
        //     'route_name'=>'org_index',
        //     'route_path' => '/org/index',
        //     'method' => 'GET',
        //     'type' => Menu::TYPE_MENU,
        // ], [
        //     'parent_id' => $menu->id,
        //     'name' => '机构列表',
        //     'icon' => 'charm:organisation',
        //     'sort' => 0,
        //     'status' => 1,
        // ]);
        //
        // Menu::query()->firstOrCreate([
        //     'route_name'=>'org_resource',
        //     'route_path' => '/org/resource',
        //     'method' => 'GET',
        //     'type' => Menu::TYPE_MENU,
        // ], [
        //     'parent_id' => $menu->id,
        //     'name' => '机构资源设置',
        //     'icon' => 'mdi:book-open-outline',
        //     'sort' => 0,
        //     'status' => 1,
        // ]);
    }
}
