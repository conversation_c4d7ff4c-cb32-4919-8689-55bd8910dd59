<?php

namespace App\Console\Commands;

use App\Services\Chat\ChatSessionService;
use Illuminate\Console\Command;

class ChatRecommendCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:chat-recommend';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '定时更新问答的推荐问题';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $questions = app(ChatSessionService::class)->setRecommend(20);

        print_r($questions);
    }
}
