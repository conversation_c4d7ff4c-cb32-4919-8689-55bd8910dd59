<?php

namespace App\Console\Commands;

use App\Jobs\CourseProgressUpdateJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CourseProgressCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'course:progress';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '学员课程进度更新';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        CourseProgressUpdateJob::dispatch();
    }
}