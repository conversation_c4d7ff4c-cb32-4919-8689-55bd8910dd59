<?php

namespace App\Console\Commands;

use App\Core\Enums\BusinessType;
use App\Libs\AsyncTasks\ContentCourseSectionTranscodeTask;
use App\Libs\AsyncTasks\ContentVideoTranscodeTask;
use App\Libs\AsyncTasks\QiniuTranscodeTestTask;
use App\Libs\AsyncTasks\QiniuVideoTranscodeTask;
use App\Libs\AsyncTasks\WpsDocScreenshotTask;
use App\Libs\Filesystem\FileUtil;
use App\Libs\Wechat\EasyWechatFactory;
use App\Libs\Wps\Converter;
use App\Models\AsyncTask;
use App\Models\Attachment\AttachmentFile;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourseSection;
use App\Models\Cms\ContentVideo;
use App\Models\Cms\Special;
use App\Models\Ers\ServiceOrder;
use App\Models\Inspect\Device;
use App\Models\Inspect\DeviceItem;
use App\Models\Inspect\DeviceItemsRecord;
use App\Models\Order\Order;
use App\Models\Org;
use App\Models\Org\Export;
use App\Models\Qa\Question;
use App\Models\Train\SubjectOption;
use App\Models\Train\Topic;
use App\Models\User;
use App\Models\User\UserCreditLog;
use App\Services\Common\AsyncTaskService;
use App\Services\Common\AttachmentService;
use App\Services\Common\OrderService;
use App\Services\Common\PaymentService;
use App\Services\Common\RegionService;
use App\Services\Ers\ServiceOrderService;
use App\Services\Org\CaptureService;
use App\Services\Org\EnrollConfigService;
use App\Services\Org\Export\ExportFactory;
use App\Services\Org\Export\HourCertExporter;
use App\Services\Org\Export\HourRecordExporter;
use App\Services\Org\Export\StudentArchiveExporter;
use App\Services\Org\ExportService;
use App\Services\Org\TemplateReplace\ReplaceService;
use App\Services\User\BalanceService;
use App\Services\User\CreditService;
use App\Services\User\InvitationService;
use App\Services\User\VisitorService;
use App\Services\WechatService;
use cardinalby\ContentDisposition\ContentDisposition;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\GifWriter;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Overtrue\EasySms\EasySms;
use Overtrue\EasySms\Exceptions\NoGatewayAvailableException;
use PhpOffice\PhpWord\TemplateProcessor;
use Qiniu\Config;
use Qiniu\Http\Client;
use Qiniu\Http\Error;
use Qiniu\Processing\PersistentFop;
use App\Extensions\CustomNewAccessToken;
use App\Services\Org\Export\ValueItem;
use App\Services\Org\Export\ValueList;
use Symfony\Component\Process\Process;

class TestCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test {method? : What method you want to test?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Developing test';

    /**
     * 检索、提示、并执行要运行的调试方法
     */
    public function handle()
    {
        $method = $this->argument('method');

        if (!$method) {
            $internalMethods = ['handle'];
            $testMethods = [];

            $ref = new \ReflectionClass($this);
            foreach ($ref->getMethods() as $method) {
                if (!in_array($method->getName(), $internalMethods) && $method->getDeclaringClass()->getName() == self::class) {
                    $testMethods[] = $method->getName();
                }
            }

            $method = $this->anticipate('What method you want to test?', $testMethods);
        }

        if (!method_exists($this, $method)) {
            $this->error("Test method '$method' does not exists.");
            return;
        }

        $this->$method();
    }

    public function wechatNotice()
    {
        $openId = 'onlCa61XrW8GaD2POAwnCe72WyTA';
        $templateId = 'FY4re6FqhaDqR4XAHmQqt9WXrsKN-rR14IKxRM35dTE';
        $data = [
            'character_string1' => ['value' => 'LHX1574149043'],
            'phrase7' => ['value' => '进行中'],
            'phrase8' => ['value' => '待服务'],
            'thing3' => ['value' => '平台已报价，请尽快确认是否维修'],
            'date2' => ['value' => '2019-11-27 16:00:00']
        ];

        EasyWechatFactory::sendSubscribeMessage($openId, $templateId, $data);
    }

    public function wechatQrcode()
    {
        $response = EasyWechatFactory::mp()->getClient()->post('wxa/getwxacodeunlimit', [
            'json' => [
                'scene' => 'org=HelloWorld'
            ]
        ]);

        echo "Code: ".$response->getStatusCode()."\n";

        $fileContent = $response->getContent();
        $tmp = storage_path('app/tmp');
        file_put_contents($tmp.'/qrcode.png', $fileContent);

        echo "FileSize: ".strlen($fileContent)."\n";
    }

    public function wechatService()
    {
        echo WechatService::makeScene('qrlogin', ['hello'=>'world']);
    }

    public function sendSms()
    {
        try {
            $easysms = app(EasySms::class);
            $ret = $easysms->send('+8618019580609', [
                'content' => '您的验证码为: 6379',
                'template' => 'd3944ea0477341df893822bbf8a76502',
                'data' => ['1545'],
            ]);
            dd($ret);
        } catch (NoGatewayAvailableException $e) {
            foreach ($e->getExceptions() as $ee) {
                echo $ee;
            }
        }
    }

    public function jwt()
    {
        $auth = auth()->guard('api');

        $user = User::find(23);
        $token = $auth->login($user);
        //$token = $auth->tokenById(1);
        $ttl = $auth->factory()->getTTL();

        $this->table([], [
            ['ttl', $ttl],
            ['token', $token]
        ]);
    }

    public function attachment()
    {
        $disk = config('heguibao.storage.pub');
        $d = AttachmentService::getUploadForm(1, 'image', diskName: $disk);
        dd($d);

        //  $file = AttachmentService::store('qiniu-pub:tmp/1-65fd1ffdd9f77.png', 'test', BusinessType::Content, 1, 'WX20230928-114430.png');
        //  dd($file);

        // $file = AttachmentFile::query()->first()?->toArray();
        // dd($file);

        // $count = AttachmentService::removeRelations(BusinessType::Content, 1, 'test/202403/2214/Fs1jsi7ck14j9Rd3AV4Ct5YoZ2-V.png');
        // dd($count);

        //$file = AttachmentFile::query()->first()?->append('url')->toArray();
        //dd($file);

        // $info = AttachmentService::download('https://files.zsxq.com/lqEPsnzaClh6hOaEizxvR_jlR-OV?attname=%E3%80%90%E9%9A%90%E6%82%A3%E5%9B%BE%E5%86%8C%E3%80%91%E4%BC%81%E4%B8%9A%E5%B8%B8%E8%A7%81%E9%9A%90%E6%82%A3%E5%9B%BE%E7%89%87%E8%A7%A3%E6%9E%90%20%28%E4%B8%8B%29%EF%BC%88143%E9%A1%B5%EF%BC%89.pptx&e=1711962921&token=kIxbL07-8jAj8w1n4s9zv64FuZZNEATmlU_Vm6zD:cB6Ddk_axbIVpmMNRnRwpA40esY=');
        // dd($info);

        // dd(AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), 'content/202404/0210/FtORk8s5lZDKQBLaHPG7w4KK4-Ja.docx', '07 某煤矿春节放假复工安全技术措施（4页）.docx'));
    }

    public function attachmentUpload()
    {
        //$file = AttachmentService::storeFromLocal(base_path('storage/app/public/selftest_share.png'), 'qiniu-pub', 'test', BusinessType::LearnCapture, 5);
        //dd($file);

        //AttachmentService::removeRelations(BusinessType::LearnCapture, 5);

        $result = AttachmentService::upload(storage_path('app/public/selftest_share.png'), config('heguibao.storage.local'));
        dd($result);
    }

    public function fileUtil()
    {
        // $info = FileUtil::detectFile('https://files.zsxq.com/lqEPsnzaClh6hOaEizxvR_jlR-OV?attname=%E3%80%90%E9%9A%90%E6%82%A3%E5%9B%BE%E5%86%8C%E3%80%91%E4%BC%81%E4%B8%9A%E5%B8%B8%E8%A7%81%E9%9A%90%E6%82%A3%E5%9B%BE%E7%89%87%E8%A7%A3%E6%9E%90%20%28%E4%B8%8B%29%EF%BC%88143%E9%A1%B5%EF%BC%89.pptx&e=1711962921&token=kIxbL07-8jAj8w1n4s9zv64FuZZNEATmlU_Vm6zD:cB6Ddk_axbIVpmMNRnRwpA40esY=', mime: 'application/zip', contentDisposition: 'application/zipattachment; filename="【隐患图册】企业常见隐患图片解析 (下)（143页）.pptx"; filename*=utf-8\'\'%E3%80%90%E9%9A%90%E6%82%A3%E5%9B%BE%E5%86%8C%E3%80%91%E4%BC%81%E4%B8%9A%E5%B8%B8%E8%A7%81%E9%9A%90%E6%82%A3%E5%9B%BE%E7%89%87%E8%A7%A3%E6%9E%90%20%28%E4%B8%8B%29%EF%BC%88143%E9%A1%B5%EF%BC%89.pptx');
        // dd($info);

        $info = FileUtil::detectFile('https://solution-provider.ks3-cn-beijing.ksyun.com/convert/jpg/02997e850223d8c0b2ac03d361e3b47a116f54cf/Sheet1_1_01.jpg?Expires=**********&KSSAccessKeyId=AKLTKVSHxfgqTr2XXElVZy9w&Signature=agl6FSxR2HOGBpw6KfRB30eoOEs%3D&response-content-disposition=attachment%3Bfilename%2A%3DUTF-8%27%27Sheet1_1_01.jpg&response-content-type=image%2Fjpeg', mime: 'image/jpeg', contentDisposition: 'attachment;filename*=UTF-8\'\'Sheet1_1_01.jpg');
        dd($info);
    }

    public function wpsConvert()
    {
        $converter = app(Converter::class);

        //$data = $converter->toJpg('https://img3.pp.cc/tmp/24342000000015204094.pdf', '24342000000015204094.pdf', [
        //    'ranges' => '1'
        //]);
        //
        //print_r($data);

        //$data = $converter->toJpg('https://img3.pp.cc/tmp/%E5%BF%AB%E6%89%8B%E5%BC%80%E5%8F%91%E8%80%85%E5%85%A5%E9%A9%BB%E8%B5%84%E8%B4%A8%E8%AE%A4%E8%AF%81%E7%94%B3%E8%AF%B7%E5%85%AC%E5%87%BD%20%E6%96%B0%E6%96%B0(1)%20(1).docx', '快手开发者入驻资质认证申请公函 新新(1) (1).docx', [
        //    'ranges' => '1'
        //]);

        //$data = $converter->toJpg('https://img3.pp.cc/tmp/%E9%99%86%E5%9C%B0%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E4%B8%AD%E9%A9%AC%E9%99%86%E7%9A%84%E7%94%9F%E6%80%81%E5%8A%9F%E8%83%BD.pdf', '陆地生态系统中马陆的生态功能.pdf', [
        //    'ranges' => '1-10'
        //]);

        //$data = $converter->toJpg('https://img3.pp.cc/tmp/%E5%BE%AE%E5%8D%9A-0302.xlsx', '微博-0302.xlsx');

        //open:vjnrpilonmjtawuiktjdcebbnqqgwhr
        //open:wlhmvuuaoibavffelplwfkrcfsgvnhh
        //open:sxejbtuksrwdzaulqrpgwkpmhzbllfj
        //open:tfxpboyzhifcigxlsgvsqoxlanjpiin
        //$data = $converter->getTask($data['task_id']);
        $data = $converter->getTask('open:lczyroqwlpneockfkkotndidszkhasq');

        if (isset($data)) {
            dd($data);
        }
    }

    public function orderService()
    {
        // $content = Content::find(9);
        // $order = OrderService::create(23, $content, 29)?->toArray();
        // dd($order);

        // $order = OrderService::create(23, new UserCreditLog(), 20);
        // dd($order->toArray());

        // $order = OrderService::create(23, Content::find(23), 15);
        // dd($order->toArray());

        //$order = OrderService::create(23, Special::find(1), 15);
        //dd($order->toArray());

        $order = OrderService::create(23, Topic::find(1), 30, ['valid_days'=>30]);
        //dd($order->toArray());

//        PaymentService::complete('240328143915000234019', '92304786548236496', 30, now());
        PaymentService::complete('250709141340001036960', '92304786548236496', 0.01, now());
    }

    public function credit()
    {
        InvitationService::invite('FrVAnmhEEOUWCqYYuHuja', 22);
        //CreditService::recharge(1, 100, 'order', 1, 'asdijfoasjdif');
    }

    public function asyncTask()
    {
        //文档文件截图
        // $task = new WpsDocScreenshotTask('https://img3.pp.cc/tmp/24342000000015204094.pdf', '24342000000015204094.pdf');
        // $model = AsyncTaskService::start($task, BusinessType::Content, 1);
        // dd($model?->toArray());

        //单视频内容转码
        //$task = new ContentVideoTranscodeTask(31);
        //$model = AsyncTaskService::start($task, BusinessType::Content, 31);
        //dd($model?->toArray());

        // $model = AsyncTask::query()->where('id', 3)->first();
        // $task = unserialize($model->task);
        // $task->check($model);

        // $ret = AsyncTaskService::complete(9);
        // dd($ret);

        //单独测试七牛视频转码
        $video6x19 = 'tmp/1891443748753592473.mp4';
        $video19x6 = 'content/202403/2215/lq_ydiN0yCVXSNv0oGtZIytTPk1B.mp4';
        $task = new QiniuTranscodeTestTask($video6x19, addWatermark: true);
        $model = AsyncTaskService::start($task, BusinessType::Content, 1);
        dd($model?->toArray());
    }

    public function avInfo()
    {
        $avInfoUrl = Storage::disk('qiniu-priv')->getAdapter()->privateDownloadUrl('tmp/1891443748753592473.mp4?avinfo');
        $avInfo = Client::get($avInfoUrl)->json();

        foreach ($avInfo['streams'] as $i => $row) {
            //如果相同的 codec_type 出现两次，则保留 codec_time_base 不是 0/1 的那个（0/1 的极有可能不是正常的音视频流，如 codec_name 是 png 的假视频流）
            if (!isset($avInfo['streams'][$row['codec_type']]) || (isset($row['codec_time_base']) && $row['codec_time_base'] != '0/1')) {
                $avInfo['streams'][$row['codec_type']] = $row;
            } else {
                //其它 codec_type 重复的部分追加个序号继续保留
                $avInfo['streams'][$row['codec_type'].'_'.$i] = $row;
            }
            unset($avInfo['streams'][$i]);
        }

        dd($avInfo);
    }

    public function qiniuTaskInfo()
    {
        $auth = Storage::disk('qiniu-priv')->getAdapter()->getAuthManager();

        //发送处理请求
        $config = new Config();
        $config->useHTTPS = true;
        $persistent = new PersistentFop($auth, $config);

        /** @var Error|null $err */
        [$ret, $err] = $persistent->status('z0.01z001d7vj803wsdor00mvdzfn002m8e');

        dd($ret, $err);
    }

    public function filesystem()
    {
        $path = Storage::disk('qiniu')->path('tmp/asdasd.jpg');
        dd($path);
    }

    public function content()
    {
        $contentVideo = ContentVideo::query()->with('content', fn($q) => $q->publicFields())->find(21);

        $extend = $contentVideo->extend;
        $extend['yes'] = true;

        $contentVideo->extend = $extend;
        $contentVideo->save();

        dd($contentVideo?->toArray());
    }

    public function visitor()
    {
        $rr = VisitorService::getRelevant('Wi1nXlbez_MjqicbEUTV9', 0);
        dd($rr);
    }

    public function qa()
    {
        $qa = Question::query()
            ->with('tags')
            ->first();

        dd($qa?->toArray());
    }

    public function preg()
    {
        preg_match_all('/#([a-zA-Z0-9_\x{4e00}-\x{9fa5}]+)(?=\s|#|\b|$)/u', '这是一个 #奇怪的#测试Ab_Test，雅 你懂的', $matches);

        print_r($matches);
    }

    public function orderSteps()
    {
        $serviceOrder = ServiceOrder::find(12);

        echo $serviceOrder->sid;

        $flows = ServiceOrderService::getFlows($serviceOrder, true);

        dd($flows);
    }

    public function arrayFilter()
    {
        print_r(array_filter([['file_id' => 1, 'path' => '123'], ['file_id' => 2, 'path' => '456']], fn($file) => $file['path'] == '123')[0]);
    }

    public function balanceService()
    {
        //$balance = BalanceService::recharge(27, 10000, BusinessType::Order, 0, '充值测试');
        $balance = BalanceService::consume(27, 55, BusinessType::ErsOrder, 12, '消费测试');
        dd($balance);
    }

    public function orgBalanceService()
    {
        //$record = \App\Services\Org\BalanceService::recharge(8, 50000, remark: '充值测试');
        //$record = \App\Services\Org\BalanceService::consume(8, 10, 2, '扣费测试');
        $record = \App\Services\Org\BalanceService::refund(8, 2, '换课退款');
        dd($record->toArray());
    }

    public function getToken()
    {
        /** @var User $user */
        $user = User::query()->find(25);
        $platform = 'PC';
        $token = $user->createToken('PC - macOS 10.15.7', extraData: ['platform' => $platform]);

        dd($token);
    }

    public function testCallback()
    {
        /** @var AsyncTask $asyncTask */
        $asyncTask = AsyncTask::query()->find(14781);
        $ret = json_decode($asyncTask->callback_data, true);
        AsyncTaskService::checkCallback($asyncTask, fn() => QiniuVideoTranscodeTask::taskStatusCheck($ret) ? $ret : false);
    }

    public function idToSid()
    {
        dd(Org::encodeId(8));
    }

    public function captureService()
    {
        $r = CaptureService::makeCode(2, 23, 99);
        dd($r);
    }

    public function timeDiff()
    {
        $time1 = Carbon::parse('2024-03-20 10:30:00');
        $time2 = Carbon::parse('2024-03-20 10:35:45');

        $diff = $time1->diff($time2);
        $result = sprintf("%02d:%02d", $diff->i, $diff->s);
        echo $result; // 输出: 05:45
    }

    /**
     * 导出值结构使用示例
     */
    public function exportValue()
    {
        $items = [];

        $items[] = ValueItem::text('姓名', '张三');
        $items[] = ValueItem::options('性别', ['男', '女'], '男');
        $items[] = ValueItem::options('爱好', ['写代码', '玩游戏', '骑自行车', '开战斗机', '钓鱼', '撸猫'], ['开战斗机', '撸猫']);
        $items[] = ValueItem::list('用户列表', User::query()->lazy(10), function(User $user) {
            return [
                ValueItem::text('ID', $user->id),
                ValueItem::text('昵称', $user->nickname),
                ValueItem::text('最后登录时间', (string)$user->last_logged_at),
                ValueItem::image('头像', $user->avatar),
            ];
        });

        foreach ($items as $item) {
            echo $item->name.': ';

            switch ($item->type) {
                case 'text':
                case 'options':
                    echo $item;
                    break;

                case 'image':
                    echo '[图像]'.$item->getUrl();
                    break;

                case 'list':
                    echo "\n=========================\n";
                    foreach ($item as $row) {
                        foreach ($row as $field) {
                            echo '  '.$field->name.': '.$field;
                        }
                        echo "\n";
                    }
                    break;
            }

            echo "\n";
        }
    }

    public function export()
    {
        //$data = unserialize(file_get_contents(storage_path('app/public/templates/hour_records.txt')));
        //$tmpPath = ReplaceService::replace(storage_path('app/public/templates/学时记录.docx'), $data);
        //dd($tmpPath);

        //$handler = ExportFactory::create(Export::TYPE_TEST_PAPER, 8, 2, []);
        $handler = new HourRecordExporter(39, 45, []);

        // 处理导出任务
        list($fileName, $tempPath) = $handler->handle();
        dd($fileName, $tempPath);

        //$enroll = Org\Enrollment::query()->where('id', 26)->first();
        //
        //$items = ExportService::parseExtra($enroll->org_id, $enroll->extra);
        //dd($items);
    }

    public function hourRecordsData()
    {
        $data = unserialize(file_get_contents(storage_path('app/public/templates/hour_records.txt')));
        dd($data);
    }

    public function studentArchiveExporter()
    {
        /** @var Org\Enrollment $enroll */
        $enroll = Org\Enrollment::query()->find(14);
        $exporter = new StudentArchiveExporter($enroll->org_id, $enroll->class_id, []);
        $arr = $exporter->handle();

        dd($arr);
    }

    public function testPaperExporter()
    {
        /** @var Org\Enrollment $enroll */
        $enroll = Org\Enrollment::query()->where('id', 14)->first();
        $handler = ExportFactory::create(Export::TYPE_TEST_PAPER, $enroll->org_id, $enroll->id, []);
        $data = $handler->fetchData();

        dd($data);
    }

    public function hourRecordExporter()
    {
        $enroll = Org\Enrollment::query()
            ->where('id', 14)
            ->first();

        $exporter = new HourRecordExporter($enroll->org_id, $enroll->id, []);
//        $exporter = new HourCertExporter($enroll->org_id, 14, []);

        $arr = $exporter->handle();

        dd($arr);
    }

    public function regionService()
    {
        $region = RegionService::code2text(110102);
        dd($region);
    }

    public function qrcode()
    {
        $qrCode = new QrCode('Life is short, I use python.');
        $writer = new GifWriter();
        $result = $writer->write($qrCode);
        $result->saveToFile(AttachmentService::tmpPath('qrcode.png'));
    }

    public function templateProcessor()
    {
        $template = AttachmentService::tmpPath('学时证明模板.docx');
        $pic = AttachmentService::tmpPath('8e582fda12f8169131bf2118b3f0d2dc.png');
        $qrcode = AttachmentService::tmpPath('response.jpeg');

        $processor = new TemplateProcessor($template);

        $prefix = '培训记录.';
        $suffix = '#1';

        $processor->cloneRow($prefix.'应修学时', 3);

        $variables = $processor->getVariables();

        $loopPrefix = $prefix.'#1';
        if (in_array($loopPrefix.'抓拍记录', $variables) && in_array('/'.$loopPrefix.'抓拍记录', $variables)) {
            $ownPrefix = $loopPrefix.'抓拍记录.';
            $processor->cloneBlock($ownPrefix.'抓拍记录', 2, true, true, true);
        }

        $processor->setValue('机构名称', '合肥网漫网络科技有限公司');
        $processor->setImageValue('照片', fn() => ['path' => $pic]);
        $processor->setImageValue('二维码', fn() => ['path' => $qrcode]);

        $processor->saveAs(AttachmentService::tmpPath('学时证明-已生成.docx'));
    }

    public function modelDirtyProperty()
    {
        $user = User::query()->where('id', 33)->first();
        $user->password = 'hello '.uniqid();

        $this->info('保存前 isDirty: '.($user->isDirty('password') ? 'true' : 'false'));
        $this->info('保存前 changes: '.json_encode($user->getChanges()));

        $user->save();

        $this->info('保存后 isDirty: '.($user->isDirty('password') ? 'true' : 'false'));
        $this->info('保存后 changes: '.json_encode($user->getChanges()));
    }

    // 创建巡检设备
    public function createInspectDevice()
    {
        $devices = [
            [
                'name' => '设备1',
                'items' => [
                    '设备1-巡检项1',
                    '设备1-巡检项2',
                    '设备1-巡检项3'
                ]
            ],
            [
                'name' => '设备2',
                'items' => [
                    '设备2-巡检项1',
                    '设备2-巡检项2',
                ]
            ],
        ];
        foreach ($devices as $deviceObj) {
            $device = new Device();
            $device->fill([
                'name' => $deviceObj['name'],
                'inspection_item_count' => count($deviceObj['items']),
            ]);
            $device->save();
            foreach ($deviceObj['items'] as $item) {
                $deviceItem = new DeviceItem();
                $deviceItem->fill([
                    'device_id' => $device->id,
                    'name' => $item,
                ]);
                $deviceItem->save();
            }
        }
        $this->info('设备创建成功');
    }
    // 创建巡检任务
    public function createInspectTask()
    {
        // 准备测试数据
        $name = '测试每3天巡检';
        $frequency = 3;
        $devices = [1];

        // 调用被测方法,测试时内部userID自定义指定
        $task = app(\App\Services\Inspect\InspectTaskService::class)->create($name, $frequency, $devices);
        dd($task->toArray());
    }

    // 获取用户任务
    public function getUserTask()
    {
        $taskList = app(\App\Services\Inspect\InspectTaskService::class)->getOwnTaskListByUserID(25);
        dd($taskList);
    }

    // 生成任务巡检记录表
    public function generateDeviceRecordTable()
    {
        $task = app(\App\Models\Inspect\Task::class)->find(2);
        app(\App\Services\Inspect\InspectTaskRecordService::class)->generateTaskDeviceRecordTable($task);
        dd("ok");
    }

    // 更新任务设备记录表
    public function updateTaskDeviceRecord()
    {

        app(\App\Services\Inspect\InspectTaskRecordService::class)->updateTaskDeviceRecord(
            4,
            [
                1 => DeviceItemsRecord::NORMAL_STATUS,
                2 => DeviceItemsRecord::ABNORMAL_STATUS,
                3 => DeviceItemsRecord::NORMAL_STATUS,
            ],
            [
                '/storage/2023/03/07/20230307000001.jpg',
                '/storage/2023/03/07/20230307000002.jpg',
            ],
            '巡检项2异常',
            '建议更换或维修'
        );
        dd("ok");
    }

    // 获取用户巡检任务记录表
    public function getTaskDetail()
    {
        $task = app(\App\Services\Inspect\InspectTaskService::class)->getTaskRecordTableByTaskID(2);
        dd($task);
    }
    public function addAssocDevice()
    {
        app(\App\Services\Inspect\InspectTaskService::class)->addAssocDevice(2, [1, 2]);
        dd("ok");
    }

    // 获取审批列表
    public function getApprovalList()
    {
        $approvalList = app(\App\Services\Inspect\InspectApprovalService::class)->getApprovalByTaskID(1);
        dd($approvalList);
    }

    // 获取设备巡检项
    public function getDeviceItems()
    {
        $deviceItems = app(\App\Services\Inspect\InspectDeviceService::class)->getTaskDeviceItemsByDeviceID(2);
        dd($deviceItems);
    }

    public function getDeviceRecord()
    {
        $deviceRecord = app(\App\Services\Inspect\InspectDeviceService::class)->getTaskDeviceRecordByRecordID(1);
        dd($deviceRecord);
    }

    public function createHiddenDanger()
    {
        // 准备测试数据
        $question = '测试问题-3';
        $suggestion = '建议-3';
        $images = [
            '/storage/2023/03/07/20230307000001.jpg',
            '/storage/2023/03/07/20230307000002.jpg',
        ];
        $is_public = 1;

        // 调用被测方法,测试时内部userID自定义指定
        $hiddenDanger = app(\App\Services\Inspect\InspectHiddenDangerService::class)->create($question, $suggestion, $images, $is_public);
        dd($hiddenDanger->toArray());
    }

    public function getHiddenDanger()
    {
        $hiddenDanger = app(\App\Services\Inspect\InspectHiddenDangerService::class)->getHiddenDangerByUserID(103);
        dd($hiddenDanger);
    }
    public function getHiddenDangerHallData()
    {
        $hiddenDanger = app(\App\Services\Inspect\InspectHiddenDangerService::class)->getHiddenDangerHallDataByType('mine');
        dd($hiddenDanger->toArray());
    }

    public function getHiddenDangerDetail()
    {
        $hiddenDanger = app(\App\Services\Inspect\InspectHiddenDangerService::class)->getHiddenDangerByID(4, 'hall');
        dd($hiddenDanger);
    }

    public function handleHiddenDanger()
    {
        app(\App\Services\Inspect\InspectHiddenDangerService::class)->handleHiddenDanger(4, 'lock');
        dd("ok");
    }

    public function enrollConfig()
    {
//        $res = app(EnrollConfigService::class)->store([
//            'org_id' => 48,
//            'title' => '测试报名配置-2',
//            'amount' => 120,
//            'sort' => 1,
//        ]);
//        $this->info('报名配置创建成功');
//        $this->info('报名配置详情:' . $res->toJson());

        $orgId = 0;
        $list = app(EnrollConfigService::class)->getListByOrgId($orgId);
//        $detail = app(EnrollConfigService::class)->getEnrollConfigById(4);
        dd($list);
    }
}
