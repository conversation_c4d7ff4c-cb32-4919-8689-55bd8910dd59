<?php

namespace App\Console\Commands;

use App\Core\Enums\BusinessType;
use App\Models\Cms\ContentDoc;
use App\Models\Crawler\CrawlerZsxq;
use App\Services\Admin\CrawlerService;
use App\Services\Cms\ContentDocService;
use App\Services\Common\AttachmentService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class CrawlerZsxqCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawler:zsxq';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '知识星球爬虫';

    /**
     * 知识星登录用户的爬虫
     */
    protected $cookie = 'zsxq_access_token=46FCD8E5-7FFD-6A0D-5861-241262D98460_C88C62BFFAFAE037; zsxqsessionid=a6e51fda21fdf5ba75065a388758dba8; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22241512512511511%22%2C%22first_id%22%3A%2218e74905abe581-01b45bdd71fd0ce-7e433c49-2073600-18e74905abf1663%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMThlNzQ5MDVhYmU1ODEtMDFiNDViZGQ3MWZkMGNlLTdlNDMzYzQ5LTIwNzM2MDAtMThlNzQ5MDVhYmYxNjYzIiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiMjQxNTEyNTEyNTExNTExIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22241512512511511%22%7D%2C%22%24device_id%22%3A%2218e74905abe581-01b45bdd71fd0ce-7e433c49-2073600-18e74905abf1663%22%7D';

    /**
     * 是否保存到草稿，否则仅将文件存储在本地
     */
    protected $saveToDraft = false;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //因为七牛的流式文件上传内部并不流式，导致上传大文件时，读入的文件超出了 PHP 内存限制，此命令暂取消内存限制
        ini_set('memory_limit', -1);

        //默认从上一次抓取到的位置往后抓（从新往旧抓）
        $lastCreateTime = CrawlerZsxq::query()
            ->orderBy('create_time', 'asc')
            ->limit(1)
            ->value('create_time');

        //上一次的内容，并不一定抓取完整了（文件有多个），所以这里时间往回退一些，确保包括上次的内容，下面抓取的时候根据 file_id 排除
        $endTime = $lastCreateTime ? Carbon::parse($lastCreateTime)->addMicrosecond()->format('Y-m-d\TH:i:s.vO') : null;

        $total = 0;
        $saveDiskName = $this->saveToDraft ? config('heguibao.storage.priv') : config('heguibao.storage.local');

        while (true) {
            $list = $this->fetchTopics($endTime);

            if (!$list['succeeded']) {
                echo "Error: {$list['error']}";
                exit;
            }

            $topics = $list['resp_data']['topics'];

            if (!$topics) {
                $this->warn("列表返回数据为空，抓取结束。");
                break;
            }

            foreach ($topics as $item) {
                //记录 $endTime 作为翻页的标识
                $ct = Carbon::parse($item['create_time']);
                $endTime = $ct->subMicrosecond()->format('Y-m-d\TH:i:s.vO');

                sleep(2);

                if (!isset($item['talk']['files']) || !is_array($item['talk']['files'])) {
                    $this->warn("Topic {$item['topic_id']} 没有 files，跳过。");
                    continue;
                }

                foreach ($item['talk']['files'] as $file) {
                    //不是目标文件，跳过
                    if (!in_array(pathinfo($file['name'], PATHINFO_EXTENSION), ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'])) {
                        $this->warn("Topic {$item['topic_id']}, File {$file['file_id']}, Name {$file['name']} 不是需要的文件格式，跳过..");
                        continue;
                    }

                    //对可能重复的文件排除
                    $dup = CrawlerZsxq::query()
                        ->where('file_id', $file['file_id'])
                        ->where('topic_id', $item['topic_id'])
                        ->exists();

                    if ($dup) {
                        $this->info("文件重复，跳过：Topic {$item['topic_id']}, File {$file['file_id']}, Name: {$file['name']}..");
                        $this->info('==================================================');
                        continue;
                    }

                    $this->info("开始下载 Topic {$item['topic_id']}, File {$file['file_id']}, Name: {$file['name']}..");

                    //打开下载界面时会加载下载记录
                    $this->fetchDownloadRecords($file['file_id']);

                    sleep(2);

                    //获取下载记录
                    $dl = $this->getDownloadUrl($file['file_id']);

                    if (!$dl['succeeded']) {
                        $this->error("获取文件 {$file['file_id']} 下载地址失败：{$dl['error']}");
                    }

                    $name = $file['name'];
                    $downloadUrl = $dl['resp_data']['download_url'];

                    $this->info("下载文件 {$name}， URL: $downloadUrl");

                    //下载文件到临时区
                    $downloadedFile = AttachmentService::download($downloadUrl, upload: true, diskName: $saveDiskName);

                    if ($this->saveToDraft) {
                        $contentId = $this->saveToDraft($name, $downloadedFile)->id;
                    } else {
                        $contentId = 0;
                        $this->saveToLocal($name, $downloadedFile, $file['create_time']);
                    }

                    //保存记录
                    CrawlerZsxq::add($item['topic_id'], $file['file_id'], $file['name'], $item['create_time'], $contentId);

                    ++$total;

                    $this->info("保存成功（已下载 $total 个）。");
                    $this->info('==================================================');

                    //每 8 个等 30 秒左右
                    if ($total % 8 == 0) {
                        $wait = mt_rand(15, 45);
                        $this->info("等待 $wait 秒..");
                        sleep($wait);
                        $this->info('==================================================');
                    }
                }
            }
        }
    }

    private function saveToDraft($filename, $downloadedFile)
    {
        $this->info("生成草稿中..");

        //生成内容草稿
        $doc = new ContentDoc();
        $doc->format = ContentDocService::getFileType($filename);
        $doc->filesize = $downloadedFile['size'];
        $doc->filename = $filename;

        $content = CrawlerService::createDraft(23, $filename, $doc);

        $this->info("已生存草稿 {$content->id}，保存附件中..");

        $attachFile = AttachmentService::store($downloadedFile['key'], 'content', BusinessType::Content, $content->id, $filename, $downloadedFile['size'], $downloadedFile['mime']);
        $doc->filepath = $attachFile->path;
        $doc->save();

        $this->info("文件已保存至 ID： {$attachFile->id} ，文件路径： {$attachFile->path}");

        return $content;
    }

    private function saveToLocal($filename, $downloadedFile, $createTime)
    {
        $disk = Storage::disk(config('heguibao.storage.local'));

        $to = 'crawler_zsxq/'.Carbon::parse($createTime)->format('Y-m-d').'/'.$filename;

        $dir = $disk->path('');
        $from = str_starts_with($downloadedFile['local_path'], $dir) ? substr($downloadedFile['local_path'], strlen($dir)) : $downloadedFile['local_path'];

        $result = $disk->move($from, $to);

        if ($result) {
            $this->info("文件已保存至 $to");
        } else {
            $this->error("文件保存至 $to 失败");
        }
    }

    private function fetchTopics($endTime=null)
    {
        $uri = 'v2/groups/828145548542/topics?scope=all&count=20';

        if ($endTime) {
            $uri .= '&end_time='.urlencode($endTime);
        }

        return $this->get($uri)->json();
    }

    private function fetchDownloadRecords($fileId)
    {
        $uri = "v2/files/$fileId/download_records?count=48";
        return $this->get($uri)->json();
    }

    private function getDownloadUrl($fileId)
    {
        $uri = "v2/files/$fileId/download_url";
        return $this->get($uri)->json();
    }

    private function get($uri)
    {
        $url = $this->url($uri);
        return $this->http($url)->get($url);
    }

    private function post($uri, $data)
    {
        $url = $this->url($uri);
        return $this->http($url)->post($url, $data);
    }

    /**
     * 建议请求实例
     *
     * @param string $url
     * @return \Illuminate\Http\Client\PendingRequest
     */
    private function http($url)
    {
        //签名
        $timestamp = time();
        $nonce = uuid_create();

        $strToSign = $url.' '.$timestamp.' '.$nonce;
        $sign = sha1($strToSign);

        return Http::withHeaders([
            'Accept' => 'application/json, text/plain, */*',
            'Accept-Language' => 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cookie' => $this->cookie,
            'Origin' => 'https://wx.zsxq.com',
            'Referer' => 'https://wx.zsxq.com/',
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.0',
            'X-Request-Id' => $nonce,
            'X-Timestamp' => $timestamp,
            'X-Signature' => $sign,
            'X-Version' => '2.52.0'
        ]);
    }

    private function url($uri)
    {
        return 'https://api.zsxq.com/'.$uri;
    }

}
