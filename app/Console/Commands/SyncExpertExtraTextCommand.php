<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Expert;

class SyncExpertExtraTextCommand extends Command
{
    protected $signature = 'experts:sync-extra-text';
    protected $description = '同步专家工作年限、擅长领域、安全服务方向至 extra_text 字段';

    public function handle()
    {
        $this->info('开始同步 expert.extra_text 字段...');

        $experts = Expert::query()->select('id', 'work_year', 'fields', 'services')->get();

        $bar = $this->output->createProgressBar($experts->count());
        $bar->start();

        foreach ($experts as $expert) {
            // 字段转换为字符串
            $fields = is_array($expert->fields) ? implode(' ', $expert->fields) : '';
            $services = is_array($expert->services) ? implode(' ', $expert->services) : '';
            $workYear = $expert->work_year ? $expert->work_year . '年' : '';

            $extra = implode(' ', array_filter([$workYear, $fields, $services]));

            $expert->extra_text = $extra;
            $expert->save();

            $bar->advance();
        }

        $bar->finish();
        $this->info("\n同步完成，共同步 {$experts->count()} 条记录。");
    }
}
