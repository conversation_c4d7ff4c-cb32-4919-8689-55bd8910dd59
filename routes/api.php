<?php

use App\Http\Controllers\Api\AttitudeController;
use App\Http\Controllers\Api\BaiduMapConfigController;
use App\Http\Controllers\Api\BalanceController;
use App\Http\Controllers\Api\Chat\ChatController;
use App\Http\Controllers\Api\Chat\ChatMessageController;
use App\Http\Controllers\Api\Chat\ChatSessionController;
use App\Http\Controllers\Api\Cms\CategoryController;
use App\Http\Controllers\Api\Cms\ContentController;
use App\Http\Controllers\Api\Cms\ContentCourseProgressController;
use App\Http\Controllers\Api\Cms\ContentCourseSectionController;
use App\Http\Controllers\Api\Cms\ContentDocController;
use App\Http\Controllers\Api\Cms\SpecialController;
use App\Http\Controllers\Api\Ers\EnterPriseController;
use App\Http\Controllers\Api\Ers\IndustryController;
use App\Http\Controllers\Api\Ers\ProjectController;
use App\Http\Controllers\Api\Ers\ServiceOrderController;
use App\Http\Controllers\Api\Ers\ServiceOrderFormController;
use App\Http\Controllers\Api\Ers\ServiceOrderPaymentController;
use App\Http\Controllers\Api\Ers\SolutionOrderDownloadController;
use App\Http\Controllers\Api\Ers\SolutionOrderPreviewController;
use App\Http\Controllers\Api\ExpertController;
use App\Http\Controllers\Api\FavoriteController;
use App\Http\Controllers\Api\Inspect\ApprovalController;
use App\Http\Controllers\Api\Inspect\DeviceItemsController;
use App\Http\Controllers\Api\Inspect\DevicesController;
use App\Http\Controllers\Api\Inspect\HiddenDangerController;
use App\Http\Controllers\Api\Inspect\TaskController;
use App\Http\Controllers\Api\Inspect\TaskDevicesRecordController;
use App\Http\Controllers\Api\InvitationController;
use App\Http\Controllers\Api\LoginController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\Org\CaptureController;
use App\Http\Controllers\Api\Org\EnrollController;
use App\Http\Controllers\Api\Org\EnrollmentController;
use App\Http\Controllers\Api\Org\OrgController;
use App\Http\Controllers\Api\PageController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\PhoneCodeController;
use App\Http\Controllers\Api\Punish\PunishController;
use App\Http\Controllers\Api\Qa\AnswerController;
use App\Http\Controllers\Api\Qa\QuestionController;
use App\Http\Controllers\Api\Qa\TagController;
use App\Http\Controllers\Api\QiniuCallbackController;
use App\Http\Controllers\Api\Train\ClassesController;
use App\Http\Controllers\Api\Train\TrainController;
use App\Http\Controllers\Api\Train\TrainTestController;
use App\Http\Controllers\Api\UploadController;
use App\Http\Controllers\Api\User\BindController;
use App\Http\Controllers\Api\User\ContentDownloadController;
use App\Http\Controllers\Api\User\CreditLogController;
use App\Http\Controllers\Api\User\MpAccountController;
use App\Http\Controllers\Api\User\OwnContentController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\VisitorController;
use App\Http\Controllers\Api\WechatController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::get('/visitor', [VisitorController::class, 'index']);
Route::post('/phone-code', [PhoneCodeController::class, 'store']);

Route::post('/login/phone-code', [LoginController::class, 'phoneCode']);
Route::post('/login/wechat-mini-app', [LoginController::class, 'wechatMiniApp']);
Route::post('/login/wechat-mini-app/openid', [LoginController::class, 'wechatMiniAppOpenId']);
Route::post('/login/bind', [LoginController::class, 'bind']);
Route::post('/login/id-card', [LoginController::class, 'idCard']);
Route::get('/login/wechat-qrcode', [LoginController::class, 'wechatQrcode']);
Route::get('/login/wechat-qrcode/{scene}', [LoginController::class, 'wechatQrcodeCheck']);
Route::post('/login/wechat-qrcode/{scene}', [LoginController::class, 'wechatQrcodeAuthorize']);

Route::post('/payments/notify/{platform}', [PaymentController::class, 'notify'])->whereIn('platform', ['wechat', 'alipay']);
Route::post('/payments/notify-service/{platform}/{encode_mch_id}', [PaymentController::class, 'notifyService'])->whereIn('platform', ['wechat', 'alipay']);

Route::post('/upload', [UploadController::class, 'store'])->name('upload');
Route::any('/upload/info', [UploadController::class, 'info'])->name('upload_info');
Route::post('/qiniu-callback/transcode', [QiniuCallbackController::class, 'transcode'])->name('qiniu_transcode_callback');

Route::post('/invitations/share-ref', [InvitationController::class, 'shareReward']);
Route::get('/wechat/share/qr_code', [WechatController::class, 'shareQrCode']);

Route::get('/orgs/{sid}/courses', [\App\Http\Controllers\Api\Org\CourseController::class, 'index']);
Route::get('/orgs/{sid}/topics', [\App\Http\Controllers\Api\Org\TopicController::class, 'index']);
Route::get('/orgs/{sid}/course-packs', [\App\Http\Controllers\Api\Org\CoursePackController::class, 'index']);

Route::prefix('qa')->group(function () {
    Route::get('questions', [QuestionController::class, 'questionsIndex']);
    Route::get('questions/{question_id}', [QuestionController::class, 'show'])->whereAlphaNumeric('question_id');
    Route::get('questions/{question_id}/answers', [AnswerController::class, 'index'])->whereAlphaNumeric('question_id');
    Route::get('search', [QuestionController::class, 'search']);
    Route::get('tags', [TagController::class, 'index']);
    Route::get('tags/{id}', [TagController::class, 'show'])->whereNumber('id');
});

Route::prefix('ers')->group(function () {
    Route::get('projects', [ProjectController::class, 'index'])->whereNumber('id');
    Route::get('projects/{id}', [ProjectController::class, 'show'])->whereNumber('id');
});

Route::get('/expert', [ExpertController::class, 'index']);
Route::get('/expert/{id}', [ExpertController::class, 'show'])->whereNumber('id');
Route::get('/expert/form-config', [ExpertController::class, 'formConfig']);

Route::get('/orgs/{sid}', [OrgController::class, 'show']);
Route::get('/orgs/{sid}/enrollment-form', [EnrollmentController::class, 'structure']);
Route::post('/orgs/{sid}/enrollments', [EnrollmentController::class, 'store']);
Route::put('/orgs/{sid}/enrollments', [EnrollmentController::class, 'update']);
Route::get('/orgs/{sid}/upload-config', [EnrollmentController::class, 'uploadConfig']);
Route::post('/orgs/{sid}/id-card-info', [EnrollmentController::class, 'getCardInfo']);
Route::post('/orgs/enrollments/to-id-photo', [EnrollmentController::class, 'toIDPhoto']);

Route::post('/learn-captures/{scene}', [CaptureController::class, 'store']);
Route::get('/learn-captures/{scene}/org', [CaptureController::class, 'getOrg']);

Route::get('/enrollments/{sid}/hour-cert', [EnrollmentController::class, 'hourCert']);
Route::get('/enrollments/{sid}/study-record', [EnrollmentController::class, 'studyRecord']);

// 获取审批详情-无需登录
Route::get('/inspect/approval-detail/{taskDeviceRecordID}', [ApprovalController::class, 'approvalDetail']);

Route::middleware(['auth:sanctum', 'check.active.token'])->namespace('App\Http\Controllers\Api')->group(function () {
    Route::get('tips', [\App\Http\Controllers\Api\TipController::class, 'view']);
    Route::post('tips', [\App\Http\Controllers\Api\TipController::class, 'store']);
    Route::delete('tips', [\App\Http\Controllers\Api\TipController::class, 'delete']);

    Route::get('/users/me', [UserController::class, 'me']);

    Route::get('/map-config', [BaiduMapConfigController::class, 'config']);

    Route::post('/payments', [PaymentController::class, 'store']);
    Route::get('/payments/{outTradeNo}', [PaymentController::class, 'show'])->where(['outTradeNo' => '\d{21}(\-\d{1,2})?']);

    Route::post('/balances/payment', [BalanceController::class, 'payment']);
    Route::get('/balances', [BalanceController::class, 'index']);

    // 文档下载
    Route::get('/cms/contents/{content_id}/download', [ContentDocController::class, "download"])->whereAlphaNumeric('content_id');
    // 用户下载列表
    Route::get('/cms/download-records', [ContentDownloadController::class, "index"]);
    // 更新视频进度
    Route::put('/cms/content-sections/{section_id}/progress', [ContentCourseSectionController::class, "update"])->whereAlphaNumeric('section_id');
    // 积分购买内容
    Route::post('/cms/contents/{content_id}/credit', [ContentController::class, "buyCredit"])->whereAlphaNumeric('content_id');
    // 内容订单
    Route::post('/cms/contents/{content_id}/buy-order', [ContentController::class, "createOrder"])->whereAlphaNumeric('content_id');
    // 购买专题
    Route::post('/cms/specials/{special_id}/order', [SpecialController::class, "buy"])->whereAlphaNumeric('special_id');
    // 已购课程
    Route::get('/cms/own-contents/{cate_id}', [OwnContentController::class, "index"])->where('cate_id', '[a-zA-Z0-9_-]+');
    // 订单列表
    Route::get('/orders', [OrderController::class, "index"]);
    Route::get('/orders/{order_no}', [OrderController::class, "show"]);
    // 订单列表
    Route::delete('/orders/{order_no}', [OrderController::class, "del"]);
    // 创建充值订单
    Route::post('/credits/order', [CreditLogController::class, "createOrder"]);
    // 邀请记录
    Route::get('/invitations', [InvitationController::class, "index"]);

    Route::prefix("users")->group(function () {
        Route::put('avatar', [UserController::class, 'updateAvatar']);
        Route::put('nickname', [UserController::class, 'updateNickname']);
        Route::get('open_id', [UserController::class, "getOpenId"]);
    });

    Route::get('/credit-records', [CreditLogController::class, 'index']);
    Route::get('/binds', [BindController::class, 'index']);

    Route::prefix("training")->group(function () {
        Route::get('topics', [TrainController::class, "topicLibrariesIndex"]);
        Route::get('chapters/{topic_id}', [TrainController::class, "chapterList"]);
        Route::put('topics/{topic_id}/choose', [TrainController::class, "chooseTopic"]);
        Route::get('topics/{topic_id}', [TrainController::class, "topicShow"]);
        Route::get('topics/{topic_id}/search', [TrainController::class, "searchIndex"]);
        Route::post('topics/{topic_id}/order', [TrainController::class, "orderStore"]);
        Route::get('tests/{topic_id}/exam/record', [TrainTestController::class, "examRecord"]);
        Route::get('tests/{org_sid}/record', [TrainTestController::class, "orgTests"]);

        Route::get('tests/current/{topic_id}', [TrainTestController::class, "test"]);
        Route::post('{topic_id}/tests', [TrainTestController::class, "createTest"]);
        Route::put('tests/{test_id}', [TrainTestController::class, "testPut"]);
        Route::post('tests/{test_id}/subjects', [TrainTestController::class, "answerQuestion"]);
        Route::post('tests/{test_id}/submit', [TrainTestController::class, "finishTest"]);
        Route::get('tests/{topic_id}/subjects', [TrainTestController::class, "subjectIndex"]);
        Route::get('tests/{test_id}', [TrainTestController::class, "subjectTextShow"]);
        Route::get('tests/{test_id}/results', [TrainTestController::class, "examResults"]);
        Route::delete('tests/{test_id}', [TrainTestController::class, "delTest"]);

        Route::delete('wrong-subjects/{test_id}/{record_id}', [TrainTestController::class, "wrongErrTestDelete"]);
    });

    Route::get('favorites', [FavoriteController::class, 'index']);
    Route::post('favorites/{business_type}/{business_id}', [FavoriteController::class, 'store']);
    Route::delete('favorites/{business_type}/{business_id}', [FavoriteController::class, 'destroy']);

    Route::post('attitude/{business_type}/{business_id}', [AttitudeController::class, 'store']);
    Route::delete('attitude/{business_type}/{business_id}', [AttitudeController::class, 'destroy']);

    Route::prefix('qa')->group(function () {
        Route::post('questions', [QuestionController::class, 'store']);
        Route::get('my-questions', [QuestionController::class, 'myQuestionsIndex']);
        Route::get('questions/upload-form', [QuestionController::class, 'uploadForm']);
        Route::delete('questions/{sid}', [QuestionController::class, 'delete'])->whereAlphaNumeric('sid');

        Route::get('my-answers', [AnswerController::class, 'myAnswers']);
        Route::post('questions/{question_id}/answers', [AnswerController::class, 'store']);
        Route::delete('answers/{sid}', [AnswerController::class, 'delete']);
    });

    Route::prefix('ers')->group(function () {
        Route::get('page/hall', [\App\Http\Controllers\Api\Ers\PageController::class, 'hall']);
        Route::get('page/order', [\App\Http\Controllers\Api\Ers\PageController::class, 'order']);

        Route::get('enterprises', [EnterPriseController::class, 'index']);
        Route::get('industries', [IndustryController::class, 'index']);

        Route::get('form/preview', [ServiceOrderFormController::class, 'preview']);
        Route::get('form/inputs', [ServiceOrderFormController::class, 'inputs']);

        Route::post('orders', [ServiceOrderController::class, 'store']);
        Route::get('orders/{sid}', [ServiceOrderController::class, 'show'])->whereAlphaNumeric('sid');

        Route::get('orders/{sid}/form/{flow_step_id}', [ServiceOrderFormController::class, 'show'])->whereAlphaNumeric('sid')->whereNumber('flow_step_id');
        Route::post('orders/{sid}/form/{flow_step_id}', [ServiceOrderFormController::class, 'store'])->whereAlphaNumeric('sid')->whereNumber('flow_step_id');
        Route::get('orders/form/upload-config', [ServiceOrderFormController::class, 'uploadConfig']);
        Route::get('orders/{sid}/payment/{flow_step_id}', [ServiceOrderPaymentController::class, 'show'])->whereAlphaNumeric('sid')->whereNumber('flow_step_id');
        Route::post('orders/{sid}/payment/{flow_step_id}', [ServiceOrderPaymentController::class, 'store'])->whereAlphaNumeric('sid')->whereNumber('flow_step_id');

        Route::get('orders/{sid}/solution-preview/{flow_step_id}', [SolutionOrderPreviewController::class, 'show'])->whereAlphaNumeric('sid')->whereNumber('flow_step_id');
        Route::post('orders/{sid}/solution-preview/{flow_step_id}', [SolutionOrderPreviewController::class, 'store'])->whereAlphaNumeric('sid')->whereNumber('flow_step_id');

        Route::get('orders/{sid}/solution-download/{flow_step_id}', [SolutionOrderDownloadController::class, 'show'])->whereAlphaNumeric('sid')->whereNumber('flow_step_id');
        Route::post('orders/{sid}/solution-download/{flow_step_id}', [SolutionOrderDownloadController::class, 'store'])->whereAlphaNumeric('sid')->whereNumber('flow_step_id');

    });

    Route::prefix('chat')->group(function () {
        Route::post('/chat', [ChatController::class, 'chat']);
        Route::post('/stream', [ChatController::class, 'streamCreate']);
        Route::post('/streamCallback', [ChatController::class, 'streamCallback']);
        Route::get('/sessions', [ChatSessionController::class, 'index']);
        Route::put('/sessions/{id}', [ChatSessionController::class, 'update'])->whereNumber('id');
        Route::post('/sessions/remove', [ChatSessionController::class, 'destroy']);
        Route::post('/sessions/recommend', [ChatSessionController::class, 'recommend']);

        Route::get('/messages', [ChatMessageController::class, 'index']);
        Route::post('/messages', [ChatMessageController::class, 'store']);
        Route::put('/messages/{id}', [ChatMessageController::class, 'update'])->whereNumber('id');
        Route::post('/messages/like', [ChatMessageController::class, 'like']);
    });

    Route::prefix('mp')->group(function () {
        Route::get('/account', [MpAccountController::class, 'show']);
        Route::post('/account', [MpAccountController::class, 'store']);
    });

    Route::prefix('wechat')->group(function () {
        Route::get('/notice-templates', [WechatController::class, 'template']);
    });

    Route::prefix('punish')->group(function () {
        Route::get('/topic', [PunishController::class, 'topic']);
        Route::get('/subject/test/{topicId}', [PunishController::class, 'subjectTestList']);
        Route::post('/subject/test', [PunishController::class, 'submitAnswer']);
        Route::get('/subject/test/result/{testId}', [PunishController::class, 'showTest']);
    });

    Route::post('/expert', [ExpertController::class, 'store']);
    Route::get('/expert/my-show', [ExpertController::class, 'myShow']);
    Route::put('/expert/{id}', [ExpertController::class, 'update'])->whereNumber('id');
    Route::get('/expert/upload-config', [ExpertController::class, 'uploadConfig']);


    Route::get('/pages/user/profile', [PageController::class, 'userProfile']);

    Route::get('/logout', [LoginController::class, 'logout']);

    Route::get('/orgs/{sid}/my-classes', [ClassesController::class, 'myClasses']);
    Route::get('/orgs', [OrgController::class, 'index']);
    Route::get('/orgs/{sid}/enrollments', [EnrollmentController::class, 'index']);
    Route::get('/orgs/{sid}/my-enrollment', [EnrollmentController::class, 'myEnrollment']);
    Route::get('/orgs/{sid}/my-train', [EnrollmentController::class, 'myTrain']);
    Route::get('/orgs/{sid}/my-study', [EnrollmentController::class, 'myStudy']);
    Route::get('/orgs/{sid}/train-tests', [\App\Http\Controllers\Api\Org\TrainTestController::class, 'index']);

    // 机构报名首页
    Route::get('/orgs/{sid}/enroll', [EnrollController::class, 'index']);
    Route::post('/orgs/enroll/{id}/buy-order', [EnrollController::class, 'createOrder']);
    Route::put('/orgs/enroll/{id}/apply-refund', [EnrollController::class, 'applyRefund']);
    Route::put('/orgs/enroll/{id}/cancel-refund', [EnrollController::class, 'cancelRefund']);
    Route::get('/orgs/enroll/{id}/refund', [EnrollController::class, 'refundDetail']);
    Route::post('/orgs/enroll/{id}/invoice', [EnrollController::class, 'invoice']);
    Route::get('/orgs/enroll/{orgId}/status', [EnrollController::class, 'enrollStatus']);

    Route::get('/enrollments/{sid}/hour-cert-image', [EnrollmentController::class, 'hourCertImage']);

    Route::get('/learn-captures/{scene}/wechat-qrcode', [CaptureController::class, 'wechatQrcode']);
    Route::get('/learn-captures/{scene}', [CaptureController::class, 'check']);

    // 巡检模块
    Route::prefix("inspect")->group(function () {
        Route::get('task-devices-records', [TaskDevicesRecordController::class, 'index']);
        Route::post('task-devices-records', [TaskDevicesRecordController::class, 'store']);
        Route::put('task-devices-records/{id}', [TaskDevicesRecordController::class, 'update'])->whereNumber('id');
        Route::delete('task-devices-records/{id}', [TaskDevicesRecordController::class, 'destroy'])->whereNumber('id');

        Route::get('devices', [DevicesController::class, 'index']);
        Route::post('devices', [DevicesController::class, 'store']);
        Route::get('devices/ai', [DevicesController::class, 'aiRecognition']);
        Route::post('devices/ai', [DevicesController::class, 'aiCreate']);
        Route::put('devices/{id}', [DevicesController::class, 'update'])->whereNumber('id');
        Route::delete('devices/{id}', [DevicesController::class, 'destroy'])->whereNumber('id');
        Route::get('devices/{id}', [DevicesController::class, "show"])->whereNumber('id');
        Route::get('/devices/upload-config', [DevicesController::class, 'uploadConfig']);

        Route::get('device-items', [DeviceItemsController::class, 'index']);
        Route::post('device-items', [DeviceItemsController::class, 'store']);

        // 创建巡检任务
        Route::post('task', [TaskController::class, 'create']);
        // 获取用户巡检任务列表
        Route::get('task', [TaskController::class, 'myTask']);
        // 获取巡检任务记录表
        Route::get('task-devices-record-table/{taskID}', [TaskController::class, 'taskDevicesRecordTable']);
        // 巡检任务关联新设备
        Route::post('assoc-device/{taskID}', [TaskController::class, 'assocDevice']);
        // 获取巡检任务审批列表
        Route::get('task-devices-record-approval/{taskID}', [TaskController::class, 'taskDevicesRecordApproval']);
        // 获取任务设备巡检记录详情
        Route::get('task-device-record/{taskDeviceRecordID}', [TaskController::class, 'taskDeviceRecord']);
        // 填写巡检任务设备项记录表
        Route::put('fill-in-task-device-record-table/{taskDeviceRecordID}', [TaskController::class, 'fillInTaskDeviceRecordTable']);
        // 获取审批列表
        Route::get('approval-list', [ApprovalController::class, 'approvalList']);

        // 审批
        Route::put('approval/{taskDeviceRecordID}', [ApprovalController::class, 'approve']);

        // 创建随手拍
        Route::post('hidden-danger', [HiddenDangerController::class, 'create']);
        // 我的隐患列表
        Route::get('hidden-danger', [HiddenDangerController::class, 'getOwnerHiddenDanger']);
        // 隐患大厅列表
        Route::get('hidden-danger-hall', [HiddenDangerController::class, 'getHiddenDangerHall']);
        // 获取隐患详情
        Route::get('hidden-danger/{id}', [HiddenDangerController::class, 'getHiddenDangerDetail']);
        // 审批随手拍
        Route::put('hidden-danger/{id}', [HiddenDangerController::class, 'approveHiddenDanger']);
        // 添加线索
        Route::post('hidden-danger/add-clue/{id}', [HiddenDangerController::class, 'addClue']);
        // 处理随手拍,顶 踩 设为私密/公开 删除
        Route::put('handle-hidden-danger/{id}', [HiddenDangerController::class, 'handleHiddenDanger']);

    });
});


Route::prefix("cms")->group(function () {
    // 分类相关接口
    Route::get('categories', [CategoryController::class, "index"]);
    Route::get('categories/sup', [CategoryController::class, "supCategory"]);

    // 内容相关接口
    Route::get('{cate_id}/new-contents', [ContentController::class, "recent"])->whereAlphaNumeric('cate_id');
    Route::get('{cate_id}/hot-contents', [ContentController::class, "hot"])->whereAlphaNumeric('cate_id');
    Route::get('{cate_id}/recommend-contents', [ContentController::class, "recommend"])->where('cate_id', '[a-zA-Z0-9_-]+');
    Route::get('{cate_id}/contents', [ContentController::class, "index"])->whereAlphaNumeric('cate_id');

    Route::get('contents/{content_id}', [ContentController::class, "show"])->whereAlphaNumeric('content_id');
    Route::get('contents/{content_id}/sections/{section_id}', [ContentCourseSectionController::class, "show"])->whereAlphaNumeric(['content_id', 'section_id']);
    Route::get('contents/{content_id}/progress/{section_id}', [ContentCourseProgressController::class, "show"])->whereAlphaNumeric(['content_id', 'section_id']);

    Route::get('related-contents/{content_id}', [ContentController::class, "relation"])->whereAlphaNumeric('content_id');

    Route::get('contents/{content_id}/docs/{doc_id}/download', [ContentCourseSectionController::class, "docDownload"])->whereAlphaNumeric(['content_id', 'doc_id']);
    Route::get('specials/{special_id}', [SpecialController::class, "show"])->whereAlphaNumeric('special_id');
    Route::get('specials', [SpecialController::class, "index"]);
});

Route::prefix("v2")->group(function () {
    Route::prefix("cms")->group(function () {
        Route::get('contents/{content_id}/sections/{section_id}', [ContentCourseSectionController::class, "v2Show"])->whereAlphaNumeric(['content_id', 'section_id']);
    });
});

Route::prefix("pages")->group(function () {
    Route::get('home', [PageController::class, 'home']);
    Route::get('me', [PageController::class, 'me']);
    Route::get('qa', [PageController::class, 'qa']);
    Route::get('train', [PageController::class, 'train']);
    Route::get('materials', [PageController::class, 'material']);
    Route::get('recharge', [PageController::class, 'recharge']);
    Route::get('topic/buy', [PageController::class, 'topicBuy']);
    Route::get('list-type', [PageController::class, 'listType']);
});

