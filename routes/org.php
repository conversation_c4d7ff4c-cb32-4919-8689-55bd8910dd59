<?php

use App\Http\Controllers\Org\Admin\AdminController;
use App\Http\Controllers\Org\Admin\RoleController;
use App\Http\Controllers\Org\AuthController;
use App\Http\Controllers\Org\BalanceRecordController;
use App\Http\Controllers\Org\ClassesController;
use App\Http\Controllers\Org\CourseController;
use App\Http\Controllers\Org\CoursePackController;
use App\Http\Controllers\Org\CourseSubController;
use App\Http\Controllers\Org\Enroll\EnrollConfigController;
use App\Http\Controllers\Org\Enroll\EnrollController;
use App\Http\Controllers\Org\Enrollment\EnrollmentController;
use App\Http\Controllers\Org\Enrollment\EnrollmentFormController;
use App\Http\Controllers\Org\ExportController;
use App\Http\Controllers\Org\OrderController;
use App\Http\Controllers\Org\OrgController;
use App\Http\Controllers\Org\Stat\DailyOrgController;
use App\Http\Controllers\Org\StudentController;
use App\Http\Controllers\Org\System\UploadController;
use App\Http\Controllers\Org\TemplateController;
use App\Http\Controllers\Org\TopicController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'auth'], function () {
    Route::post('login', [AuthController::class, 'login'])->name('org.login');
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('me', [AuthController::class, 'me']);
    Route::post('menus', [AuthController::class, 'menus']);
});

Route::middleware('auth.org')->group(function () {
    Route::get('admins', [AdminController::class, 'index']);
    Route::get('admins/{id}', [AdminController::class, 'show'])->whereNumber('id');
    Route::post('admins', [AdminController::class,  'store']);
    Route::put('admins/{id}', [AdminController::class, 'update'])->whereNumber('id');
    Route::put('admins/{id}/updatePassword', [AdminController::class, 'updatePassword'])->whereNumber('id');

    Route::get('roles', [RoleController::class, 'index']);
    Route::get('roles/search', [RoleController::class, 'search']);
    Route::post('roles', [RoleController::class, 'store']);
    Route::put('roles/{id}', [RoleController::class, 'update'])->whereNumber('id');
    Route::delete('roles/{id}', [RoleController::class, 'destroy'])->whereNumber('id');

    Route::get('orgs', [OrgController::class, 'show']);
    Route::put('orgs', [OrgController::class, 'update']);

    Route::get('balanceRecords', [BalanceRecordController::class, 'index']);

    Route::get('orders', [OrderController::class, 'index']);

    Route::get('topics', [TopicController::class, 'index']);
    Route::get('topics/getSubjectTypeCount', [TopicController::class, 'getSubjectTypeCount']);
    Route::put('topics/{id}', [TopicController::class, 'update'])->whereNumber('id');
    Route::put('topics/batchUpdate', [TopicController::class, 'batchUpdate']);

    Route::get('courses', [CourseController::class, 'index']);
    Route::put('courses/batchUpdate', [CourseController::class, 'batchUpdate']);
    Route::get('courses/{id}/getChapterSections', [CourseController::class, 'getChapterSections'])->whereNumber('id');
    Route::post('courses/{id}/operate', [CourseController::class, 'operate'])->whereNumber('id');

    Route::get('coursePacks', [CoursePackController::class, 'index']);
    Route::put('coursePacks/batchUpdate', [CoursePackController::class, 'batchUpdate']);

    Route::get('classes', [ClassesController::class, 'index']);
    Route::get('classes/getOptions', [ClassesController::class, 'getOptions']);
    Route::get('classes/{id}', [ClassesController::class, 'show'])->whereNumber('id');
    Route::post('classes', [ClassesController::class, 'store']);
    Route::post('classes/finished', [ClassesController::class, 'finished']);
    Route::put('classes/{id}', [ClassesController::class, 'update'])->whereNumber('id');
    Route::delete('classes/{id}', [ClassesController::class, 'destroy'])->whereNumber('id');

    Route::get('students', [StudentController::class, 'index']);
    Route::post('students', [StudentController::class, 'store']);
    Route::put('students/{id}', [StudentController::class, 'update'])->whereNumber('id');
    Route::post('students/batchAssign', [StudentController::class, 'batchAssign']);
    Route::post('students/importIdCardPhoto', [StudentController::class, 'importIdCardPhoto']);
    Route::delete('students/{id}', [StudentController::class, 'destroy'])->whereNumber('id');

    // 机构报名课程配置
    Route::get('enroll-config', [EnrollConfigController::class, 'index']);
    Route::post('enroll-config', [EnrollConfigController::class, 'store']);
    Route::put('enroll-config/{id}', [EnrollConfigController::class, 'update'])->whereNumber('id');
    Route::delete('enroll-config/{id}', [EnrollConfigController::class, 'destroy'])->whereNumber('id');
    // 机构报名记录
    Route::get('enroll', [EnrollController::class, 'index']);
    Route::get('enroll/{id}', [EnrollController::class, 'detail']);
    Route::put('enroll/{id}/audit', [EnrollController::class, 'audit']);

    Route::get('enrollments', [EnrollmentController::class, 'index']);
    Route::get('enrollments/getOptions', [EnrollmentController::class, 'getOptions']);
    Route::put('enrollments/examRetake/{id}', [EnrollmentController::class, 'examRetake'])->whereNumber('id');
    Route::delete('enrollments/{id}', [EnrollmentController::class, 'destroy'])->whereNumber('id');
    Route::post('enrollments/getIdCardInfo', [EnrollmentController::class, 'getIdCardInfo']);
    Route::post('enrollments/batchAssign', [EnrollmentController::class, 'batchAssign']);
    Route::post('enrollments/batchRemove', [EnrollmentController::class, 'batchRemove']);
    Route::post('enrollments/importEnrollment', [EnrollmentController::class, 'importEnrollment']);
    Route::post('enrollments/importEnrollmentPhoto', [EnrollmentController::class, 'importEnrollmentPhoto']);
    Route::post('enrollments/getTestPaper', [EnrollmentController::class, 'getTestPaper']);
    Route::post('enrollments/hourCertImage', [EnrollmentController::class, 'hourCertImage']);

    Route::get('enrollmentForms', [EnrollmentFormController::class, 'show']);
    Route::put('enrollmentForms', [EnrollmentFormController::class, 'update']);

    Route::get('templates', [TemplateController::class, 'index']);
    Route::get('templates/getFields', [TemplateController::class, 'getFields']);
    Route::post('templates', [TemplateController::class, 'store']);
    Route::put('templates/{id}', [TemplateController::class, 'update'])->whereNumber('id');
    Route::put('templates/default/{id}', [TemplateController::class, 'default'])->whereNumber('id');
    Route::delete('templates/{id}', [TemplateController::class, 'destroy'])->whereNumber('id');

    Route::prefix('system')->group(function () {
        Route::post('upload/config', [UploadController::class, 'config']);
    });

    // 导出相关路由
    Route::group(['prefix' => 'exports'], function () {
        Route::get('/', [ExportController::class, 'index']);
        Route::post('/', [ExportController::class, 'store']);
        Route::delete('/{id}', [ExportController::class, 'destroy'])->whereNumber('id');
        Route::get('/{id}/download', [ExportController::class, 'download'])->whereNumber('id');
    });

    Route::prefix('stat')->group(function () {
        Route::get('org/getSubtotalData', [DailyOrgController::class, 'getSubtotalData']);
        Route::get('org/getDateList', [DailyOrgController::class, 'getDateList']);
    });
});


