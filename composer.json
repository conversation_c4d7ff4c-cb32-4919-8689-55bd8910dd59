{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "ext-bcmath": "*", "ext-curl": "*", "ext-fileinfo": "*", "ext-zip": "*", "babenkoivan/elastic-migrations": "^2.0", "elasticsearch/elasticsearch": "7.17.0", "endroid/qr-code": "^5.1", "fileeye/mimemap": "*", "guzzlehttp/guzzle": "^7.2", "hidehalo/nanoid-php": "^1.1", "huaweicloud/huaweicloud-sdk-php": "^3.1", "kosinix/grafika": "^2.0", "laravel-lang/locales": "^2.6", "laravel/framework": "^10.10", "laravel/sanctum": "^3.3", "laravel/scout": "9.8.0", "laravel/tinker": "^2.8", "maatwebsite/excel": "^3.1", "overtrue/easy-sms": "^2.6", "overtrue/laravel-filesystem-qiniu": "^2.2", "phpoffice/phpword": "^1.3", "pp/laravel-scout-elastic": "dev-master", "qiniu/php-sdk": "^7.12", "reliese/laravel": "^1.3", "sqids/sqids": "^0.4.1", "tencentcloud/sms": "^3.0", "tymon/jwt-auth": "^2.0", "w7corp/easywechat": "^6.15", "yansongda/pay": "~3.6.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.10", "barryvdh/laravel-ide-helper": "^3.0", "fakerphp/faker": "^1.9.1", "kitloong/laravel-migrations-generator": "^7.0", "laravel-lang/common": "^6.2", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "repositories": [{"type": "git", "url": "***********************:yangpp/laravel-scout-elastic.git"}], "minimum-stability": "stable", "prefer-stable": true}