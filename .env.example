APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

PC_URL=https://hgb-pc.test.pp.cc

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
CACHE_PREFIX=cache
FILESYSTEM_DISK=qiniu-pub
FILESYSTEM_DISK_PRIV=qiniu-priv
QUEUE_CONNECTION=redis
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=************
REDIS_PASSWORD=93SQSqn20Ybf
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

HASH_ID_SALT=
JWT_SECRET=

# elasticsearch配置
ELASTICSEARCH_HOST=***************:9200
ELASTICSEARCH_ENABLE=true

# scout配置
SCOUT_DRIVER=elasticsearch
SCOUT_PREFIX='heguibao_'

HUAWEI_SMS_SENDER=
HUAWEI_SMS_APP_KEY=
HUAWEI_SMS_KEY_SECRET=
HUAWEI_SMS_SIGN="合规保"

QINIU_ACCESS_KEY=eI8hTa7IbDmMAMxJMebBBjRPRrq90rVk2yO12JoI
QINIU_SECRET_KEY=Lw5hSq7_KA3aMFVqY6p8WmeswRDzmpd8QQABhEtB
QINIU_PUB_BUCKET=heguibao-test-pub
QINIU_PUB_DOMAIN=http://img-test.shiwusuo100.com
QINIU_PRIV_BUCKET=heguibao-test-priv
QINIU_PRIV_DOMAIN=http://vod-test.shiwusuo100.com

WECHAT_MCH_ID=1670310692
WECHAT_MCH_SECRET_KEY=egV322C8s6JH9eqwzG4jLNkAeCg9pjgS
WECHAT_MCH_SECRET_CERT=wechat_pay_cert/apiclient_key.pem
WECHAT_MCH_PUBLIC_CERT_PATH=wechat_pay_cert/apiclient_cert.pem
WECHAT_MP_APP_ID=wx06b2b154c3d298d3

# 微信支付-服务商模式配置
WECHAT_SERVICE_MCH_ID=1723035535
WECHAT_SERVICE_MCH_SECRET_KEY=mF2vtmkwbhhb27mwnYbdsZmpr5nmePbB
WECHAT_SERVICE_MCH_SECRET_CERT=wechat_server_pay_cert/apiclient_key.pem
WECHAT_SERVICE_MCH_PUBLIC_CERT_PATH=wechat_server_pay_cert/apiclient_cert.pem
WECHAT_SERVICE_MINI_APP_ID=wx06b2b154c3d298d3
# 题库金额
TOPIC_MONTH_AMOUNT=0.01
TOPIC_YEAR_AMOUNT=0.02

# 百度ID_CARD_ORC配置
BAIDU_ORC_API_KEY=srmsjFeZEuc76HaoEGi78ZAU
BAIDU_ORC_API_SECRET=yU4W4hJHw3HcXXdAGv619mJrbTGNzAPB

# 美图 AIGCP API 配置
MEITU_API_ID=357643
MEITU_API_KEY=65c187bc3d3e4173b44f4cc86331fbdb
MEITU_SECRET_ID=0cba9f7a058848eaab200fc9c8e98835

# 通义千问 配置
TONG_YI_API_KEY='sk-a8eb511a37ff4a619d6aeec06c455136'

# 百度地图key
BAIDU_MAP_KEY=lZdbbY8TpU10kRfwRA4GG5V8Ht2vZPq3
