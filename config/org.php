<?php

use App\Models\Org\Export;

return [
    /**
     * 机构未设置模板时使用的平台默认模板
     * 所有文件都在 public_path 下
     */
    'export' => [
        'default_templates' => [
            Export::TYPE_HOUR_RECORD => 'assets/org_templates/学习记录模板.docx',
            Export::TYPE_HOUR_CERT => 'assets/org_templates/学时证明模板.docx',
            Export::TYPE_STUDENT_ARCHIVE => 'assets/org_templates/班级档案模板.docx',
            Export::TYPE_ORG_ENROLLMENT_FORM => 'assets/org_templates/报名表模板.docx',
        ]
    ],
    'capture' => [
        'interval' => config('app.env') == 'production' ? 5 : 1,
    ]
];
