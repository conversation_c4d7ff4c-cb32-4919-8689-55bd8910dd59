<?php

/**
 * EasySMS 配置
 */

use App\Libs\Sms\SmsTemplate;

return [
    // HTTP 请求的超时时间（秒）
    'timeout' => 5.0,

    // 默认发送配置
    'default' => [
        // 网关调用策略，默认：顺序调用
        'strategy' => \Overtrue\EasySms\Strategies\OrderStrategy::class,

        // 默认可用的发送网关
        'gateways' => [
            'qcloud',
        ],
    ],
    // 可用的网关配置
    'gateways' => [
        'errorlog' => [
            'file' => '/tmp/easy-sms.log',
        ],
        'huawei-cloud' => [
            'sender' => env('HUAWEI_SMS_SENDER'),
            'sender_notice' => env('HUAWEI_SMS_SENDER_NOTICE'),
            'app_key' => env('HUAWEI_SMS_APP_KEY'),
            'key_secret' => env('HUAWEI_SMS_KEY_SECRET'),
            'sign' => env('HUAWEI_SMS_SIGN'),
            'template_ids' => [
                SmsTemplate::LOGIN => 'aa06baae6da744a79a786d62bea234bb',
                SmsTemplate::BIND => 'aa06baae6da744a79a786d62bea234bb',
                SmsTemplate::PHONE_VERIFY => 'aa06baae6da744a79a786d62bea234bb',
                SmsTemplate::CHANGE_PHONE => 'aa06baae6da744a79a786d62bea234bb',
                SmsTemplate::USER_DESTROY => 'aa06baae6da744a79a786d62bea234bb',
                SmsTemplate::RESET_PASSWORD => 'aa06baae6da744a79a786d62bea234bb',
                SmsTemplate::SERVICE_ORDER => 'f0c0f30be86547ce876276915f99988f',
                SmsTemplate::SERVICE_ORDER_FLOW => '54145625d21e4b0cb89f58f1da63bd5b'
            ],
            'notice_template_ids' => [
                SmsTemplate::SERVICE_ORDER => 'f0c0f30be86547ce876276915f99988f',
                SmsTemplate::SERVICE_ORDER_FLOW => '54145625d21e4b0cb89f58f1da63bd5b'
            ]
        ],
        'qcloud' => [
            'sdk_app_id' => env('TENCENT_SMS_SDK_APP_ID'),
            'secret_id' => env('TENCENT_SMS_SECRET_ID'),
            'secret_key' => env('TENCENT_SMS_SECRET_KEY'),
            'sign_name' => env('TENCENT_SMS_SIGN_NAME'),
            'template_ids' => [
                SmsTemplate::LOGIN => '2414828',
                SmsTemplate::BIND => '2414828',
                SmsTemplate::PHONE_VERIFY => '2414828',
                SmsTemplate::CHANGE_PHONE => '2414828',
                SmsTemplate::USER_DESTROY => '2414828',
                SmsTemplate::RESET_PASSWORD => '2414828',
                SmsTemplate::SERVICE_ORDER => '2411854',
                SmsTemplate::SERVICE_ORDER_FLOW => '2411863'
            ],
            'notice_template_ids' => [
                SmsTemplate::SERVICE_ORDER => '2411854',
                SmsTemplate::SERVICE_ORDER_FLOW => '2411863'
            ],
            'region' => 'ap-guangzhou', // 根据实际情况设置
        ]
    ],
];

