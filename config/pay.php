<?php

use <PERSON><PERSON>gda\Pay\Pay;

return [
    'alipay' => [
        'default' => [
            // 必填-支付宝分配的 app_id
            'app_id' => env('ALIPAY_APP_ID', ''),
            // 必填-应用私钥 字符串或路径
            'app_secret_cert' => env('ALIPAY_APP_SECRET_CERT', ''),
            // 必填-应用公钥证书 路径
            'app_public_cert_path' => resource_path(env('ALIPAY_APP_PUBLIC_CERT_PATH', '')),
            // 必填-支付宝公钥证书 路径
            'alipay_public_cert_path' => resource_path(env('ALIPAY_PUBLIC_CERT_PATH', '')),
            // 必填-支付宝根证书 路径
            'alipay_root_cert_path' => resource_path(env('ALIPAY_ROOT_CERT_PATH', '')),
            'return_url' => env('WAP_URL') . '/pages/me/buy-success',
            'notify_url' => env('APP_URL') . '/api/payment/notify/alipay',
            // 选填-服务商模式下的服务商 id，当 mode 为 Pay::MODE_SERVICE 时使用该参数
            'service_provider_id' => '',
            // 选填-默认为正常模式。可选为： MODE_NORMAL, MODE_SANDBOX, MODE_SERVICE
            'mode' => env('ALIPAY_MODE', Pay::MODE_NORMAL),
        ]
    ],
    'wechat' => [
        'default' => [
            // 必填-商户号，服务商模式下为服务商商户号
            'mch_id' => env('WECHAT_MCH_ID', ''),
            // 必填-商户秘钥
            'mch_secret_key' => env('WECHAT_MCH_SECRET_KEY', ''),
            // 必填-商户私钥 字符串或路径
            'mch_secret_cert' => resource_path(env('WECHAT_MCH_SECRET_CERT', '')),
            // 必填-商户公钥证书路径
            'mch_public_cert_path' => resource_path(env('WECHAT_MCH_PUBLIC_CERT_PATH', '')),
            // 必填
            'notify_url' => env('APP_URL') . '/api/payments/notify/wechat',
//            'notify_url' => 'http://requestbin.cn:80/1fixlp51',
            // 选填-公众号 的 app_id
            'mp_app_id' => env('WECHAT_OFFICIAL_APP_ID', ''),
            // 选填-小程序 的 app_id
            'mini_app_id' => env('WECHAT_MP_APP_ID',''),
            // 选填-app 的 app_id
            'app_id' => env('WECHAT_APP_APP_ID',''),
            // 选填-微信公钥证书路径, optional，强烈建议 php-fpm 模式下配置此参数
            'wechat_public_cert_path' => [],
            // 选填-默认为正常模式。可选为： MODE_NORMAL, MODE_SERVICE
            'mode' => Pay::MODE_NORMAL,
        ]
    ],
    'http' => [ // optional
        'timeout' => 15.0,
        'connect_timeout' => 15.0,
        // 更多配置项请参考 [Guzzle](https://guzzle-cn.readthedocs.io/zh_CN/latest/request-options.html)
    ],
    // optional，默认 warning；日志路径为：sys_get_temp_dir().'/logs/yansongda.pay.log'
    'logger' => [
        'enable' => true,
        'level' => 'info',
    ],
];
