<?php declare(strict_types=1);

return [
    'table' => env('ELASTIC_MIGRATIONS_TABLE', 'elastic_migrations'),// 迁移表名
    'connection' => env('ELASTIC_MIGRATIONS_CONNECTION', 'mysql'),// 数据库链接
    'storage_directory' => env('ELASTIC_MIGRATIONS_DIRECTORY', base_path('elastic/migrations')),// 迁移目录
    'index_name_prefix' => env('ELASTIC_MIGRATIONS_INDEX_NAME_PREFIX', env('SCOUT_PREFIX', '')),// 索引前缀
    'alias_name_prefix' => env('ELASTIC_MIGRATIONS_ALIAS_NAME_PREFIX', env('SCOUT_PREFIX', '')),// 别名前缀
];
