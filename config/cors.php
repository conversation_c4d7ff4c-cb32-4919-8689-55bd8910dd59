<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    'paths' => ['api/*', 'admin/*', 'org/*', 'sanctum/csrf-cookie'],

    'allowed_methods' => ['*'],

    'allowed_origins' => [
        'https://hgb-admin.shiwusuo100.com',
        'https://hgb-org.shiwusuo100.com',
        'https://hgb-pc.shiwusuo100.com',
        'https://hgb-admin.test.pp.cc',
        'http://hgb-admin.test.pp.cc',
        'https://hgb-org.test.pp.cc',
        'https://hgb-pc.test.pp.cc',
        'http://localhost:5183',
        'http://localhost:9527',
        'http://localhost:9528',
        'http://localhost:5183',
        'http://localhost:5182',
        'http://**************:5182',
        'null' //<- 七牛上传后 303 跳回 Origin 是 null 会导致跨域问题，这里加 null 就解决了
    ],

    'allowed_origins_patterns' => [],

    'allowed_headers' => ['*'],

    'exposed_headers' => ['*'],

    'max_age' => 86400,

    'supports_credentials' => false,

];
