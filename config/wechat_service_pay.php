<?php

use <PERSON><PERSON>g<PERSON>\Pay\Pay;

return [
    'wechat' => [
        'default' => [
            'mch_id' => env('WECHAT_SERVICE_MCH_ID', ''),
            'mch_secret_key' => env('WECHAT_SERVICE_MCH_SECRET_KEY', ''),
            'mch_secret_cert' => resource_path(env('WECHAT_SERVICE_MCH_SECRET_CERT', '')),
            'mch_public_cert_path' => resource_path(env('WECHAT_SERVICE_MCH_PUBLIC_CERT_PATH', '')),
//            'notify_url' => env('APP_URL') . '/api/payments/notify-service/wechat',
//            'notify_url' => 'https://b256166b4681.ngrok-free.app/api/payments/notify-service/wechat',
            'mini_app_id' => env('WECHAT_SERVICE_MINI_APP_ID',''),
            // 若是在服务商的小程序中调起支付，则生成签名值时的 appid 需与下单时的 sp_appid 一致。
            'sub_mini_app_id' => env('WECHAT_SERVICE_MINI_APP_ID',''),
            'wechat_public_cert_path' => [
                '2772A0B22C1F4D04A84908A8A2BF2C7D5913BD66' => resource_path('wechat_server_pay_cert/2772A0B22C1F4D04A84908A8A2BF2C7D5913BD66.crt'),
                'PUB_KEY_ID_0117230355352025072300381914003004' => resource_path('wechat_server_pay_cert/pub_key.pem')
            ],
            'mode' => Pay::MODE_SERVICE,
        ],
    ],
    'http' => [
        'timeout' => 15.0,
        'connect_timeout' => 15.0,
    ],
    'logger' => [
        'enable' => true,
        'level' => 'info',
    ],
];
