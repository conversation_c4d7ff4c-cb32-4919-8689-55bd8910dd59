{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "9a4c543d5743373f5fb97d60b0a2f4bb", "packages": [{"name": "archtechx/enums", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/archtechx/enums.git", "reference": "a99ee1a7e083736c22d3a44fd3a988e7e472cf96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/archtechx/enums/zipball/a99ee1a7e083736c22d3a44fd3a988e7e472cf96", "reference": "a99ee1a7e083736c22d3a44fd3a988e7e472cf96", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"larastan/larastan": "^2.4", "orchestra/testbench": "^8.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ArchTech\\Enums\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Helpers for making PHP enums more lovable.", "support": {"issues": "https://github.com/archtechx/enums/issues", "source": "https://github.com/archtechx/enums/tree/v1.0.1"}, "time": "2024-01-28T17:52:47+00:00"}, {"name": "babenkoivan/elastic-adapter", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/babenkoivan/elastic-adapter.git", "reference": "16179d073785070984122eeaaa3f57651c673dec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/babenkoivan/elastic-adapter/zipball/16179d073785070984122eeaaa3f57651c673dec", "reference": "16179d073785070984122eeaaa3f57651c673dec", "shasum": ""}, "require": {"elasticsearch/elasticsearch": "^7.3", "php": "^7.3 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "orchestra/testbench": "^6.20", "phpstan/phpstan": "^0.12.32", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"ElasticAdapter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adapter for official PHP Elasticsearch client", "keywords": ["adapter", "client", "elastic", "elasticsearch", "php"], "support": {"issues": "https://github.com/babenkoivan/elastic-adapter/issues", "source": "https://github.com/babenkoivan/elastic-adapter/tree/v2.4.0"}, "funding": [{"url": "https://ko-fi.com/ivanbabenko", "type": "ko-fi"}, {"url": "https://paypal.me/babenkoi", "type": "paypal"}], "time": "2022-02-20T11:46:46+00:00"}, {"name": "babenkoivan/elastic-client", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/babenkoivan/elastic-client.git", "reference": "a1e818b444c5e64afd33a578aa4a009c54aff065"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/babenkoivan/elastic-client/zipball/a1e818b444c5e64afd33a578aa4a009c54aff065", "reference": "a1e818b444c5e64afd33a578aa4a009c54aff065", "shasum": ""}, "require": {"elasticsearch/elasticsearch": "^7.3", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "orchestra/testbench": "^6.12", "phpstan/phpstan": "^0.12.32", "phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"laravel": {"providers": ["ElasticClient\\ServiceProvider"]}}, "autoload": {"psr-4": {"ElasticClient\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The official PHP Elasticsearch client integrated with Laravel", "keywords": ["client", "elastic", "elasticsearch", "laravel", "php"], "support": {"issues": "https://github.com/babenkoivan/elastic-client/issues", "source": "https://github.com/babenkoivan/elastic-client/tree/v1.2.0"}, "funding": [{"url": "https://www.buymeacoffee.com/ivanbabenko", "type": "buymeacoffee"}, {"url": "https://paypal.me/babenkoi", "type": "paypal"}], "time": "2021-02-16T07:28:08+00:00"}, {"name": "babenkoivan/elastic-migrations", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/babenkoivan/elastic-migrations.git", "reference": "cddfd3232a24a6b5f4f5bdc4bd5a21689759f1f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/babenkoivan/elastic-migrations/zipball/cddfd3232a24a6b5f4f5bdc4bd5a21689759f1f5", "reference": "cddfd3232a24a6b5f4f5bdc4bd5a21689759f1f5", "shasum": ""}, "require": {"babenkoivan/elastic-adapter": "^2.0", "babenkoivan/elastic-client": "^1.2", "php": "^7.3 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "orchestra/testbench": "^6.12", "phpstan/phpstan": "^0.12.32", "phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"laravel": {"providers": ["ElasticMigrations\\ServiceProvider"]}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"ElasticMigrations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Elasticsearch migrations for Laravel", "keywords": ["elastic", "elasticsearch", "laravel", "migrations", "php"], "support": {"issues": "https://github.com/babenkoivan/elastic-migrations/issues", "source": "https://github.com/babenkoivan/elastic-migrations/tree/v2.0.1"}, "funding": [{"url": "https://ko-fi.com/ivanbabenko", "type": "ko-fi"}, {"url": "https://paypal.me/babenkoi", "type": "paypal"}], "time": "2022-01-16T11:01:35+00:00"}, {"name": "bacon/bacon-qr-code", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "f9cc1f52b5a463062251d666761178dbdb6b544f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/f9cc1f52b5a463062251d666761178dbdb6b544f", "reference": "f9cc1f52b5a463062251d666761178dbdb6b544f", "shasum": ""}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^8.1"}, "require-dev": {"phly/keep-a-changelog": "^2.12", "phpunit/phpunit": "^10.5.11 || 11.0.4", "spatie/phpunit-snapshot-assertions": "^5.1.5", "squizlabs/php_codesniffer": "^3.9"}, "suggest": {"ext-imagick": "to generate QR code images"}, "type": "library", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/v3.0.1"}, "time": "2024-10-01T13:55:55+00:00"}, {"name": "brick/math", "version": "0.11.0", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/0ad82ce168c82ba30d1c01ec86116ab52f589478", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.0", "vimeo/psalm": "5.0.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.11.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2023-01-15T23:15:59+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"doctrine/dbal": "<3.7.0 || >=4.0.0"}, "require-dev": {"doctrine/dbal": "^3.7.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/2.1.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2023-12-11T17:09:12+00:00"}, {"name": "composer/semver", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/35e8d0af4486141bc745f23a29cc2091eb624a32", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-08-31T09:50:34+00:00"}, {"name": "dasprid/enum", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DASPRiD/Enum/zipball/8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "shasum": ""}, "require": {"php": ">=7.1 <9.0"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "*"}, "type": "library", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "support": {"issues": "https://github.com/DASPRiD/Enum/issues", "source": "https://github.com/DASPRiD/Enum/tree/1.0.6"}, "time": "2024-08-09T14:30:48+00:00"}, {"name": "dflydev/dot-access-data", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "f41715465d65213d644d3141a6a93081be5d3549"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/f41715465d65213d644d3141a6a93081be5d3549", "reference": "f41715465d65213d644d3141a6a93081be5d3549", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.2"}, "time": "2022-10-27T11:44:00+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/dbal", "version": "3.8.5", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "0e3536ba088a749985c8801105b6b3ac6c1280b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/0e3536ba088a749985c8801105b6b3ac6c1280b6", "reference": "0e3536ba088a749985c8801105b6b3ac6c1280b6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1|^2", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "12.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.1", "phpstan/phpstan": "1.11.1", "phpstan/phpstan-strict-rules": "^1.6", "phpunit/phpunit": "9.6.19", "psalm/plugin-phpunit": "0.18.4", "slevomat/coding-standard": "8.13.1", "squizlabs/php_codesniffer": "3.9.2", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/console": "^4.4|^5.4|^6.0|^7.0", "vimeo/psalm": "4.30.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.8.5"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2024-06-08T17:49:56+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "dragon-code/contracts", "version": "2.23.0", "source": {"type": "git", "url": "https://github.com/TheDragonCode/contracts.git", "reference": "44dbad923f152e0dc2699fbac2d33b65dd6a8f7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TheDragonCode/contracts/zipball/44dbad923f152e0dc2699fbac2d33b65dd6a8f7d", "reference": "44dbad923f152e0dc2699fbac2d33b65dd6a8f7d", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-message": "^1.0.1 || ^2.0", "symfony/http-kernel": "^4.0 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.23"}, "conflict": {"andrey-helldar/contracts": "*"}, "require-dev": {"illuminate/database": "^10.0 || ^11.0", "phpdocumentor/reflection-docblock": "^5.0"}, "type": "library", "autoload": {"psr-4": {"DragonCode\\Contracts\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://dragon-code.pro"}], "description": "A set of contracts for any project", "keywords": ["contracts", "interfaces"], "support": {"source": "https://github.com/TheDragonCode/contracts"}, "funding": [{"url": "https://boosty.to/dragon-code", "type": "boosty"}, {"url": "https://www.donationalerts.com/r/dragon_code", "type": "donationalerts"}, {"url": "https://yoomoney.ru/to/410012608840929", "type": "<PERSON><PERSON><PERSON>"}], "time": "2024-03-11T20:15:12+00:00"}, {"name": "dragon-code/support", "version": "6.13.0", "source": {"type": "git", "url": "https://github.com/TheDragonCode/support.git", "reference": "a6f0468e32581efbccb23190b6d5c880cc519747"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TheDragonCode/support/zipball/a6f0468e32581efbccb23190b6d5c880cc519747", "reference": "a6f0468e32581efbccb23190b6d5c880cc519747", "shasum": ""}, "require": {"dragon-code/contracts": "^2.22.0", "ext-bcmath": "*", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-mbstring": "*", "php": "^8.1", "psr/http-message": "^1.0.1 || ^2.0", "symfony/polyfill-php81": "^1.25", "voku/portable-ascii": "^1.4.8 || ^2.0.1"}, "conflict": {"andrey-helldar/support": "*"}, "require-dev": {"illuminate/contracts": "^9.0 || ^10.0 || ^11.0", "phpunit/phpunit": "^9.6 || ^11.0", "symfony/var-dumper": "^6.0 || ^7.0"}, "suggest": {"dragon-code/laravel-support": "Various helper files for the Laravel and Lumen frameworks", "symfony/thanks": "Give thanks (in the form of a GitHub) to your fellow PHP package maintainers"}, "type": "library", "extra": {"dragon-code": {"docs-generator": {"preview": {"brand": "php", "vendor": "The Dragon Code"}}}}, "autoload": {"psr-4": {"DragonCode\\Support\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://dragon-code.pro"}], "description": "Support package is a collection of helpers and tools for any project.", "keywords": ["dragon", "dragon-code", "framework", "helper", "helpers", "laravel", "php", "support", "symfony", "yii", "yii2"], "support": {"issues": "https://github.com/TheDragonCode/support/issues", "source": "https://github.com/TheDragonCode/support"}, "funding": [{"url": "https://boosty.to/dragon-code", "type": "boosty"}, {"url": "https://www.donationalerts.com/r/dragon_code", "type": "donationalerts"}, {"url": "https://yoomoney.ru/to/410012608840929", "type": "<PERSON><PERSON><PERSON>"}], "time": "2024-03-12T20:45:00+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "shasum": ""}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-webmozart-assert": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.3"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2023-08-10T19:36:49+00:00"}, {"name": "egulias/email-validator", "version": "4.0.2", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ebaaf5be6c0286928352e054f2d5125608e5405e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ebaaf5be6c0286928352e054f2d5125608e5405e", "reference": "ebaaf5be6c0286928352e054f2d5125608e5405e", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.2"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2023-10-06T06:47:41+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.0", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "1890f9d7fde076b5a3ddcf579a802af05b2e781b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/1890f9d7fde076b5a3ddcf579a802af05b2e781b", "reference": "1890f9d7fde076b5a3ddcf579a802af05b2e781b", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v7.17.0"}, "time": "2022-02-03T13:40:04+00:00"}, {"name": "endroid/qr-code", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/endroid/qr-code.git", "reference": "393fec6c4cbdc1bd65570ac9d245704428010122"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/endroid/qr-code/zipball/393fec6c4cbdc1bd65570ac9d245704428010122", "reference": "393fec6c4cbdc1bd65570ac9d245704428010122", "shasum": ""}, "require": {"bacon/bacon-qr-code": "^3.0", "php": "^8.1"}, "require-dev": {"endroid/quality": "dev-main", "ext-gd": "*", "khanamiryan/qrcode-detector-decoder": "^2.0.2", "setasign/fpdf": "^1.8.2"}, "suggest": {"ext-gd": "Enables you to write PNG images", "khanamiryan/qrcode-detector-decoder": "Enables you to use the image validator", "roave/security-advisories": "Makes sure package versions with known security issues are not installed", "setasign/fpdf": "Enables you to use the PDF writer"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.x-dev"}}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Endroid QR Code", "homepage": "https://github.com/endroid/qr-code", "keywords": ["code", "endroid", "php", "qr", "qrcode"], "support": {"issues": "https://github.com/endroid/qr-code/issues", "source": "https://github.com/endroid/qr-code/tree/5.1.0"}, "funding": [{"url": "https://github.com/endroid", "type": "github"}], "time": "2024-09-08T08:52:55+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/b4b5a025dfee70d6cd34c780e07330eb93d5b997", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.1.0"}, "time": "2022-10-24T12:58:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/7887fc8488013065f72f977dcb281994f5fde9f4", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.2"}, "time": "2022-12-07T11:28:53+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.18.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "cb56001e54359df7ae76dc522d08845dc741621b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/cb56001e54359df7ae76dc522d08845dc741621b", "reference": "cb56001e54359df7ae76dc522d08845dc741621b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.18.0"}, "time": "2024-11-01T03:51:45+00:00"}, {"name": "fileeye/mimemap", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/FileEye/MimeMap.git", "reference": "0795b7db12838ffb7bc564e0a02cf53fb1463ec0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FileEye/MimeMap/zipball/0795b7db12838ffb7bc564e0a02cf53fb1463ec0", "reference": "0795b7db12838ffb7bc564e0a02cf53fb1463ec0", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"composer-runtime-api": "^2.0.0", "phpstan/phpstan": "^1.2", "phpunit/phpunit": "^9 | ^10", "sebastian/comparator": ">=4", "sebastian/diff": ">=4", "squizlabs/php_codesniffer": ">=3.6", "symfony/console": ">=5.4", "symfony/filesystem": ">=5.4", "symfony/var-dumper": ">=5.4", "symfony/yaml": ">=5.4", "vimeo/psalm": "^4.23 | ^5"}, "bin": ["bin/fileeye-mimemap"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"FileEye\\MimeMap\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "description": "A PHP library to handle MIME Content-Type fields and their related file extensions.", "homepage": "https://github.com/FileEye/MimeMap", "keywords": ["mime", "mime-database", "mime-parser", "mime-type"], "support": {"issues": "https://github.com/FileEye/MimeMap/issues", "source": "https://github.com/FileEye/MimeMap/tree/2.0.3"}, "time": "2023-11-11T14:14:23+00:00"}, {"name": "fruitcake/php-cors", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/fruitcake/php-cors.git", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/php-cors/zipball/3d158f36e7875e2f040f37bc0573956240a5a38b", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b", "shasum": ""}, "require": {"php": "^7.4|^8.0", "symfony/http-foundation": "^4.4|^5.4|^6|^7"}, "require-dev": {"phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barryvdh", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library for the Symfony HttpFoundation", "homepage": "https://github.com/fruitcake/php-cors", "keywords": ["cors", "laravel", "symfony"], "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.3.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2023-10-12T05:21:21+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "fbd48bce38f73f8a4ec8583362e732e4095e5862"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/fbd48bce38f73f8a4ec8583362e732e4095e5862", "reference": "fbd48bce38f73f8a4ec8583362e732e4095e5862", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.2"}, "require-dev": {"phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2023-11-12T22:16:48+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/41042bc7ab002487b876a0683fc8dce04ddce104", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-12-03T20:35:24+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/bbff78d96034045e58e13dedd6ad91b5d1253223", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-12-03T20:19:20+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.6.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/45b30f99ac27b5ca93cb4831afe16285f57b8221", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-12-03T20:05:35+00:00"}, {"name": "guzzlehttp/uri-template", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/guzzle/uri-template.git", "reference": "ecea8feef63bd4fef1f037ecb288386999ecc11c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/uri-template/zipball/ecea8feef63bd4fef1f037ecb288386999ecc11c", "reference": "ecea8feef63bd4fef1f037ecb288386999ecc11c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "uri-template/tests": "1.0.0"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\UriTemplate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "A polyfill class for uri_template of PHP", "keywords": ["guzzlehttp", "uri-template"], "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/uri-template", "type": "tidelift"}], "time": "2023-12-03T19:50:20+00:00"}, {"name": "hidehalo/nanoid-php", "version": "1.1.13", "source": {"type": "git", "url": "https://github.com/hidehalo/nanoid-php.git", "reference": "3fc7c949f4e655939cc30e7110d658af3dbb0e30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hidehalo/nanoid-php/zipball/3fc7c949f4e655939cc30e7110d658af3dbb0e30", "reference": "3fc7c949f4e655939cc30e7110d658af3dbb0e30", "shasum": ""}, "require": {"paragonie/random_compat": ">=2.0", "php": "~5.6|~7.0|~8.0"}, "require-dev": {"phpunit/phpunit": ">=5.6", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Hidehalo\\Nanoid\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/hidehalo", "role": "Owner"}], "description": "A copy of nanoid in PHP", "homepage": "https://github.com/hidehalo/nanoid-php", "keywords": ["<PERSON><PERSON><PERSON>", "nanoid-php"], "support": {"issues": "https://github.com/hidehalo/nanoid-php/issues", "source": "https://github.com/hidehalo/nanoid-php/tree/1.1.13"}, "time": "2022-08-04T12:07:12+00:00"}, {"name": "huaweicloud/huaweicloud-sdk-php", "version": "3.1.100", "source": {"type": "git", "url": "https://github.com/huaweicloud/huaweicloud-sdk-php-v3.git", "reference": "3d71ba1b6c77232a29a3fc10c930b6f66db26a62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/huaweicloud/huaweicloud-sdk-php-v3/zipball/3d71ba1b6c77232a29a3fc10c930b6f66db26a62", "reference": "3d71ba1b6c77232a29a3fc10c930b6f66db26a62", "shasum": ""}, "require": {"guzzlehttp/guzzle": ">=6.3.0", "guzzlehttp/promises": ">=1.3.1", "guzzlehttp/psr7": ">=1.4.2", "monolog/monolog": ">=2.4.0 || <= 2.9.0", "php": ">=5.6.0", "psr/http-message": ">=1.0.1"}, "type": "library", "autoload": {"psr-4": {"HuaweiCloud\\SDK\\": "Services/", "HuaweiCloud\\SDK\\_As\\": "Services/As/", "HuaweiCloud\\SDK\\Core\\": "Core/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "HuaweiCloud_SDK", "email": "<EMAIL>", "homepage": "https://sdkcenter.developer.huaweicloud.com/?language=PHP"}], "description": "Huawei Cloud SDK for PHP", "keywords": ["api", "php", "rest", "sdk"], "support": {"issues": "https://github.com/huaweicloud/huaweicloud-sdk-php-v3/issues", "source": "https://github.com/huaweicloud/huaweicloud-sdk-php-v3/tree/v3.1.100"}, "time": "2024-07-04T08:42:54+00:00"}, {"name": "kosinix/grafika", "version": "2.0.8", "source": {"type": "git", "url": "https://github.com/kosinix/grafika.git", "reference": "211f61fc334b8b36616b23e8af7c5727971d96ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kosinix/grafika/zipball/211f61fc334b8b36616b23e8af7c5727971d96ee", "reference": "211f61fc334b8b36616b23e8af7c5727971d96ee", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "type": "library", "autoload": {"psr-4": {"Grafika\\": "src/<PERSON>ika"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT", "GPL-2.0+"], "authors": [{"name": "<PERSON>", "homepage": "https://www.kosinix.com"}], "description": "An image manipulation library for PHP.", "homepage": "http://kosinix.github.io/grafika", "keywords": ["grafika"], "support": {"issues": "https://github.com/kosinix/grafika/issues", "source": "https://github.com/kosinix/grafika/tree/develop"}, "time": "2017-06-20T03:13:49+00:00"}, {"name": "laravel-lang/locale-list", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/Laravel-Lang/locale-list.git", "reference": "c525c99a87af6d42414c4459460b42c7fdc29cfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Lara<PERSON>-Lang/locale-list/zipball/c525c99a87af6d42414c4459460b42c7fdc29cfe", "reference": "c525c99a87af6d42414c4459460b42c7fdc29cfe", "shasum": ""}, "require": {"archtechx/enums": "^0.3.2 || ^1.0", "php": "^8.1"}, "type": "library", "autoload": {"psr-4": {"LaravelLang\\LocaleList\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://dragon-code.pro"}, {"name": "<PERSON>vel-Lang Team", "homepage": "https://laravel-lang.com"}], "description": "List of localizations available in Laravel Lang projects", "keywords": ["<PERSON><PERSON><PERSON>lang", "lang", "languages", "laravel", "locale", "locales", "localization", "translation", "translations"], "support": {"issues": "https://github.com/Lara<PERSON>-Lang/locale-list/issues", "source": "https://github.com/Laravel-Lang/locale-list"}, "time": "2024-01-17T08:34:28+00:00"}, {"name": "laravel-lang/locales", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/Laravel-Lang/locales.git", "reference": "8cd288c5041731befc3b8cf9e63b8cc44b62cdc0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Laravel-Lang/locales/zipball/8cd288c5041731befc3b8cf9e63b8cc44b62cdc0", "reference": "8cd288c5041731befc3b8cf9e63b8cc44b62cdc0", "shasum": ""}, "require": {"archtechx/enums": "^0.3.2 || ^1.0", "dragon-code/support": "^6.11.3", "ext-json": "*", "illuminate/collections": "^10.0 || ^11.0", "laravel-lang/locale-list": "^1.2", "laravel-lang/native-country-names": "^1.3", "laravel-lang/native-currency-names": "^1.3", "laravel-lang/native-locale-names": "^2.2", "php": "^8.1"}, "require-dev": {"orchestra/testbench": "^8.0 || ^9.0", "pestphp/pest": "^2.24.1", "symfony/var-dumper": "^6.0 || ^7.0"}, "type": "library", "extra": {"laravel": {"providers": ["LaravelLang\\Locales\\ServiceProvider"]}}, "autoload": {"psr-4": {"LaravelLang\\Locales\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>vel-Lang Team", "homepage": "https://laravel-lang.com"}], "description": "Basic functionality for working with localizations", "keywords": ["laravel", "locale", "locales", "localization", "translation", "translations"], "support": {"issues": "https://github.com/Laravel-Lang/locales/issues", "source": "https://github.com/Laravel-Lang/locales"}, "time": "2024-03-13T09:48:27+00:00"}, {"name": "laravel-lang/native-country-names", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-<PERSON>/native-country-names.git", "reference": "cddca6627c8732770a2e2c11c921753b6d0adb66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON>-<PERSON>/native-country-names/zipball/cddca6627c8732770a2e2c11c921753b6d0adb66", "reference": "cddca6627c8732770a2e2c11c921753b6d0adb66", "shasum": ""}, "require": {"dragon-code/support": "^6.11", "ext-json": "*", "illuminate/collections": "^10.0 || ^11.0", "php": "^8.1"}, "require-dev": {"illuminate/support": "^10.0 || ^11.0", "laravel-lang/locale-list": "^1.2", "pestphp/pest": "^2.24.3", "punic/punic": "^3.8", "symfony/console": "^6.3 || ^7.0", "symfony/process": "^6.3 || ^7.0", "symfony/var-dumper": "^6.3 || ^7.0", "vlucas/phpdotenv": "^5.6"}, "type": "library", "autoload": {"psr-4": {"LaravelLang\\NativeCountryNames\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>vel-Lang Team", "homepage": "https://laravel-lang.com"}], "description": "The project contains native translations of country names", "keywords": ["<PERSON><PERSON><PERSON>lang", "countries", "country", "lang", "languages", "laravel", "locale", "locales", "localization", "territories", "territory", "translation", "translations"], "support": {"issues": "https://github.com/<PERSON><PERSON>-<PERSON>/native-country-names/issues", "source": "https://github.com/<PERSON><PERSON>-<PERSON>/native-country-names"}, "time": "2024-03-13T09:34:55+00:00"}, {"name": "laravel-lang/native-currency-names", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-<PERSON>/native-currency-names.git", "reference": "2e0fbe039421ac753b4fd5256bd239147a0a88fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON>-<PERSON>/native-currency-names/zipball/2e0fbe039421ac753b4fd5256bd239147a0a88fe", "reference": "2e0fbe039421ac753b4fd5256bd239147a0a88fe", "shasum": ""}, "require": {"dragon-code/support": "^6.11", "ext-json": "*", "illuminate/collections": "^10.0 || ^11.0", "php": "^8.1"}, "require-dev": {"illuminate/support": "^10.0 || ^11.0", "laravel-lang/locale-list": "^1.2", "pestphp/pest": "^2.24.3", "punic/punic": "^3.8", "symfony/console": "^6.3 || ^7.0", "symfony/process": "^6.3 || ^7.0", "symfony/var-dumper": "^6.3 || ^7.0", "vlucas/phpdotenv": "^5.6"}, "type": "library", "autoload": {"psr-4": {"LaravelLang\\NativeCurrencyNames\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>vel-Lang Team", "homepage": "https://laravel-lang.com"}], "description": "The project contains native translations of currency names", "keywords": ["<PERSON><PERSON><PERSON>lang", "currency", "lang", "languages", "laravel", "locale", "locales", "localization", "translation", "translations"], "support": {"issues": "https://github.com/<PERSON><PERSON>-<PERSON>/native-currency-names/issues", "source": "https://github.com/<PERSON><PERSON>-<PERSON>/native-currency-names"}, "time": "2024-03-13T09:40:02+00:00"}, {"name": "laravel-lang/native-locale-names", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-<PERSON>/native-locale-names.git", "reference": "39ef3330938b74277456049bf386453109e4d05c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON>-<PERSON>/native-locale-names/zipball/39ef3330938b74277456049bf386453109e4d05c", "reference": "39ef3330938b74277456049bf386453109e4d05c", "shasum": ""}, "require": {"dragon-code/support": "^6.11", "ext-json": "*", "php": "^8.1"}, "require-dev": {"illuminate/support": "^10.31 || ^11.0", "laravel-lang/locale-list": "^1.2", "pestphp/pest": "^2.24.3", "punic/punic": "^3.8", "symfony/console": "^6.3 || ^7.0", "symfony/process": "^6.3 || ^7.0", "symfony/var-dumper": "^6.3 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"LaravelLang\\NativeLocaleNames\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>vel-Lang Team", "homepage": "https://laravel-lang.com"}], "description": "The project contains native translations of locale names", "keywords": ["<PERSON><PERSON><PERSON>lang", "lang", "languages", "laravel", "locale", "locales", "localization", "translation", "translations"], "support": {"issues": "https://github.com/<PERSON><PERSON>-<PERSON>/native-locale-names/issues", "source": "https://github.com/<PERSON><PERSON>-<PERSON>/native-locale-names"}, "time": "2024-03-13T09:28:19+00:00"}, {"name": "laravel/framework", "version": "v10.47.0", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "fce29b8de62733cdecbe12e3bae801f83fff2ea4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/fce29b8de62733cdecbe12e3bae801f83fff2ea4", "reference": "fce29b8de62733cdecbe12e3bae801f83fff2ea4", "shasum": ""}, "require": {"brick/math": "^0.9.3|^0.10.2|^0.11|^0.12", "composer-runtime-api": "^2.2", "doctrine/inflector": "^2.0.5", "dragonmantank/cron-expression": "^3.3.2", "egulias/email-validator": "^3.2.1|^4.0", "ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-session": "*", "ext-tokenizer": "*", "fruitcake/php-cors": "^1.2", "guzzlehttp/uri-template": "^1.0", "laravel/prompts": "^0.1.9", "laravel/serializable-closure": "^1.3", "league/commonmark": "^2.2.1", "league/flysystem": "^3.8.0", "monolog/monolog": "^3.0", "nesbot/carbon": "^2.67", "nunomaduro/termwind": "^1.13", "php": "^8.1", "psr/container": "^1.1.1|^2.0.1", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "ramsey/uuid": "^4.7", "symfony/console": "^6.2", "symfony/error-handler": "^6.2", "symfony/finder": "^6.2", "symfony/http-foundation": "^6.4", "symfony/http-kernel": "^6.2", "symfony/mailer": "^6.2", "symfony/mime": "^6.2", "symfony/process": "^6.2", "symfony/routing": "^6.2", "symfony/uid": "^6.2", "symfony/var-dumper": "^6.2", "tijsverkoyen/css-to-inline-styles": "^2.2.5", "vlucas/phpdotenv": "^5.4.1", "voku/portable-ascii": "^2.0"}, "conflict": {"carbonphp/carbon-doctrine-types": ">=3.0", "doctrine/dbal": ">=4.0", "phpunit/phpunit": ">=11.0.0", "tightenco/collect": "<5.5.33"}, "provide": {"psr/container-implementation": "1.1|2.0", "psr/simple-cache-implementation": "1.0|2.0|3.0"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/collections": "self.version", "illuminate/conditionable": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/macroable": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/process": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/testing": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"ably/ably-php": "^1.0", "aws/aws-sdk-php": "^3.235.5", "doctrine/dbal": "^3.5.1", "ext-gmp": "*", "fakerphp/faker": "^1.21", "guzzlehttp/guzzle": "^7.5", "league/flysystem-aws-s3-v3": "^3.0", "league/flysystem-ftp": "^3.0", "league/flysystem-path-prefixing": "^3.3", "league/flysystem-read-only": "^3.3", "league/flysystem-sftp-v3": "^3.0", "mockery/mockery": "^1.5.1", "nyholm/psr7": "^1.2", "orchestra/testbench-core": "^8.18", "pda/pheanstalk": "^4.0", "phpstan/phpstan": "^1.4.7", "phpunit/phpunit": "^10.0.7", "predis/predis": "^2.0.2", "symfony/cache": "^6.2", "symfony/http-client": "^6.2.4", "symfony/psr-http-message-bridge": "^2.0"}, "suggest": {"ably/ably-php": "Required to use the Ably broadcast driver (^1.0).", "aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage, and SES mail driver (^3.235.5).", "brianium/paratest": "Required to run tests in parallel (^6.0).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^3.5.1).", "ext-apcu": "Required to use the APC cache driver.", "ext-fileinfo": "Required to use the Filesystem class.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker and console signal trapping.", "ext-pdo": "Required to use all database features.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "filp/whoops": "Required for friendly error pages in development (^2.14.3).", "guzzlehttp/guzzle": "Required to use the HTTP Client and the ping methods on schedules (^7.5).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.0).", "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.0).", "league/flysystem-path-prefixing": "Required to use the scoped driver (^3.3).", "league/flysystem-read-only": "Required to use read-only disks (^3.3)", "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.0).", "mockery/mockery": "Required to use mocking (^1.5.1).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "phpunit/phpunit": "Required to use assertions and run tests (^9.5.8|^10.0.7).", "predis/predis": "Required to use the predis connector (^2.0.2).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^6.0|^7.0).", "symfony/cache": "Required to PSR-6 cache bridge (^6.2).", "symfony/filesystem": "Required to enable support for relative symbolic links (^6.2).", "symfony/http-client": "Required to enable support for the Symfony API mail transports (^6.2).", "symfony/mailgun-mailer": "Required to enable support for the Mailgun mail transport (^6.2).", "symfony/postmark-mailer": "Required to enable support for the Postmark mail transport (^6.2).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "autoload": {"files": ["src/Illuminate/Collections/helpers.php", "src/Illuminate/Events/functions.php", "src/Illuminate/Filesystem/functions.php", "src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/", "Illuminate\\Support\\": ["src/Illuminate/Macroable/", "src/Illuminate/Collections/", "src/Illuminate/Conditionable/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2024-03-05T15:18:36+00:00"}, {"name": "laravel/prompts", "version": "v0.1.16", "source": {"type": "git", "url": "https://github.com/laravel/prompts.git", "reference": "ca6872ab6aec3ab61db3a61f83a6caf764ec7781"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/prompts/zipball/ca6872ab6aec3ab61db3a61f83a6caf764ec7781", "reference": "ca6872ab6aec3ab61db3a61f83a6caf764ec7781", "shasum": ""}, "require": {"ext-mbstring": "*", "illuminate/collections": "^10.0|^11.0", "php": "^8.1", "symfony/console": "^6.2|^7.0"}, "conflict": {"illuminate/console": ">=10.17.0 <10.25.0", "laravel/framework": ">=10.17.0 <10.25.0"}, "require-dev": {"mockery/mockery": "^1.5", "pestphp/pest": "^2.3", "phpstan/phpstan": "^1.11", "phpstan/phpstan-mockery": "^1.1"}, "suggest": {"ext-pcntl": "Required for the spinner to be animated."}, "type": "library", "extra": {"branch-alias": {"dev-main": "0.1.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Laravel\\Prompts\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "support": {"issues": "https://github.com/laravel/prompts/issues", "source": "https://github.com/laravel/prompts/tree/v0.1.16"}, "time": "2024-02-21T19:25:27+00:00"}, {"name": "laravel/sanctum", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/laravel/sanctum.git", "reference": "8c104366459739f3ada0e994bcd3e6fd681ce3d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/sanctum/zipball/8c104366459739f3ada0e994bcd3e6fd681ce3d5", "reference": "8c104366459739f3ada0e994bcd3e6fd681ce3d5", "shasum": ""}, "require": {"ext-json": "*", "illuminate/console": "^9.21|^10.0", "illuminate/contracts": "^9.21|^10.0", "illuminate/database": "^9.21|^10.0", "illuminate/support": "^9.21|^10.0", "php": "^8.0.2"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^7.28.2|^8.8.3", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}, "laravel": {"providers": ["Laravel\\Sanctum\\SanctumServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Sanctum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel Sanctum provides a featherweight authentication system for SPAs and simple APIs.", "keywords": ["auth", "laravel", "sanctum"], "support": {"issues": "https://github.com/laravel/sanctum/issues", "source": "https://github.com/laravel/sanctum"}, "time": "2023-12-19T18:44:48+00:00"}, {"name": "laravel/scout", "version": "v9.8.0", "source": {"type": "git", "url": "https://github.com/laravel/scout.git", "reference": "f765673311226919d2526d3148a558306283bc7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/scout/zipball/f765673311226919d2526d3148a558306283bc7c", "reference": "f765673311226919d2526d3148a558306283bc7c", "shasum": ""}, "require": {"illuminate/bus": "^8.0|^9.0|^10.0", "illuminate/contracts": "^8.0|^9.0|^10.0", "illuminate/database": "^8.0|^9.0|^10.0", "illuminate/http": "^8.0|^9.0|^10.0", "illuminate/pagination": "^8.0|^9.0|^10.0", "illuminate/queue": "^8.0|^9.0|^10.0", "illuminate/support": "^8.0|^9.0|^10.0", "php": "^7.3|^8.0"}, "require-dev": {"meilisearch/meilisearch-php": "^0.19", "mockery/mockery": "^1.0", "orchestra/testbench": "^6.17|^7.0|^8.0", "php-http/guzzle7-adapter": "^1.0", "phpunit/phpunit": "^9.3"}, "suggest": {"algolia/algoliasearch-client-php": "Required to use the Algolia engine (^3.2).", "meilisearch/meilisearch-php": "Required to use the MeiliSearch engine (^0.23)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}, "laravel": {"providers": ["Laravel\\Scout\\ScoutServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Scout\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel Scout provides a driver based solution to searching your Eloquent models.", "keywords": ["algolia", "laravel", "search"], "support": {"issues": "https://github.com/laravel/scout/issues", "source": "https://github.com/laravel/scout"}, "time": "2023-01-17T16:30:55+00:00"}, {"name": "laravel/serializable-closure", "version": "v1.3.3", "source": {"type": "git", "url": "https://github.com/laravel/serializable-closure.git", "reference": "3dbf8a8e914634c48d389c1234552666b3d43754"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/3dbf8a8e914634c48d389c1234552666b3d43754", "reference": "3dbf8a8e914634c48d389c1234552666b3d43754", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"nesbot/carbon": "^2.61", "pestphp/pest": "^1.21.3", "phpstan/phpstan": "^1.8.2", "symfony/var-dumper": "^5.4.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Laravel\\SerializableClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.", "keywords": ["closure", "laravel", "serializable"], "support": {"issues": "https://github.com/laravel/serializable-closure/issues", "source": "https://github.com/laravel/serializable-closure"}, "time": "2023-11-08T14:08:06+00:00"}, {"name": "laravel/tinker", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "502e0fe3f0415d06d5db1f83a472f0f3b754bafe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/502e0fe3f0415d06d5db1f83a472f0f3b754bafe", "reference": "502e0fe3f0415d06d5db1f83a472f0f3b754bafe", "shasum": ""}, "require": {"illuminate/console": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/contracts": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "php": "^7.2.5|^8.0", "psy/psysh": "^0.11.1|^0.12.0", "symfony/var-dumper": "^4.3.4|^5.0|^6.0|^7.0"}, "require-dev": {"mockery/mockery": "~1.3.3|^1.4.2", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "suggest": {"illuminate/database": "The Illuminate Database package (^6.0|^7.0|^8.0|^9.0|^10.0|^11.0)."}, "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "support": {"issues": "https://github.com/laravel/tinker/issues", "source": "https://github.com/laravel/tinker/tree/v2.9.0"}, "time": "2024-01-04T16:10:04+00:00"}, {"name": "lcobucci/clock", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/lcobucci/clock.git", "reference": "039ef98c6b57b101d10bd11d8fdfda12cbd996dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/clock/zipball/039ef98c6b57b101d10bd11d8fdfda12cbd996dc", "reference": "039ef98c6b57b101d10bd11d8fdfda12cbd996dc", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0", "psr/clock": "^1.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"infection/infection": "^0.26", "lcobucci/coding-standard": "^9.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-deprecation-rules": "^1.1.1", "phpstan/phpstan-phpunit": "^1.3.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^9.5.27"}, "type": "library", "autoload": {"psr-4": {"Lcobucci\\Clock\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Yet another clock abstraction", "support": {"issues": "https://github.com/lcobucci/clock/issues", "source": "https://github.com/lcobucci/clock/tree/3.0.0"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2022-12-19T15:00:24+00:00"}, {"name": "lcobucci/jwt", "version": "4.3.0", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "4d7de2fe0d51a96418c0d04004986e410e87f6b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/4d7de2fe0d51a96418c0d04004986e410e87f6b4", "reference": "4d7de2fe0d51a96418c0d04004986e410e87f6b4", "shasum": ""}, "require": {"ext-hash": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-sodium": "*", "lcobucci/clock": "^2.0 || ^3.0", "php": "^7.4 || ^8.0"}, "require-dev": {"infection/infection": "^0.21", "lcobucci/coding-standard": "^6.0", "mikey179/vfsstream": "^1.6.7", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.4", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/php-invoker": "^3.1", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "support": {"issues": "https://github.com/lcobucci/jwt/issues", "source": "https://github.com/lcobucci/jwt/tree/4.3.0"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2023-01-02T13:28:00+00:00"}, {"name": "league/commonmark", "version": "2.4.2", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "91c24291965bd6d7c46c46a12ba7492f83b1cadf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/91c24291965bd6d7c46c46a12ba7492f83b1cadf", "reference": "91c24291965bd6d7c46c46a12ba7492f83b1cadf", "shasum": ""}, "require": {"ext-mbstring": "*", "league/config": "^1.1.1", "php": "^7.4 || ^8.0", "psr/event-dispatcher": "^1.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"cebe/markdown": "^1.0", "commonmark/cmark": "0.30.3", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21 || ^10.5.9 || ^11.0.0", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0 || ^7.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0 || ^7.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}, "suggest": {"symfony/yaml": "v2.3+ required if using the Front Matter extension"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and GitHub-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "support": {"docs": "https://commonmark.thephpleague.com/", "forum": "https://github.com/thephpleague/commonmark/discussions", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2024-02-02T11:59:32+00:00"}, {"name": "league/config", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/config.git", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/config/zipball/754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3.0.1", "nette/schema": "^1.2", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.2-dev"}}, "autoload": {"psr-4": {"League\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Define configuration arrays with strict schemas and access values with dot notation", "homepage": "https://config.thephpleague.com", "keywords": ["array", "config", "configuration", "dot", "dot-access", "nested", "schema"], "support": {"docs": "https://config.thephpleague.com/", "issues": "https://github.com/thephpleague/config/issues", "rss": "https://github.com/thephpleague/config/releases.atom", "source": "https://github.com/thephpleague/config"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}], "time": "2022-12-11T20:36:23+00:00"}, {"name": "league/flysystem", "version": "3.25.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "4c44347133618cccd9b3df1729647a1577b4ad99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4c44347133618cccd9b3df1729647a1577b4ad99", "reference": "4c44347133618cccd9b3df1729647a1577b4ad99", "shasum": ""}, "require": {"league/flysystem-local": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "conflict": {"async-aws/core": "<1.19.0", "async-aws/s3": "<1.14.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0", "guzzlehttp/guzzle": "<7.0", "guzzlehttp/ringphp": "<1.1.1", "phpseclib/phpseclib": "3.0.15", "symfony/http-client": "<5.2"}, "require-dev": {"async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "microsoft/azure-storage-blob": "^1.1", "phpseclib/phpseclib": "^3.0.36", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5.11|^10.0", "sabre/dav": "^4.6.0"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.25.0"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}], "time": "2024-03-09T17:06:45+00:00"}, {"name": "league/flysystem-local", "version": "3.23.1", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-local.git", "reference": "b884d2bf9b53bb4804a56d2df4902bb51e253f00"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/b884d2bf9b53bb4804a56d2df4902bb51e253f00", "reference": "b884d2bf9b53bb4804a56d2df4902bb51e253f00", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/flysystem": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Local\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Local filesystem adapter for Flysystem.", "keywords": ["Flysystem", "file", "files", "filesystem", "local"], "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.23.1"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}], "time": "2024-01-26T18:25:23+00:00"}, {"name": "league/mime-type-detection", "version": "1.15.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.15.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-01-28T23:22:08+00:00"}, {"name": "maatwebsite/excel", "version": "3.1.59", "source": {"type": "git", "url": "https://github.com/SpartnerNL/Laravel-Excel.git", "reference": "b4d8b877e31998811700283d91292ab2b9a4970d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SpartnerNL/Laravel-Excel/zipball/b4d8b877e31998811700283d91292ab2b9a4970d", "reference": "b4d8b877e31998811700283d91292ab2b9a4970d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer/semver": "^3.3", "ext-json": "*", "illuminate/support": "5.8.*||^6.0||^7.0||^8.0||^9.0||^10.0||^11.0", "php": "^7.0||^8.0", "phpoffice/phpspreadsheet": "^1.29.2", "psr/simple-cache": "^1.0||^2.0||^3.0"}, "require-dev": {"laravel/scout": "^7.0||^8.0||^9.0||^10.0", "orchestra/testbench": "^6.0||^7.0||^8.0||^9.0", "predis/predis": "^1.1"}, "type": "library", "extra": {"laravel": {"providers": ["Maatwebsite\\Excel\\ExcelServiceProvider"], "aliases": {"Excel": "Maatwebsite\\Excel\\Facades\\Excel"}}}, "autoload": {"psr-4": {"Maatwebsite\\Excel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Supercharged Excel exports and imports in Laravel", "keywords": ["PHPExcel", "batch", "csv", "excel", "export", "import", "laravel", "php", "phpspreadsheet"], "support": {"issues": "https://github.com/SpartnerNL/Laravel-Excel/issues", "source": "https://github.com/SpartnerNL/Laravel-Excel/tree/3.1.59"}, "funding": [{"url": "https://laravel-excel.com/commercial-support", "type": "custom"}, {"url": "https://github.com/patrickbrouwers", "type": "github"}], "time": "2024-10-25T15:40:14+00:00"}, {"name": "maennchen/zipstream-php", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "6187e9cc4493da94b9b63eb2315821552015fca9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/6187e9cc4493da94b9b63eb2315821552015fca9", "reference": "6187e9cc4493da94b9b63eb2315821552015fca9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php-64bit": "^8.1"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.16", "guzzlehttp/guzzle": "^7.5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^10.0", "vimeo/psalm": "^5.0"}, "suggest": {"guzzlehttp/psr7": "^2.4", "psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.1.1"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}], "time": "2024-10-10T12:33:01+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "monolog/monolog", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "c915e2634718dbc8a4a15c61b0e62e7a44e14448"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/c915e2634718dbc8a4a15c61b0e62e7a44e14448", "reference": "c915e2634718dbc8a4a15c61b0e62e7a44e14448", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpstan/phpstan": "^1.9", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-strict-rules": "^1.4", "phpunit/phpunit": "^10.1", "predis/predis": "^1.1 || ^2", "ruflin/elastica": "^7", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.5.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2023-10-27T15:32:31+00:00"}, {"name": "myclabs/php-enum", "version": "1.8.4", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "a867478eae49c9f59ece437ae7f9506bfaa27483"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/a867478eae49c9f59ece437ae7f9506bfaa27483", "reference": "a867478eae49c9f59ece437ae7f9506bfaa27483", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.4"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2022-08-04T09:53:51+00:00"}, {"name": "nesbot/carbon", "version": "2.72.3", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "0c6fd108360c562f6e4fd1dedb8233b423e91c83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/0c6fd108360c562f6e4fd1dedb8233b423e91c83", "reference": "0c6fd108360c562f6e4fd1dedb8233b423e91c83", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2024-01-25T10:35:09+00:00"}, {"name": "nette/schema", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "a6d3a6d1f545f01ef38e60f375d1cf1f4de98188"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/a6d3a6d1f545f01ef38e60f375d1cf1f4de98188", "reference": "a6d3a6d1f545f01ef38e60f375d1cf1f4de98188", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.3"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.3.0"}, "time": "2023-12-11T11:54:22+00:00"}, {"name": "nette/utils", "version": "v4.0.4", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "shasum": ""}, "require": {"php": ">=8.0 <8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.4"}, "time": "2024-01-17T16:50:36+00:00"}, {"name": "nikic/php-parser", "version": "v5.0.2", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "139676794dc1e9231bf7bcd123cfc0c99182cb13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/139676794dc1e9231bf7bcd123cfc0c99182cb13", "reference": "139676794dc1e9231bf7bcd123cfc0c99182cb13", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.0.2"}, "time": "2024-03-05T20:51:40+00:00"}, {"name": "nunomaduro/termwind", "version": "v1.15.1", "source": {"type": "git", "url": "https://github.com/nunomaduro/termwind.git", "reference": "8ab0b32c8caa4a2e09700ea32925441385e4a5dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/termwind/zipball/8ab0b32c8caa4a2e09700ea32925441385e4a5dc", "reference": "8ab0b32c8caa4a2e09700ea32925441385e4a5dc", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^8.0", "symfony/console": "^5.3.0|^6.0.0"}, "require-dev": {"ergebnis/phpstan-rules": "^1.0.", "illuminate/console": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "laravel/pint": "^1.0.0", "pestphp/pest": "^1.21.0", "pestphp/pest-plugin-mock": "^1.0", "phpstan/phpstan": "^1.4.6", "phpstan/phpstan-strict-rules": "^1.1.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}, "type": "library", "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Termwind\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Its like Tailwind CSS, but for the console.", "keywords": ["cli", "console", "css", "package", "php", "style"], "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.15.1"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://github.com/xiCO2k", "type": "github"}], "time": "2023-02-08T01:06:31+00:00"}, {"name": "nyholm/psr7", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "aa5fc277a4f5508013d571341ade0c3886d4d00e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/aa5fc277a4f5508013d571341ade0c3886d4d00e", "reference": "aa5fc277a4f5508013d571341ade0c3886d4d00e", "shasum": ""}, "require": {"php": ">=7.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0", "psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "php-http/message-factory": "^1.0", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.4", "symfony/error-handler": "^4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.8.1"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2023-11-13T09:31:12+00:00"}, {"name": "nyholm/psr7-server", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7-server.git", "reference": "4335801d851f554ca43fa6e7d2602141538854dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7-server/zipball/4335801d851f554ca43fa6e7d2602141538854dc", "reference": "4335801d851f554ca43fa6e7d2602141538854dc", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"nyholm/nsa": "^1.1", "nyholm/psr7": "^1.3", "phpunit/phpunit": "^7.0 || ^8.5 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"Nyholm\\Psr7Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "Helper classes to handle PSR-7 server requests", "homepage": "http://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7-server/issues", "source": "https://github.com/Nyholm/psr7-server/tree/1.1.0"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2023-11-08T09:30:43+00:00"}, {"name": "overtrue/easy-sms", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/overtrue/easy-sms.git", "reference": "bb88b244f0de8d1f74bc50c4c08414f4c5f30281"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/easy-sms/zipball/bb88b244f0de8d1f74bc50c4c08414f4c5f30281", "reference": "bb88b244f0de8d1f74bc50c4c08414f4c5f30281", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.2 || ^7.0", "php": ">=5.6"}, "require-dev": {"brainmaestro/composer-git-hooks": "^2.8", "jetbrains/phpstorm-attributes": "^1.0", "mockery/mockery": "~1.3.3 || ^1.4.2", "phpunit/phpunit": "^5.7 || ^7.5 || ^8.5.19 || ^9.5.8"}, "type": "library", "extra": {"hooks": {"pre-commit": ["composer check-style", "composer psalm", "composer test"], "pre-push": ["composer check-style"]}}, "autoload": {"psr-4": {"Overtrue\\EasySms\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "The easiest way to send short message.", "support": {"issues": "https://github.com/overtrue/easy-sms/issues", "source": "https://github.com/overtrue/easy-sms/tree/2.6.0"}, "funding": [{"url": "https://github.com/overtrue", "type": "github"}], "time": "2024-03-08T06:36:45+00:00"}, {"name": "overtrue/flysystem-qiniu", "version": "3.2.3", "source": {"type": "git", "url": "https://github.com/overtrue/flysystem-qiniu.git", "reference": "5e04ac38be53b57345c56e02973fc4224db776e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/flysystem-qiniu/zipball/5e04ac38be53b57345c56e02973fc4224db776e6", "reference": "5e04ac38be53b57345c56e02973fc4224db776e6", "shasum": ""}, "require": {"league/flysystem": "^3.0", "qiniu/php-sdk": "^7.2"}, "require-dev": {"mockery/mockery": "^1.4", "phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"hooks": {"pre-commit": ["composer test", "composer check-style"], "pre-push": ["composer test", "composer check-style"]}}, "autoload": {"psr-4": {"Overtrue\\Flysystem\\Qiniu\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "Flysystem adapter for the Qiniu storage.", "support": {"issues": "https://github.com/overtrue/flysystem-qiniu/issues", "source": "https://github.com/overtrue/flysystem-qiniu/tree/3.2.3"}, "funding": [{"url": "https://github.com/overtrue", "type": "github"}], "time": "2023-12-21T14:40:53+00:00"}, {"name": "overtrue/laravel-filesystem-qiniu", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/overtrue/laravel-filesystem-qiniu.git", "reference": "fd427c46454e09ff2229ce08d94a1f0433460c55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/laravel-filesystem-qiniu/zipball/fd427c46454e09ff2229ce08d94a1f0433460c55", "reference": "fd427c46454e09ff2229ce08d94a1f0433460c55", "shasum": ""}, "require": {"laravel/framework": "^9.0|^10.0", "overtrue/flysystem-qiniu": "^3.0"}, "require-dev": {"brainmaestro/composer-git-hooks": "dev-master", "laravel/pint": "^1.5", "mockery/mockery": "^1.4", "phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"laravel": {"providers": ["Overtrue\\LaravelFilesystem\\Qiniu\\QiniuStorageServiceProvider"]}, "hooks": {"pre-commit": ["composer check-style"], "pre-push": ["composer check-style"]}}, "autoload": {"psr-4": {"Overtrue\\LaravelFilesystem\\Qiniu\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "A Qiniu storage filesystem for Laravel.", "support": {"issues": "https://github.com/overtrue/laravel-filesystem-qiniu/issues", "source": "https://github.com/overtrue/laravel-filesystem-qiniu/tree/2.2.1"}, "funding": [{"url": "https://github.com/overtrue", "type": "github"}], "time": "2023-02-15T08:24:05+00:00"}, {"name": "overtrue/socialite", "version": "4.10.1", "source": {"type": "git", "url": "https://github.com/overtrue/socialite.git", "reference": "457b48f31414dc00d3fb445d6ab9355595067afe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/socialite/zipball/457b48f31414dc00d3fb445d6ab9355595067afe", "reference": "457b48f31414dc00d3fb445d6ab9355595067afe", "shasum": ""}, "require": {"ext-json": "*", "ext-openssl": "*", "guzzlehttp/guzzle": "^7.0", "php": ">=8.0.2", "symfony/psr-http-message-bridge": "^2.1|^6.0"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "laravel/pint": "^1.2", "mockery/mockery": "^1.3", "phpstan/phpstan": "^1.7", "phpunit/phpunit": "^9.0"}, "type": "library", "autoload": {"files": ["src/Contracts/FactoryInterface.php", "src/Contracts/UserInterface.php", "src/Contracts/ProviderInterface.php"], "psr-4": {"Overtrue\\Socialite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "A collection of OAuth 2 packages.", "keywords": ["<PERSON><PERSON><PERSON>", "login", "o<PERSON>h", "qcloud", "qq", "social", "wechat", "weibo"], "support": {"issues": "https://github.com/overtrue/socialite/issues", "source": "https://github.com/overtrue/socialite/tree/4.10.1"}, "funding": [{"url": "https://github.com/overtrue", "type": "github"}], "time": "2024-03-08T06:41:54+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "phpoffice/math", "version": "0.2.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/Math.git", "reference": "fc2eb6d1a61b058d5dac77197059db30ee3c8329"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/Math/zipball/fc2eb6d1a61b058d5dac77197059db30ee3c8329", "reference": "fc2eb6d1a61b058d5dac77197059db30ee3c8329", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.88 || ^1.0.0", "phpunit/phpunit": "^7.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\Math\\": "src/Math/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Progi1984", "homepage": "https://lefevre.dev"}], "description": "Math - Manipulate Math Formula", "homepage": "https://phpoffice.github.io/Math/", "keywords": ["MathML", "officemathml", "php"], "support": {"issues": "https://github.com/PHPOffice/Math/issues", "source": "https://github.com/PHPOffice/Math/tree/0.2.0"}, "time": "2024-08-12T07:30:45+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.29.6", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "08597725b84570cd6f32bf0ea92e75a803ef28c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/08597725b84570cd6f32bf0ea92e75a803ef28c2", "reference": "08597725b84570cd6f32bf0ea92e75a803ef28c2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.15", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.4 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^1.0 || ^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.29.6"}, "time": "2024-12-08T05:49:00+00:00"}, {"name": "phpoffice/phpword", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPWord.git", "reference": "8392134ce4b5dba65130ba956231a1602b848b7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPWord/zipball/8392134ce4b5dba65130ba956231a1602b848b7f", "reference": "8392134ce4b5dba65130ba956231a1602b848b7f", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-xml": "*", "php": "^7.1|^8.0", "phpoffice/math": "^0.2"}, "require-dev": {"dompdf/dompdf": "^2.0", "ext-gd": "*", "ext-libxml": "*", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.3", "mpdf/mpdf": "^8.1", "phpmd/phpmd": "^2.13", "phpstan/phpstan-phpunit": "@stable", "phpunit/phpunit": ">=7.0", "symfony/process": "^4.4 || ^5.0", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Allows writing PDF", "ext-gd2": "Allows adding images", "ext-xmlwriter": "Allows writing OOXML and ODF", "ext-xsl": "Allows applying XSL style sheet to headers, to main document part, and to footers of an OOXML template", "ext-zip": "Allows writing OOXML and ODF"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpWord\\": "src/PhpWord"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gabrielbull.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net/blog/"}, {"name": "<PERSON>", "homepage": "http://ivan.lanin.org"}, {"name": "<PERSON>", "homepage": "http://ru.linkedin.com/pub/roman-syroeshko/34/a53/994/"}, {"name": "<PERSON>"}], "description": "PHPWord - A pure PHP library for reading and writing word processing documents (OOXML, ODF, RTF, HTML, PDF)", "homepage": "https://phpoffice.github.io/PHPWord/", "keywords": ["ISO IEC 29500", "OOXML", "Office Open XML", "OpenDocument", "OpenXML", "PhpOffice", "PhpWord", "Rich Text Format", "WordprocessingML", "doc", "docx", "html", "odf", "odt", "office", "pdf", "php", "reader", "rtf", "template", "template processor", "word", "writer"], "support": {"issues": "https://github.com/PHPOffice/PHPWord/issues", "source": "https://github.com/PHPOffice/PHPWord/tree/1.3.0"}, "time": "2024-08-30T18:03:42+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.2", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "80735db690fe4fc5c76dfa7f9b770634285fa820"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/80735db690fe4fc5c76dfa7f9b770634285fa820", "reference": "80735db690fe4fc5c76dfa7f9b770634285fa820", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2023-11-12T21:59:55+00:00"}, {"name": "pp/laravel-scout-elastic", "version": "dev-master", "source": {"type": "git", "url": "***********************:yangpp/laravel-scout-elastic.git", "reference": "c0c8f2d10b63ef840e7dc56341b876094584532c"}, "require": {"laravel/scout": "^9.3", "php": "^7.2|^8.0|^8.1"}, "require-dev": {"elasticsearch/elasticsearch": "^7.9", "mockery/mockery": "^1.0", "phpunit/phpunit": "^8.0|^9.3"}, "suggest": {"elasticsearch/elasticsearch": "Required to use the Elasticsearch engine (^7.9.0)."}, "default-branch": true, "type": "library", "extra": {"laravel": {"providers": ["Tamayo\\LaravelScoutElastic\\LaravelScoutElasticProvider"]}}, "autoload": {"psr-4": {"Tamayo\\LaravelScoutElastic\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Elastic Driver for Laravel Scout", "keywords": ["elastic", "elasticsearch", "laravel", "scout"], "time": "2024-04-08T08:14:18+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/1.0.2"}, "time": "2023-04-10T20:10:41+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "psy/psysh", "version": "v0.12.0", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "750bf031a48fd07c673dbe3f11f72362ea306d0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/750bf031a48fd07c673dbe3f11f72362ea306d0d", "reference": "750bf031a48fd07c673dbe3f11f72362ea306d0d", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-main": "0.12.x-dev"}, "bamarni-bin": {"bin-links": false, "forward-command": false}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.0"}, "time": "2023-12-20T15:28:09+00:00"}, {"name": "qiniu/php-sdk", "version": "v7.12.1", "source": {"type": "git", "url": "https://github.com/qiniu/php-sdk.git", "reference": "88952a65e97b4c2fae3f954bc4419ba14e42f154"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qiniu/php-sdk/zipball/88952a65e97b4c2fae3f954bc4419ba14e42f154", "reference": "88952a65e97b4c2fae3f954bc4419ba14e42f154", "shasum": ""}, "require": {"myclabs/php-enum": "~1.5.2 || ~1.6.6 || ~1.7.7 || ~1.8.4", "php": ">=5.3.3"}, "require-dev": {"paragonie/random_compat": ">=2", "phpunit/phpunit": "^4.8 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4", "squizlabs/php_codesniffer": "^2.3 || ~3.6"}, "type": "library", "autoload": {"files": ["src/Qiniu/functions.php", "src/Qiniu/Http/Middleware/Middleware.php"], "psr-4": {"Qiniu\\": "src/<PERSON>iu"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.qiniu.com"}], "description": "Qiniu Resource (Cloud) Storage SDK for PHP", "homepage": "http://developer.qiniu.com/", "keywords": ["cloud", "qiniu", "sdk", "storage"], "support": {"issues": "https://github.com/qiniu/php-sdk/issues", "source": "https://github.com/qiniu/php-sdk/tree/v7.12.1"}, "time": "2024-03-12T07:03:12+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5", "reference": "a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.0.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2022-12-31T21:50:55+00:00"}, {"name": "ramsey/uuid", "version": "4.7.5", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "5f0df49ae5ad6efb7afa69e6bfab4e5b1e080d8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/5f0df49ae5ad6efb7afa69e6bfab4e5b1e080d8e", "reference": "5f0df49ae5ad6efb7afa69e6bfab4e5b1e080d8e", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.5"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2023-11-08T05:53:05+00:00"}, {"name": "react/promise", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-16T16:16:50+00:00"}, {"name": "<PERSON>e/laravel", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/reliese/laravel.git", "reference": "c286d2815987fa627a26cce3d2f1f04dd031c626"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reliese/laravel/zipball/c286d2815987fa627a26cce3d2f1f04dd031c626", "reference": "c286d2815987fa627a26cce3d2f1f04dd031c626", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/dbal": ">=2.5", "illuminate/console": ">=5.1", "illuminate/contracts": ">=5.1", "illuminate/database": ">=5.1", "illuminate/filesystem": ">=5.1", "illuminate/support": ">=5.1", "php": "^7.3|^8.0"}, "require-dev": {"fzaninotto/faker": "~1.4", "mockery/mockery": ">=1.4", "phpunit/phpunit": "^9"}, "type": "library", "extra": {"laravel": {"providers": ["Reliese\\Coders\\CodersServiceProvider"]}}, "autoload": {"psr-4": {"Reliese\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "crist<PERSON><PERSON><PERSON>@outlook.com"}], "description": "Reliese Components for Laravel Framework code generation.", "homepage": "http://cristianllanos.com", "keywords": ["laravel", "<PERSON>e"], "support": {"issues": "https://github.com/reliese/laravel/issues", "source": "https://github.com/reliese/laravel"}, "time": "2024-06-06T14:27:32+00:00"}, {"name": "sqids/sqids", "version": "0.4.1", "source": {"type": "git", "url": "https://github.com/sqids/sqids-php.git", "reference": "ff3d1214c0c2119ae4b676cc00f3abb22f61da42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sqids/sqids-php/zipball/ff3d1214c0c2119ae4b676cc00f3abb22f61da42", "reference": "ff3d1214c0c2119ae4b676cc00f3abb22f61da42", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"phpunit/phpunit": "^10.3"}, "suggest": {"ext-bcmath": "Required to use BC Math arbitrary precision mathematics (*).", "ext-gmp": "Required to use GNU multiple precision mathematics (*)."}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "autoload": {"psr-4": {"Sqids\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Generate short YouTube-looking IDs from numbers", "homepage": "https://sqids.org/php", "keywords": ["decode", "encode", "generate", "hashids", "ids", "sqids"], "support": {"issues": "https://github.com/sqids/sqids-php/issues", "source": "https://github.com/sqids/sqids-php/tree/0.4.1"}, "time": "2023-09-12T22:30:42+00:00"}, {"name": "symfony/cache", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "0ef36534694c572ff526d91c7181f3edede176e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/0ef36534694c572ff526d91c7181f3edede176e7", "reference": "0ef36534694c572ff526d91c7181f3edede176e7", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.3.6|^7.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/var-dumper": "<5.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-22T20:27:10+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "1d74b127da04ffa87aa940abe15446fa89653778"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/1d74b127da04ffa87aa940abe15446fa89653778", "reference": "1d74b127da04ffa87aa940abe15446fa89653778", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-09-25T12:52:38+00:00"}, {"name": "symfony/console", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "0d9e4eb5ad413075624378f474c4167ea202de78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/0d9e4eb5ad413075624378f474c4167ea202de78", "reference": "0d9e4eb5ad413075624378f474c4167ea202de78", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-22T20:27:10+00:00"}, {"name": "symfony/css-selector", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "ee0f7ed5cf298cc019431bb3b3977ebc52b86229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/ee0f7ed5cf298cc019431bb3b3977ebc52b86229", "reference": "ee0f7ed5cf298cc019431bb3b3977ebc52b86229", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/7c3aff79d10325257a001fcf92d991f24fc967cf", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-23T14:45:45+00:00"}, {"name": "symfony/error-handler", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "c725219bdf2afc59423c32793d5019d2a904e13a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/c725219bdf2afc59423c32793d5019d2a904e13a", "reference": "c725219bdf2afc59423c32793d5019d2a904e13a", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-22T20:27:10+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "ae9d3a6f3003a6caf56acd7466d8d52378d44fef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/ae9d3a6f3003a6caf56acd7466d8d52378d44fef", "reference": "ae9d3a6f3003a6caf56acd7466d8d52378d44fef", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "a76aed96a42d2b521153fb382d418e30d18b59df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/a76aed96a42d2b521153fb382d418e30d18b59df", "reference": "a76aed96a42d2b521153fb382d418e30d18b59df", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-23T14:45:45+00:00"}, {"name": "symfony/finder", "version": "v6.4.0", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "11d736e97f116ac375a81f96e662911a34cd50ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/11d736e97f116ac375a81f96e662911a34cd50ce", "reference": "11d736e97f116ac375a81f96e662911a34cd50ce", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-10-31T17:30:12+00:00"}, {"name": "symfony/http-client", "version": "v6.4.5", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "f3c86a60a3615f466333a11fd42010d4382a82c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/f3c86a60a3615f466333a11fd42010d4382a82c7", "reference": "f3c86a60a3615f466333a11fd42010d4382a82c7", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-foundation": "<6.3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v6.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-02T12:45:30+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "1ee70e699b41909c209a0c930f11034b93578654"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/1ee70e699b41909c209a0c930f11034b93578654", "reference": "1ee70e699b41909c209a0c930f11034b93578654", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-07-30T20:28:31+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "ebc713bc6e6f4b53f46539fc158be85dfcd77304"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/ebc713bc6e6f4b53f46539fc158be85dfcd77304", "reference": "ebc713bc6e6f4b53f46539fc158be85dfcd77304", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.3"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.3|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-08T15:01:18+00:00"}, {"name": "symfony/http-kernel", "version": "v6.4.5", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "f6947cb939d8efee137797382cb4db1af653ef75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/f6947cb939d8efee137797382cb4db1af653ef75", "reference": "f6947cb939d8efee137797382cb4db1af653ef75", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.4", "symfony/config": "<6.1", "symfony/console": "<5.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<5.4", "symfony/messenger": "<5.4", "symfony/translation": "<5.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<5.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.3", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/clock": "^6.2|^7.0", "symfony/config": "^6.1|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.5|^6.0.5|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.4|^7.0.4", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4|^6.0|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-exporter": "^6.2|^7.0", "twig/twig": "^2.13|^3.0.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v6.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-04T21:00:47+00:00"}, {"name": "symfony/mailer", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "791c5d31a8204cf3db0c66faab70282307f4376b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/791c5d31a8204cf3db0c66faab70282307f4376b", "reference": "791c5d31a8204cf3db0c66faab70282307f4376b", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.1", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.2|^7.0", "symfony/twig-bridge": "^6.2|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-03T21:33:47+00:00"}, {"name": "symfony/mime", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "5017e0a9398c77090b7694be46f20eb796262a34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/5017e0a9398c77090b7694be46f20eb796262a34", "reference": "5017e0a9398c77090b7694be46f20eb796262a34", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.3.2"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.3.2|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-30T08:32:12+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ef4d7e442ca910c4764bce785146269b30cb5fc4", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/32a9da87d7b3245e09ac426c83d334ae9f06f80f", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "a287ed7475f85bf6f61890146edbc932c0fff919"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a287ed7475f85bf6f61890146edbc932c0fff919", "reference": "a287ed7475f85bf6f61890146edbc932c0fff919", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/bc45c394692b948b4d383a08d7753968bed9a83d", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "861391a8da9a04cbad2d232ddd9e4893220d6e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/861391a8da9a04cbad2d232ddd9e4893220d6e25", "reference": "861391a8da9a04cbad2d232ddd9e4893220d6e25", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "c565ad1e63f30e7477fc40738343c62b40bc672d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/c565ad1e63f30e7477fc40738343c62b40bc672d", "reference": "c565ad1e63f30e7477fc40738343c62b40bc672d", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/86fcae159633351e5fd145d1c47de6c528f8caff", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-php80": "^1.14"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "3abdd21b0ceaa3000ee950097bc3cf9efc137853"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/3abdd21b0ceaa3000ee950097bc3cf9efc137853", "reference": "3abdd21b0ceaa3000ee950097bc3cf9efc137853", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/process", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "710e27879e9be3395de2b98da3f52a946039f297"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/710e27879e9be3395de2b98da3f52a946039f297", "reference": "710e27879e9be3395de2b98da3f52a946039f297", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-20T12:31:00+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "49cfb0223ec64379f7154214dcc1f7c46f3c7a47"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/49cfb0223ec64379f7154214dcc1f7c46f3c7a47", "reference": "49cfb0223ec64379f7154214dcc1f7c46f3c7a47", "shasum": ""}, "require": {"php": ">=8.1", "psr/http-message": "^1.0|^2.0", "symfony/http-foundation": "^5.4|^6.0|^7.0"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-kernel": "<6.2"}, "require-dev": {"nyholm/psr7": "^1.1", "php-http/discovery": "^1.15", "psr/log": "^1.1.4|^2|^3", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/framework-bundle": "^6.2|^7.0", "symfony/http-kernel": "^6.2|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "https://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/routing", "version": "v6.4.5", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "7fe30068e207d9c31c0138501ab40358eb2d49a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/7fe30068e207d9c31c0138501ab40358eb2d49a4", "reference": "7fe30068e207d9c31c0138501ab40358eb2d49a4", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<6.2", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^6.2|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v6.4.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-27T12:33:30+00:00"}, {"name": "symfony/service-contracts", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "fe07cbc8d837f60caf7018068e350cc5163681a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/fe07cbc8d837f60caf7018068e350cc5163681a0", "reference": "fe07cbc8d837f60caf7018068e350cc5163681a0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.4.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-12-26T14:02:43+00:00"}, {"name": "symfony/string", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "4e465a95bdc32f49cf4c7f07f751b843bbd6dcd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/4e465a95bdc32f49cf4c7f07f751b843bbd6dcd9", "reference": "4e465a95bdc32f49cf4c7f07f751b843bbd6dcd9", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-01T13:16:41+00:00"}, {"name": "symfony/translation", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "bce6a5a78e94566641b2594d17e48b0da3184a8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/bce6a5a78e94566641b2594d17e48b0da3184a8e", "reference": "bce6a5a78e94566641b2594d17e48b0da3184a8e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-20T13:16:58+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "06450585bf65e978026bda220cdebca3f867fde7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/06450585bf65e978026bda220cdebca3f867fde7", "reference": "06450585bf65e978026bda220cdebca3f867fde7", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.4.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-12-26T14:02:43+00:00"}, {"name": "symfony/uid", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/uid.git", "reference": "1d31267211cc3a2fff32bcfc7c1818dac41b6fc0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/uid/zipball/1d31267211cc3a2fff32bcfc7c1818dac41b6fc0", "reference": "1d31267211cc3a2fff32bcfc7c1818dac41b6fc0", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to generate and represent UIDs", "homepage": "https://symfony.com", "keywords": ["UID", "ulid", "uuid"], "support": {"source": "https://github.com/symfony/uid/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "b439823f04c98b84d4366c79507e9da6230944b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/b439823f04c98b84d4366c79507e9da6230944b1", "reference": "b439823f04c98b84d4366c79507e9da6230944b1", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-15T11:23:52+00:00"}, {"name": "symfony/var-exporter", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "0bd342e24aef49fc82a21bd4eedd3e665d177e5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/0bd342e24aef49fc82a21bd4eedd3e665d177e5b", "reference": "0bd342e24aef49fc82a21bd4eedd3e665d177e5b", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-26T08:37:45+00:00"}, {"name": "tencentcloud/common", "version": "3.0.1339", "source": {"type": "git", "url": "https://github.com/tencentcloud-sdk-php/common.git", "reference": "97fea23b090ac99798050a3dff636182ba1f9a2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tencentcloud-sdk-php/common/zipball/97fea23b090ac99798050a3dff636182ba1f9a2c", "reference": "97fea23b090ac99798050a3dff636182ba1f9a2c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.3||^7.0", "php": ">=5.6.0"}, "type": "library", "autoload": {"psr-4": {"TencentCloud\\": "./src/TencentCloud"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "ten<PERSON>clouda<PERSON>", "email": "<EMAIL>", "homepage": "https://cloud.tencent.com/document/sdk/PHP", "role": "Developer"}], "description": "TencentCloudApi php sdk", "homepage": "https://github.com/tencentcloud-sdk-php/common", "support": {"issues": "https://github.com/tencentcloud-sdk-php/common/issues", "source": "https://github.com/tencentcloud-sdk-php/common/tree/3.0.1339"}, "time": "2025-03-14T10:52:02+00:00"}, {"name": "tencentcloud/sms", "version": "3.0.1339", "source": {"type": "git", "url": "https://github.com/tencentcloud-sdk-php/sms.git", "reference": "94f5f648582c9482092aae3dd04cd868c0347248"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tencentcloud-sdk-php/sms/zipball/94f5f648582c9482092aae3dd04cd868c0347248", "reference": "94f5f648582c9482092aae3dd04cd868c0347248", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"tencentcloud/common": "3.0.1339"}, "type": "library", "autoload": {"psr-4": {"TencentCloud\\": "./src/TencentCloud"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "ten<PERSON>clouda<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/tencentcloud-sdk-php/sms", "role": "Developer"}], "description": "TencentCloudApi php sdk sms", "homepage": "https://github.com/tencentcloud-sdk-php/sms", "support": {"issues": "https://github.com/tencentcloud-sdk-php/sms/issues", "source": "https://github.com/tencentcloud-sdk-php/sms/tree/3.0.1339"}, "time": "2025-03-14T11:30:53+00:00"}, {"name": "thenorthmemory/xml", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/TheNorthMemory/xml.git", "reference": "6f50c63450a0b098772423f8bdc3c4ad2c4c30bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TheNorthMemory/xml/zipball/6f50c63450a0b098772423f8bdc3c4ad2c4c30bb", "reference": "6f50c63450a0b098772423f8bdc3c4ad2c4c30bb", "shasum": ""}, "require": {"ext-libxml": "*", "ext-simplexml": "*", "php": ">=7.1.2"}, "require-dev": {"phpstan/phpstan": "^0.12.89 || ^1.0", "phpunit/phpunit": "^7.5 || ^8.5.16 || ^9.3.5"}, "type": "library", "autoload": {"psr-4": {"TheNorthMemory\\Xml\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/TheNorthMemory"}], "description": "A wrapper of the XML parser and builder", "homepage": "https://github.com/TheNorthMemory/xml", "keywords": ["xml-builder", "xml-parser"], "support": {"issues": "https://github.com/TheNorthMemory/xml/issues", "source": "https://github.com/TheNorthMemory/xml/tree/1.1.1"}, "time": "2023-01-15T06:01:13+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "v2.2.7", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/83ee6f38df0a63106a9e4536e3060458b74ccedb", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.2.7"}, "time": "2023-12-08T13:03:43+00:00"}, {"name": "tymon/jwt-auth", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/tymondesigns/jwt-auth.git", "reference": "9c82452d93986a877ea606153125304e1104c7f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tymondesigns/jwt-auth/zipball/9c82452d93986a877ea606153125304e1104c7f7", "reference": "9c82452d93986a877ea606153125304e1104c7f7", "shasum": ""}, "require": {"illuminate/auth": "^9.0|^10.0|^11.0", "illuminate/contracts": "^9.0|^10.0|^11.0", "illuminate/http": "^9.0|^10.0|^11.0", "illuminate/support": "^9.0|^10.0|^11.0", "lcobucci/jwt": "^4.0", "nesbot/carbon": "^2.0", "php": "^8.0"}, "require-dev": {"illuminate/console": "^9.0|^10.0|^11.0", "illuminate/database": "^9.0|^10.0|^11.0", "illuminate/routing": "^9.0|^10.0|^11.0", "mockery/mockery": ">=0.9.9", "phpunit/phpunit": "^9.4"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "1.0-dev", "dev-2.x": "2.0-dev"}, "laravel": {"aliases": {"JWTAuth": "Tymon\\JWTAuth\\Facades\\JWTAuth", "JWTFactory": "Tymon\\JWTAuth\\Facades\\JWTFactory"}, "providers": ["Tymon\\JWTAuth\\Providers\\LaravelServiceProvider"]}}, "autoload": {"psr-4": {"Tymon\\JWTAuth\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://tymon.xyz", "role": "Developer"}], "description": "JSON Web Token Authentication for <PERSON><PERSON> and <PERSON><PERSON>", "homepage": "https://github.com/tymondesigns/jwt-auth", "keywords": ["Authentication", "JSON Web Token", "auth", "jwt", "laravel"], "support": {"issues": "https://github.com/tymondesigns/jwt-auth/issues", "source": "https://github.com/tymondesigns/jwt-auth"}, "funding": [{"url": "https://www.patreon.com/seantymon", "type": "patreon"}], "time": "2024-03-02T14:26:05+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.0", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4", "reference": "2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.2", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.2", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2023-11-12T22:43:29+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "b56450eed252f6801410d810c8e1727224ae0743"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b56450eed252f6801410d810c8e1727224ae0743", "reference": "b56450eed252f6801410d810c8e1727224ae0743", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/2.0.1"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2022-03-08T17:03:00+00:00"}, {"name": "w7corp/easywechat", "version": "6.15.0", "source": {"type": "git", "url": "https://github.com/w7corp/easywechat.git", "reference": "1a6d898053905f77834d8bb547613de072cdbc48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/w7corp/easywechat/zipball/1a6d898053905f77834d8bb547613de072cdbc48", "reference": "1a6d898053905f77834d8bb547613de072cdbc48", "shasum": ""}, "require": {"ext-curl": "*", "ext-fileinfo": "*", "ext-libxml": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-sodium": "*", "nyholm/psr7": "^1.5", "nyholm/psr7-server": "^1.0", "overtrue/socialite": "^3.5.4|^4.0.1", "php": ">=8.0.2", "psr/http-client": "^1.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/polyfill-php81": "^1.25", "symfony/psr-http-message-bridge": "^2.1.2|^6.4.0", "thenorthmemory/xml": "^1.0"}, "require-dev": {"brainmaestro/composer-git-hooks": "^2.8", "jetbrains/phpstorm-attributes": "^1.0", "laravel/pint": "^1.2", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.4.4", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^9.5", "symfony/var-dumper": "^5.2"}, "type": "library", "extra": {"hooks": {"pre-commit": ["composer check-style", "composer php<PERSON>", "composer test"], "pre-push": ["composer check-style"], "config": {"stop-on-failure": ["pre-commit", "pre-push"]}}}, "autoload": {"psr-4": {"EasyWeChat\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "微信SDK", "keywords": ["easywechat", "sdk", "wechat", "weixin", "weixin-sdk"], "support": {"issues": "https://github.com/w7corp/easywechat/issues", "source": "https://github.com/w7corp/easywechat/tree/6.15.0"}, "funding": [{"url": "https://github.com/overtrue", "type": "github"}], "time": "2024-01-22T01:17:07+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "yansongda/artful", "version": "v1.0.9", "source": {"type": "git", "url": "https://github.com/yansongda/artful.git", "reference": "1fbf987c0deb95a9b67eb343f2b96c8489635e6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yansongda/artful/zipball/1fbf987c0deb95a9b67eb343f2b96c8489635e6f", "reference": "1fbf987c0deb95a9b67eb343f2b96c8489635e6f", "shasum": ""}, "require": {"guzzlehttp/psr7": "^2.6", "php": ">=8.0", "psr/container": "^1.1 || ^2.0", "psr/event-dispatcher": "^1.0", "psr/http-client": "^1.0", "psr/http-message": "^1.1 || ^2.0", "psr/log": "^1.1 || ^2.0 || ^3.0", "yansongda/supports": "~4.0.9"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.44", "guzzlehttp/guzzle": "^7.0", "hyperf/pimple": "^2.2", "mockery/mockery": "^1.4", "monolog/monolog": "^2.2", "phpstan/phpstan": "^1.0.0", "phpunit/phpunit": "^9.0", "symfony/event-dispatcher": "^5.2.0", "symfony/http-foundation": "^5.2.0", "symfony/psr-http-message-bridge": "^2.1", "symfony/var-dumper": "^5.1"}, "suggest": {"hyperf/pimple": "其它/无框架下使用 SDK，请安装，任选其一", "illuminate/container": "其它/无框架下使用 SDK，请安装，任选其一"}, "type": "library", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Yansongda\\Artful\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ya<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Artful 是一个简单易用的 API 请求框架 PHP Api RequesT Framwork U Like。", "keywords": ["api", "artful", "framework", "request"], "support": {"homepage": "https://artful.yansongda.cn", "issues": "https://github.com/yansongda/artful/issues", "source": "https://github.com/yansongda/artful"}, "time": "2024-02-06T13:19:54+00:00"}, {"name": "yansongda/pay", "version": "v3.6.3", "source": {"type": "git", "url": "https://github.com/yansongda/pay.git", "reference": "fba0bb30636cb936eed9e9078d781e0dc4e4282b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yansongda/pay/zipball/fba0bb30636cb936eed9e9078d781e0dc4e4282b", "reference": "fba0bb30636cb936eed9e9078d781e0dc4e4282b", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-json": "*", "ext-libxml": "*", "ext-openssl": "*", "ext-simplexml": "*", "php": ">=8.0", "yansongda/artful": "~1.0.9", "yansongda/supports": "~4.0.9"}, "conflict": {"hyperf/framework": "<3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.44", "guzzlehttp/guzzle": "^7.0", "hyperf/pimple": "^2.2", "mockery/mockery": "^1.4", "monolog/monolog": "^2.2", "phpstan/phpstan": "^1.0.0", "phpunit/phpunit": "^9.0", "symfony/event-dispatcher": "^5.2.0", "symfony/http-foundation": "^5.2.0", "symfony/psr-http-message-bridge": "^2.1", "symfony/var-dumper": "^5.1"}, "type": "library", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Yansongda\\Pay\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ya<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "可能是我用过的最优雅的 Alipay 和 WeChat 的支付 SDK 扩展包了", "keywords": ["alipay", "pay", "wechat"], "support": {"homepage": "https://pay.yansongda.cn", "issues": "https://github.com/yansongda/pay/issues", "source": "https://github.com/yansongda/pay"}, "time": "2024-02-29T12:19:11+00:00"}, {"name": "yansongda/supports", "version": "v4.0.9", "source": {"type": "git", "url": "https://github.com/yansongda/supports.git", "reference": "c3479723be665360a5635c15f184a1d0a8dc995a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yansongda/supports/zipball/c3479723be665360a5635c15f184a1d0a8dc995a", "reference": "c3479723be665360a5635c15f184a1d0a8dc995a", "shasum": ""}, "require": {"php": ">=8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "mockery/mockery": "^1.4", "phpstan/phpstan": "^1.1.0", "phpunit/phpunit": "^9.0"}, "suggest": {"monolog/monolog": "Use logger", "symfony/console": "Use stdout logger"}, "type": "library", "autoload": {"files": ["src/Functions.php"], "psr-4": {"Yansongda\\Supports\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ya<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "common components", "keywords": ["array", "collection", "config", "support"], "support": {"issues": "https://github.com/yansongda/supports/issues", "source": "https://github.com/yansongda/supports"}, "time": "2024-01-06T05:14:33+00:00"}], "packages-dev": [{"name": "barryvdh/laravel-debugbar", "version": "v3.11.0", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-debugbar.git", "reference": "762684120b4edba3f2cd1d2c33d63a34866b6caa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-debugbar/zipball/762684120b4edba3f2cd1d2c33d63a34866b6caa", "reference": "762684120b4edba3f2cd1d2c33d63a34866b6caa", "shasum": ""}, "require": {"illuminate/routing": "^9|^10|^11", "illuminate/session": "^9|^10|^11", "illuminate/support": "^9|^10|^11", "maximebf/debugbar": "~1.21.0", "php": "^8.0", "symfony/finder": "^6|^7"}, "require-dev": {"mockery/mockery": "^1.3.3", "orchestra/testbench-dusk": "^5|^6|^7|^8|^9", "phpunit/phpunit": "^9.6|^10.5", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.10-dev"}, "laravel": {"providers": ["Barryvdh\\Debugbar\\ServiceProvider"], "aliases": {"Debugbar": "Barryvdh\\Debugbar\\Facades\\Debugbar"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Barryvdh\\Debugbar\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "PHP Debugbar integration for Laravel", "keywords": ["debug", "debugbar", "laravel", "profiler", "webprofiler"], "support": {"issues": "https://github.com/barryvdh/laravel-debugbar/issues", "source": "https://github.com/barryvdh/laravel-debugbar/tree/v3.11.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2024-03-09T08:41:41+00:00"}, {"name": "barryvdh/laravel-ide-helper", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-ide-helper.git", "reference": "bc1d67f01ce8c77e3f97d48ba51fa1d81874f622"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-ide-helper/zipball/bc1d67f01ce8c77e3f97d48ba51fa1d81874f622", "reference": "bc1d67f01ce8c77e3f97d48ba51fa1d81874f622", "shasum": ""}, "require": {"barryvdh/reflection-docblock": "^2.1.1", "composer/class-map-generator": "^1.0", "ext-json": "*", "illuminate/console": "^10 || ^11", "illuminate/database": "^10.38 || ^11", "illuminate/filesystem": "^10 || ^11", "illuminate/support": "^10 || ^11", "nikic/php-parser": "^4.18 || ^5", "php": "^8.1", "phpdocumentor/type-resolver": "^1.1.0"}, "require-dev": {"ext-pdo_sqlite": "*", "friendsofphp/php-cs-fixer": "^3", "illuminate/config": "^9 || ^10 || ^11", "illuminate/view": "^9 || ^10 || ^11", "mockery/mockery": "^1.4", "orchestra/testbench": "^8 || ^9", "phpunit/phpunit": "^10.5", "spatie/phpunit-snapshot-assertions": "^4 || ^5", "vimeo/psalm": "^5.4"}, "suggest": {"illuminate/events": "Required for automatic helper generation (^6|^7|^8|^9|^10|^11)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}, "laravel": {"providers": ["Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider"]}}, "autoload": {"psr-4": {"Barryvdh\\LaravelIdeHelper\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Laravel IDE Helper, generates correct PHPDocs for all Facade classes, to improve auto-completion.", "keywords": ["autocomplete", "codeintel", "helper", "ide", "laravel", "netbeans", "phpdoc", "phpstorm", "sublime"], "support": {"issues": "https://github.com/barryvdh/laravel-ide-helper/issues", "source": "https://github.com/barryvdh/laravel-ide-helper/tree/v3.0.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2024-03-01T12:53:18+00:00"}, {"name": "barryvdh/reflection-docblock", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/barryvdh/ReflectionDocBlock.git", "reference": "e6811e927f0ecc37cc4deaa6627033150343e597"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/ReflectionDocBlock/zipball/e6811e927f0ecc37cc4deaa6627033150343e597", "reference": "e6811e927f0ecc37cc4deaa6627033150343e597", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "^8.5.14|^9"}, "suggest": {"dflydev/markdown": "~1.0", "erusev/parsedown": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-0": {"Barryvdh": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"source": "https://github.com/barryvdh/ReflectionDocBlock/tree/v2.1.1"}, "time": "2023-06-14T05:06:27+00:00"}, {"name": "composer/class-map-generator", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/composer/class-map-generator.git", "reference": "953cc4ea32e0c31f2185549c7d216d7921f03da9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/class-map-generator/zipball/953cc4ea32e0c31f2185549c7d216d7921f03da9", "reference": "953cc4ea32e0c31f2185549c7d216d7921f03da9", "shasum": ""}, "require": {"composer/pcre": "^2.1 || ^3.1", "php": "^7.2 || ^8.0", "symfony/finder": "^4.4 || ^5.3 || ^6 || ^7"}, "require-dev": {"phpstan/phpstan": "^1.6", "phpstan/phpstan-deprecation-rules": "^1", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "symfony/filesystem": "^5.4 || ^6", "symfony/phpunit-bridge": "^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\ClassMapGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Utilities to scan PHP code and generate class maps.", "keywords": ["classmap"], "support": {"issues": "https://github.com/composer/class-map-generator/issues", "source": "https://github.com/composer/class-map-generator/tree/1.1.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-06-30T13:58:57+00:00"}, {"name": "composer/pcre", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "4775f35b2d70865807c89d32c8e7385b86eb0ace"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/4775f35b2d70865807c89d32c8e7385b86eb0ace", "reference": "4775f35b2d70865807c89d32c8e7385b86eb0ace", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.1.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-03-07T15:38:35+00:00"}, {"name": "dragon-code/pretty-array", "version": "v4.1.0", "source": {"type": "git", "url": "https://github.com/TheDragonCode/pretty-array.git", "reference": "6c84e2454491b414efbd37985c322712cdf9012f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TheDragonCode/pretty-array/zipball/6c84e2454491b414efbd37985c322712cdf9012f", "reference": "6c84e2454491b414efbd37985c322712cdf9012f", "shasum": ""}, "require": {"dragon-code/contracts": "^2.20", "dragon-code/support": "^6.11.2", "ext-dom": "*", "ext-mbstring": "*", "php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^10.2"}, "suggest": {"symfony/thanks": "Give thanks (in the form of a GitHub) to your fellow PHP package maintainers"}, "type": "library", "autoload": {"psr-4": {"DragonCode\\PrettyArray\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/andrey-helldar"}], "description": "Simple conversion of an array to a pretty view", "keywords": ["andrey helldar", "array", "dragon", "dragon code", "pretty", "pretty array"], "support": {"issues": "https://github.com/TheDragonCode/pretty-array/issues", "source": "https://github.com/TheDragonCode/pretty-array"}, "funding": [{"url": "https://boosty.to/dragon-code", "type": "boosty"}, {"url": "https://github.com/sponsors/TheDragonCode", "type": "github"}, {"url": "https://opencollective.com/dragon-code", "type": "open_collective"}, {"url": "https://yoomoney.ru/to/410012608840929", "type": "<PERSON><PERSON><PERSON>"}], "time": "2023-06-02T11:37:44+00:00"}, {"name": "fakerphp/faker", "version": "v1.23.1", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "bfb4fe148adbf78eff521199619b93a52ae3554b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/bfb4fe148adbf78eff521199619b93a52ae3554b", "reference": "bfb4fe148adbf78eff521199619b93a52ae3554b", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.23.1"}, "time": "2024-01-02T13:46:09+00:00"}, {"name": "filp/whoops", "version": "2.15.4", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "a139776fa3f5985a50b509f2a02ff0f709d2a546"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/a139776fa3f5985a50b509f2a02ff0f709d2a546", "reference": "a139776fa3f5985a50b509f2a02ff0f709d2a546", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"mockery/mockery": "^0.9 || ^1.0", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.15.4"}, "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "time": "2023-11-03T12:00:00+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "kitloong/laravel-migrations-generator", "version": "v7.0.1", "source": {"type": "git", "url": "https://github.com/kitloong/laravel-migrations-generator.git", "reference": "a21df90076f7c6c4325dded1ea7ed6d2ffb90c3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kitloong/laravel-migrations-generator/zipball/a21df90076f7c6c4325dded1ea7ed6d2ffb90c3d", "reference": "a21df90076f7c6c4325dded1ea7ed6d2ffb90c3d", "shasum": ""}, "require": {"ext-pdo": "*", "illuminate/support": "^10.43|^11.0", "php": "^8.1"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.0|^3.0", "friendsofphp/php-cs-fixer": "^3.1", "larastan/larastan": "^1.0|^2.0", "mockery/mockery": "^1.0", "orchestra/testbench": "^8.0|^9.0", "phpmd/phpmd": "^2.10", "slevomat/coding-standard": "^8.0", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"laravel": {"providers": ["KitLoong\\MigrationsGenerator\\MigrationsGeneratorServiceProvider"]}}, "autoload": {"psr-4": {"KitLoong\\MigrationsGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Generates Laravel Migrations from an existing database", "keywords": ["artisan", "generator", "laravel", "lumen", "migration", "migrations"], "support": {"issues": "https://github.com/kitloong/laravel-migrations-generator/issues", "source": "https://github.com/kitloong/laravel-migrations-generator/tree/v7.0.1"}, "funding": [{"url": "https://www.buymeacoffee.com/kitloong", "type": "custom"}], "time": "2024-03-05T14:29:34+00:00"}, {"name": "laravel-lang/actions", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-<PERSON>/actions.git", "reference": "14e163af33ff3330c8675cfcb2ffd9abd4150a91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON>-<PERSON>/actions/zipball/14e163af33ff3330c8675cfcb2ffd9abd4150a91", "reference": "14e163af33ff3330c8675cfcb2ffd9abd4150a91", "shasum": ""}, "require": {"ext-json": "*", "laravel-lang/publisher": "^14.0 || ^15.0 || ^16.0", "php": "^8.1"}, "require-dev": {"laravel-lang/status-generator": "^2.3.1", "phpunit/phpunit": "^10.0", "symfony/var-dumper": "^6.3 || ^7.0"}, "type": "library", "extra": {"laravel": {"providers": ["LaravelLang\\Actions\\ServiceProvider"]}}, "autoload": {"psr-4": {"LaravelLang\\Actions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://dragon-code.pro"}, {"name": "<PERSON><PERSON>", "homepage": "https://laravel-lang.com"}], "description": "Translation of buttons and other action elements", "keywords": ["actions", "buttons", "lang", "languages", "laravel", "translations"], "support": {"issues": "https://github.com/<PERSON><PERSON>-<PERSON>/actions/issues", "source": "https://github.com/<PERSON><PERSON>-<PERSON>/actions/tree/1.7.0"}, "time": "2024-03-29T13:01:40+00:00"}, {"name": "laravel-lang/attributes", "version": "2.10.1", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-<PERSON>/attributes.git", "reference": "b6a9227b148114ae8df5e1b24442dc847f46ebca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON>-<PERSON>/attributes/zipball/b6a9227b148114ae8df5e1b24442dc847f46ebca", "reference": "b6a9227b148114ae8df5e1b24442dc847f46ebca", "shasum": ""}, "require": {"ext-json": "*", "laravel-lang/publisher": "^14.0 || ^15.0 || ^16.0", "php": "^8.1"}, "require-dev": {"laravel-lang/status-generator": "^1.19 || ^2.0", "phpunit/phpunit": "^10.0", "symfony/var-dumper": "^6.0 || ^7.0"}, "type": "library", "extra": {"laravel": {"providers": ["LaravelLang\\Attributes\\ServiceProvider"]}}, "autoload": {"psr-4": {"LaravelLang\\Attributes\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>vel-Lang Team", "homepage": "https://github.com/<PERSON><PERSON>-<PERSON>"}], "description": "List of 126 languages for form field names", "keywords": ["attributes", "fields", "form", "lang", "languages", "laravel", "messages", "translations", "validation"], "support": {"issues": "https://github.com/<PERSON><PERSON>-<PERSON>/attributes/issues", "source": "https://github.com/<PERSON><PERSON>-<PERSON>/attributes/tree/2.10.1"}, "time": "2024-03-31T13:40:16+00:00"}, {"name": "laravel-lang/common", "version": "6.2.0", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-<PERSON>/common.git", "reference": "6dd7ec4beae3c9fa046ced5cef020d2f8a353ea5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON>-<PERSON>/common/zipball/6dd7ec4beae3c9fa046ced5cef020d2f8a353ea5", "reference": "6dd7ec4beae3c9fa046ced5cef020d2f8a353ea5", "shasum": ""}, "require": {"laravel-lang/actions": "^1.1", "laravel-lang/attributes": "^2.7", "laravel-lang/http-statuses": "^3.7", "laravel-lang/json-fallback": "^2.0", "laravel-lang/lang": "^13.12 || ^14.0 || ^15.0", "laravel-lang/locales": "^2.3", "laravel-lang/publisher": "^16.0", "php": "^8.1"}, "require-dev": {"dragon-code/support": "^6.12", "orchestra/testbench": "^8.17 || ^9.0", "phpunit/phpunit": "^10.5.3", "symfony/var-dumper": "^6.4 || ^7.0"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>vel-Lang Team", "homepage": "https://github.com/<PERSON><PERSON>-<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://dragon-code.pro"}], "description": "Easily connect the necessary language packs to the application", "keywords": ["<PERSON><PERSON><PERSON>lang", "actions", "attribute", "attributes", "breeze", "buttons", "cashier", "fortify", "framework", "http", "http-status", "http-status-code", "i18n", "jetstream", "lang", "language", "languages", "laravel", "locale", "locales", "localization", "localizations", "nova", "publisher", "spark", "translation", "translations", "ui"], "support": {"issues": "https://github.com/<PERSON><PERSON>-<PERSON>/common/issues", "source": "https://github.com/<PERSON><PERSON>-<PERSON>/common"}, "time": "2024-03-22T18:25:34+00:00"}, {"name": "laravel-lang/http-statuses", "version": "3.8.2", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-<PERSON>/http-statuses.git", "reference": "b408ea74292df2e4fbec2be93858a3e697189b29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON>-<PERSON>/http-statuses/zipball/b408ea74292df2e4fbec2be93858a3e697189b29", "reference": "b408ea74292df2e4fbec2be93858a3e697189b29", "shasum": ""}, "require": {"ext-json": "*", "laravel-lang/publisher": "^14.1 || ^15.0 || ^16.0", "php": "^8.1"}, "require-dev": {"laravel-lang/status-generator": "^1.19 || ^2.0", "phpunit/phpunit": "^10.0", "symfony/var-dumper": "^6.0 || ^7.0"}, "type": "library", "extra": {"laravel": {"providers": ["LaravelLang\\HttpStatuses\\ServiceProvider"]}}, "autoload": {"psr-4": {"LaravelLang\\HttpStatuses\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>vel-Lang Team", "homepage": "https://github.com/<PERSON><PERSON>-<PERSON>"}], "description": "List of 126 languages for HTTP statuses", "keywords": ["http", "lang", "languages", "laravel", "messages", "status", "translations"], "support": {"issues": "https://github.com/<PERSON><PERSON>-<PERSON>/http-statuses/issues", "source": "https://github.com/<PERSON><PERSON>-<PERSON>/http-statuses/tree/3.8.2"}, "time": "2024-03-11T01:33:17+00:00"}, {"name": "laravel-lang/json-fallback", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-<PERSON>/json-fallback.git", "reference": "597865ffcef81b7e92227ea73ff3a9a3372fb91a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON>-<PERSON>/json-fallback/zipball/597865ffcef81b7e92227ea73ff3a9a3372fb91a", "reference": "597865ffcef81b7e92227ea73ff3a9a3372fb91a", "shasum": ""}, "require": {"illuminate/support": "^10.0 || ^11.0", "illuminate/translation": "^10.0 || ^11.0", "php": "^8.1"}, "require-dev": {"orchestra/testbench": "^8.0 || ^9.0", "phpunit/phpunit": "^10.0"}, "type": "library", "autoload": {"psr-4": {"LaravelLang\\JsonFallback\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/andrey-helldar"}, {"name": "Felipe D<PERSON>dev", "homepage": "https://github.com/felipe-dsdev"}], "description": "Adds support for fallback JSON string translation", "support": {"issues": "https://github.com/<PERSON><PERSON>-<PERSON>/json-fallback/issues", "source": "https://github.com/<PERSON><PERSON>-<PERSON>/json-fallback/tree/2.1.0"}, "time": "2024-03-13T09:18:03+00:00"}, {"name": "laravel-lang/lang", "version": "14.7.0", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-<PERSON>/lang.git", "reference": "6b196fc8d22fc1775d8acb15059814470687b275"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON>-<PERSON>/lang/zipball/6b196fc8d22fc1775d8acb15059814470687b275", "reference": "6b196fc8d22fc1775d8acb15059814470687b275", "shasum": ""}, "require": {"ext-json": "*", "laravel-lang/publisher": "^14.0 || ^15.0 || ^16.0", "php": "^8.1"}, "require-dev": {"laravel-lang/status-generator": "^1.19 || ^2.0", "phpunit/phpunit": "^10.0", "symfony/var-dumper": "^6.0 || ^7.0"}, "type": "library", "extra": {"laravel": {"providers": ["LaravelLang\\Lang\\ServiceProvider"]}}, "autoload": {"psr-4": {"LaravelLang\\Lang\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>vel-Lang Team", "homepage": "https://github.com/<PERSON><PERSON>-<PERSON>"}], "description": "List of 126 languages for Laravel Framework, Laravel Jetstream, Laravel Fortify, <PERSON><PERSON>, <PERSON><PERSON> Cashier, Laravel Nova, Laravel Spark and Laravel UI", "keywords": ["lang", "languages", "laravel", "lpm"], "support": {"issues": "https://github.com/<PERSON><PERSON>-<PERSON>/lang/issues", "source": "https://github.com/<PERSON><PERSON>-<PERSON>/lang"}, "time": "2024-03-22T18:18:21+00:00"}, {"name": "laravel-lang/publisher", "version": "16.3.0", "source": {"type": "git", "url": "https://github.com/<PERSON><PERSON>-<PERSON>/publisher.git", "reference": "b33e0c22e8a374b453a944169a49c626914d5ad7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/<PERSON><PERSON>-<PERSON>/publisher/zipball/b33e0c22e8a374b453a944169a49c626914d5ad7", "reference": "b33e0c22e8a374b453a944169a49c626914d5ad7", "shasum": ""}, "require": {"composer/semver": "^3.4", "dragon-code/pretty-array": "^4.1", "dragon-code/support": "^6.11.3", "ext-json": "*", "illuminate/collections": "^10.0 || ^11.0", "illuminate/console": "^10.0 || ^11.0", "illuminate/support": "^10.0 || ^11.0", "laravel-lang/locales": "^2.3", "league/commonmark": "^2.4.1", "league/config": "^1.2", "php": "^8.1"}, "conflict": {"laravel-lang/attributes": "<2.0", "laravel-lang/http-statuses": "<3.0", "laravel-lang/lang": "<11.0"}, "require-dev": {"laravel-lang/json-fallback": "^2.0", "orchestra/testbench": "^8.14 || ^9.0", "phpunit/phpunit": "^10.4.2", "symfony/var-dumper": "^6.3.6 || ^7.0"}, "type": "library", "extra": {"laravel": {"providers": ["LaravelLang\\Publisher\\ServiceProvider"]}}, "autoload": {"psr-4": {"LaravelLang\\Publisher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>vel-Lang Team", "homepage": "https://laravel-lang.com"}], "description": "Publisher lang files for the Laravel and Lumen Frameworks, Jetstream, Fortify, Cashier, Spark and Nova from Laravel-Lang/lang", "keywords": ["<PERSON><PERSON><PERSON>lang", "breeze", "cashier", "fortify", "framework", "i18n", "jetstream", "lang", "languages", "laravel", "locale", "locales", "localization", "localizations", "lpm", "lumen", "nova", "publisher", "spark", "trans", "translation", "translations", "validations"], "support": {"issues": "https://github.com/<PERSON><PERSON>-<PERSON>/publisher/issues", "source": "https://github.com/<PERSON><PERSON>-<PERSON>/publisher"}, "time": "2024-03-13T09:51:30+00:00"}, {"name": "laravel/pint", "version": "v1.14.0", "source": {"type": "git", "url": "https://github.com/laravel/pint.git", "reference": "6b127276e3f263f7bb17d5077e9e0269e61b2a0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/pint/zipball/6b127276e3f263f7bb17d5077e9e0269e61b2a0e", "reference": "6b127276e3f263f7bb17d5077e9e0269e61b2a0e", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "ext-xml": "*", "php": "^8.1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.49.0", "illuminate/view": "^10.43.0", "larastan/larastan": "^2.8.1", "laravel-zero/framework": "^10.3.0", "mockery/mockery": "^1.6.7", "nunomaduro/termwind": "^1.15.1", "pestphp/pest": "^2.33.6"}, "bin": ["builds/pint"], "type": "project", "autoload": {"psr-4": {"App\\": "app/", "Database\\Seeders\\": "database/seeders/", "Database\\Factories\\": "database/factories/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An opinionated code formatter for PHP.", "homepage": "https://laravel.com", "keywords": ["format", "formatter", "lint", "linter", "php"], "support": {"issues": "https://github.com/laravel/pint/issues", "source": "https://github.com/laravel/pint"}, "time": "2024-02-20T17:38:05+00:00"}, {"name": "laravel/sail", "version": "v1.28.2", "source": {"type": "git", "url": "https://github.com/laravel/sail.git", "reference": "057777403b8ab79222dcc04983beaab10b6de6a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/sail/zipball/057777403b8ab79222dcc04983beaab10b6de6a0", "reference": "057777403b8ab79222dcc04983beaab10b6de6a0", "shasum": ""}, "require": {"illuminate/console": "^9.52.16|^10.0|^11.0", "illuminate/contracts": "^9.52.16|^10.0|^11.0", "illuminate/support": "^9.52.16|^10.0|^11.0", "php": "^8.0", "symfony/yaml": "^6.0|^7.0"}, "require-dev": {"orchestra/testbench": "^7.0|^8.0|^9.0", "phpstan/phpstan": "^1.10"}, "bin": ["bin/sail"], "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Sail\\SailServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Sail\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Docker files for running a basic Laravel application.", "keywords": ["docker", "laravel"], "support": {"issues": "https://github.com/laravel/sail/issues", "source": "https://github.com/laravel/sail"}, "time": "2024-03-04T14:58:29+00:00"}, {"name": "maximebf/debugbar", "version": "v1.21.0", "source": {"type": "git", "url": "https://github.com/maximebf/php-debugbar.git", "reference": "9700e509740daa03d909e5f7cc19f719bcbe6915"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maximebf/php-debugbar/zipball/9700e509740daa03d909e5f7cc19f719bcbe6915", "reference": "9700e509740daa03d909e5f7cc19f719bcbe6915", "shasum": ""}, "require": {"php": "^7.1|^8", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^4|^5|^6|^7"}, "require-dev": {"phpunit/phpunit": ">=7.5.20 <10.0", "twig/twig": "^1.38|^2.7|^3.0"}, "suggest": {"kriswallsmith/assetic": "The best way to manage assets", "monolog/monolog": "Log using Monolog", "predis/predis": "Redis storage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.20-dev"}}, "autoload": {"psr-4": {"DebugBar\\": "src/DebugBar/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "maxime.bouroume<PERSON>@gmail.com", "homepage": "http://maximebf.com"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Debug bar in the browser for php application", "homepage": "https://github.com/maximebf/php-debugbar", "keywords": ["debug", "debugbar"], "support": {"issues": "https://github.com/maximebf/php-debugbar/issues", "source": "https://github.com/maximebf/php-debugbar/tree/v1.21.0"}, "time": "2024-03-01T15:28:34+00:00"}, {"name": "mockery/mockery", "version": "1.6.7", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "0cc058854b3195ba21dc6b1f7b1f60f4ef3a9c06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/0cc058854b3195ba21dc6b1f7b1f60f4ef3a9c06", "reference": "0cc058854b3195ba21dc6b1f7b1f60f4ef3a9c06", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.10", "symplify/easy-coding-standard": "^12.0.8"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "time": "2023-12-10T02:24:34+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2023-03-08T13:26:56+00:00"}, {"name": "nunomaduro/collision", "version": "v7.10.0", "source": {"type": "git", "url": "https://github.com/nunomaduro/collision.git", "reference": "49ec67fa7b002712da8526678abd651c09f375b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/collision/zipball/49ec67fa7b002712da8526678abd651c09f375b2", "reference": "49ec67fa7b002712da8526678abd651c09f375b2", "shasum": ""}, "require": {"filp/whoops": "^2.15.3", "nunomaduro/termwind": "^1.15.1", "php": "^8.1.0", "symfony/console": "^6.3.4"}, "conflict": {"laravel/framework": ">=11.0.0"}, "require-dev": {"brianium/paratest": "^7.3.0", "laravel/framework": "^10.28.0", "laravel/pint": "^1.13.3", "laravel/sail": "^1.25.0", "laravel/sanctum": "^3.3.1", "laravel/tinker": "^2.8.2", "nunomaduro/larastan": "^2.6.4", "orchestra/testbench-core": "^8.13.0", "pestphp/pest": "^2.23.2", "phpunit/phpunit": "^10.4.1", "sebastian/environment": "^6.0.1", "spatie/laravel-ignition": "^2.3.1"}, "type": "library", "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "autoload": {"files": ["./src/Adapters/Phpunit/Autoload.php"], "psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["artisan", "cli", "command-line", "console", "error", "handling", "laravel", "laravel-zero", "php", "symfony"], "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2023-10-11T15:45:01+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "153ae662783729388a584b4361f2545e4d841e3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/153ae662783729388a584b4361f2545e4d841e3c", "reference": "153ae662783729388a584b4361f2545e4d841e3c", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.13"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.8.2"}, "time": "2024-02-23T11:10:43+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.26.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "231e3186624c03d7e7c890ec662b81e6b0405227"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/231e3186624c03d7e7c890ec662b81e6b0405227", "reference": "231e3186624c03d7e7c890ec662b81e6b0405227", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.26.0"}, "time": "2024-02-23T16:05:55+00:00"}, {"name": "phpunit/php-code-coverage", "version": "10.1.13", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "d51c3aec14896d5e80b354fad58e998d1980f8f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/d51c3aec14896d5e80b354fad58e998d1980f8f8", "reference": "d51c3aec14896d5e80b354fad58e998d1980f8f8", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1", "phpunit/php-file-iterator": "^4.0", "phpunit/php-text-template": "^3.0", "sebastian/code-unit-reverse-lookup": "^3.0", "sebastian/complexity": "^3.0", "sebastian/environment": "^6.0", "sebastian/lines-of-code": "^2.0", "sebastian/version": "^4.0", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^10.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "10.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.13"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-09T16:54:15+00:00"}, {"name": "phpunit/php-file-iterator", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/a95037b6d9e608ba092da1b23931e537cadc3c3c", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T06:24:48+00:00"}, {"name": "phpunit/php-invoker", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:56:09+00:00"}, {"name": "phpunit/php-text-template", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/0c7b06ff49e3d5072f057eb1fa59258bf287a748", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T14:07:24+00:00"}, {"name": "phpunit/php-timer", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/e2a2d67966e740530f4a3343fe2e030ffdc1161d", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:57:52+00:00"}, {"name": "phpunit/phpunit", "version": "10.5.12", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "41a9886b85ac7bf3929853baf96b95361cd69d2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/41a9886b85ac7bf3929853baf96b95361cd69d2b", "reference": "41a9886b85ac7bf3929853baf96b95361cd69d2b", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.1", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=8.1", "phpunit/php-code-coverage": "^10.1.5", "phpunit/php-file-iterator": "^4.0", "phpunit/php-invoker": "^4.0", "phpunit/php-text-template": "^3.0", "phpunit/php-timer": "^6.0", "sebastian/cli-parser": "^2.0", "sebastian/code-unit": "^2.0", "sebastian/comparator": "^5.0", "sebastian/diff": "^5.0", "sebastian/environment": "^6.0", "sebastian/exporter": "^5.1", "sebastian/global-state": "^6.0.1", "sebastian/object-enumerator": "^5.0", "sebastian/recursion-context": "^5.0", "sebastian/type": "^4.0", "sebastian/version": "^4.0"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "10.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/10.5.12"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2024-03-09T12:04:07+00:00"}, {"name": "sebastian/cli-parser", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/c34583b87e7b7a8055bf6c450c2c77ce32a24084", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:12:49+00:00"}, {"name": "sebastian/code-unit", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/a81fee9eef0b7a76af11d121767abc44c104e503", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/2.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:58:43+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:59:15+00:00"}, {"name": "sebastian/comparator", "version": "5.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2db5010a484d53ebf536087a70b4a5423c102372"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2db5010a484d53ebf536087a70b4a5423c102372", "reference": "2db5010a484d53ebf536087a70b4a5423c102372", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-14T13:18:12+00:00"}, {"name": "sebastian/complexity", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "68ff824baeae169ec9f2137158ee529584553799"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/68ff824baeae169ec9f2137158ee529584553799", "reference": "68ff824baeae169ec9f2137158ee529584553799", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/3.2.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:37:17+00:00"}, {"name": "sebastian/diff", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/c41e007b4b62af48218231d6c2275e4c9b975b2e", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:15:17+00:00"}, {"name": "sebastian/environment", "version": "6.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "43c751b41d74f96cbbd4e07b7aec9675651e2951"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/43c751b41d74f96cbbd4e07b7aec9675651e2951", "reference": "43c751b41d74f96cbbd4e07b7aec9675651e2951", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/6.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-04-11T05:39:26+00:00"}, {"name": "sebastian/exporter", "version": "5.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "955288482d97c19a372d3f31006ab3f37da47adf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/955288482d97c19a372d3f31006ab3f37da47adf", "reference": "955288482d97c19a372d3f31006ab3f37da47adf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.1", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/5.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:17:12+00:00"}, {"name": "sebastian/global-state", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:19:19+00:00"}, {"name": "sebastian/lines-of-code", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/856e7f6a75a84e339195d48c556f23be2ebf75d0", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:38:20+00:00"}, {"name": "sebastian/object-enumerator", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/202d0e344a580d7f7d04b3fafce6933e59dae906", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:08:32+00:00"}, {"name": "sebastian/object-reflector", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/24ed13d98130f0e7122df55d06c5c4942a577957", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:06:18+00:00"}, {"name": "sebastian/recursion-context", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "05909fb5bc7df4c52992396d0116aed689f93712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/05909fb5bc7df4c52992396d0116aed689f93712", "reference": "05909fb5bc7df4c52992396d0116aed689f93712", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:05:40+00:00"}, {"name": "sebastian/type", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/462699a16464c3944eefc02ebdd77882bd3925bf", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:10:45+00:00"}, {"name": "sebastian/version", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c51fa83a5d8f43f1402e3f32a005e6262244ef17", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-07T11:34:05+00:00"}, {"name": "spatie/backtrace", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/spatie/backtrace.git", "reference": "483f76a82964a0431aa836b6ed0edde0c248e3ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/backtrace/zipball/483f76a82964a0431aa836b6ed0edde0c248e3ab", "reference": "483f76a82964a0431aa836b6ed0edde0c248e3ab", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"ext-json": "*", "phpunit/phpunit": "^9.3", "spatie/phpunit-snapshot-assertions": "^4.2", "symfony/var-dumper": "^5.1"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Backtrace\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "A better backtrace", "homepage": "https://github.com/spatie/backtrace", "keywords": ["Backtrace", "spatie"], "support": {"source": "https://github.com/spatie/backtrace/tree/1.5.3"}, "funding": [{"url": "https://github.com/sponsors/spatie", "type": "github"}, {"url": "https://spatie.be/open-source/support-us", "type": "other"}], "time": "2023-06-28T12:59:17+00:00"}, {"name": "spatie/flare-client-php", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/spatie/flare-client-php.git", "reference": "17082e780752d346c2db12ef5d6bee8e835e399c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/flare-client-php/zipball/17082e780752d346c2db12ef5d6bee8e835e399c", "reference": "17082e780752d346c2db12ef5d6bee8e835e399c", "shasum": ""}, "require": {"illuminate/pipeline": "^8.0|^9.0|^10.0|^11.0", "php": "^8.0", "spatie/backtrace": "^1.5.2", "symfony/http-foundation": "^5.2|^6.0|^7.0", "symfony/mime": "^5.2|^6.0|^7.0", "symfony/process": "^5.2|^6.0|^7.0", "symfony/var-dumper": "^5.2|^6.0|^7.0"}, "require-dev": {"dms/phpunit-arraysubset-asserts": "^0.5.0", "pestphp/pest": "^1.20|^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "spatie/phpunit-snapshot-assertions": "^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.3.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spatie\\FlareClient\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Send PHP errors to <PERSON><PERSON><PERSON>", "homepage": "https://github.com/spatie/flare-client-php", "keywords": ["exception", "flare", "reporting", "spatie"], "support": {"issues": "https://github.com/spatie/flare-client-php/issues", "source": "https://github.com/spatie/flare-client-php/tree/1.4.4"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2024-01-31T14:18:45+00:00"}, {"name": "spatie/ignition", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/spatie/ignition.git", "reference": "5b6f801c605a593106b623e45ca41496a6e7d56d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/ignition/zipball/5b6f801c605a593106b623e45ca41496a6e7d56d", "reference": "5b6f801c605a593106b623e45ca41496a6e7d56d", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^8.0", "spatie/backtrace": "^1.5.3", "spatie/flare-client-php": "^1.4.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "require-dev": {"illuminate/cache": "^9.52|^10.0|^11.0", "mockery/mockery": "^1.4", "pestphp/pest": "^1.20|^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "psr/simple-cache-implementation": "*", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "vlucas/phpdotenv": "^5.5"}, "suggest": {"openai-php/client": "Require get solutions from OpenAI", "simple-cache-implementation": "To cache solutions from OpenAI"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.5.x-dev"}}, "autoload": {"psr-4": {"Spatie\\Ignition\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A beautiful error page for PHP applications.", "homepage": "https://flareapp.io/ignition", "keywords": ["error", "flare", "laravel", "page"], "support": {"docs": "https://flareapp.io/docs/ignition-for-laravel/introduction", "forum": "https://twitter.com/flareappio", "issues": "https://github.com/spatie/ignition/issues", "source": "https://github.com/spatie/ignition"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2024-01-03T15:49:39+00:00"}, {"name": "spatie/laravel-ignition", "version": "2.4.2", "source": {"type": "git", "url": "https://github.com/spatie/laravel-ignition.git", "reference": "351504f4570e32908839fc5a2dc53bf77d02f85e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-ignition/zipball/351504f4570e32908839fc5a2dc53bf77d02f85e", "reference": "351504f4570e32908839fc5a2dc53bf77d02f85e", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "illuminate/support": "^10.0|^11.0", "php": "^8.1", "spatie/flare-client-php": "^1.3.5", "spatie/ignition": "^1.9", "symfony/console": "^6.2.3|^7.0", "symfony/var-dumper": "^6.2.3|^7.0"}, "require-dev": {"livewire/livewire": "^2.11|^3.3.5", "mockery/mockery": "^1.5.1", "openai-php/client": "^0.8.1", "orchestra/testbench": "^8.0|^9.0", "pestphp/pest": "^2.30", "phpstan/extension-installer": "^1.2", "phpstan/phpstan-deprecation-rules": "^1.1.1", "phpstan/phpstan-phpunit": "^1.3.3", "vlucas/phpdotenv": "^5.5"}, "suggest": {"openai-php/client": "Require get solutions from OpenAI", "psr/simple-cache-implementation": "Needed to cache solutions from OpenAI"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\LaravelIgnition\\IgnitionServiceProvider"], "aliases": {"Flare": "Spatie\\LaravelIgnition\\Facades\\Flare"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spatie\\LaravelIgnition\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A beautiful error page for Laravel applications.", "homepage": "https://flareapp.io/ignition", "keywords": ["error", "flare", "laravel", "page"], "support": {"docs": "https://flareapp.io/docs/ignition-for-laravel/introduction", "forum": "https://twitter.com/flareappio", "issues": "https://github.com/spatie/laravel-ignition/issues", "source": "https://github.com/spatie/laravel-ignition"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2024-02-09T16:08:40+00:00"}, {"name": "symfony/yaml", "version": "v6.4.3", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "d75715985f0f94f978e3a8fa42533e10db921b90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/d75715985f0f94f978e3a8fa42533e10db921b90", "reference": "d75715985f0f94f978e3a8fa42533e10db921b90", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T14:51:35+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"pp/laravel-scout-elastic": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^8.1", "ext-bcmath": "*", "ext-curl": "*", "ext-fileinfo": "*", "ext-zip": "*"}, "platform-dev": [], "plugin-api-version": "2.3.0"}