<?php
namespace Deployer;

require 'recipe/common.php';

// Project name
set('application', 'heguibao-server');

// Project repository
set('repository', '***********************:heguibao/heguibao-server.git');

// [Optional] Allocate tty for git clone. Default value is false.
set('git_tty', true);

// Shared files/dirs between deploys
set('shared_files', []);
set('shared_dirs', ['storage']);

// Writable dirs by web server
set('writable_mode', 'chmod');
set('writable_chmod_mode', '0775');
set('writable_chmod_recursive', false);
set('writable_dirs', [
    'bootstrap/cache',
    'storage',
    'storage/app',
    'storage/app/public',
    'storage/app/private',
    'storage/framework',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
]);

set('clear_paths', [
    'tests',
    'package.json',
    'phpunit.xml',
    'README.md',
    'deploy.php',
    '.gitignore',
    '.gitattributes',
    '.phpstorm.meta.php',
    '.editorconfig',
    '.env.example',
    '.env.prod',
    '.env.test'
]);

set('composer_options', 'install --verbose --no-dev --optimize-autoloader');
set('default_stage', 'test');
set('fpm_sockets', '127.0.0.1:9000');
// 注意 deploy_path 必须与项目在 heguibao-php81-fpm 容器中的路径一致
set('deploy_path', '/home/<USER>/heguibao/heguibao-server');

//默认情况下是否执行数据库迁移，正常情况下会询问是否执行，可以添加 -n -o migrate_on_default=true 来默认执行数据库迁移
set('migrate_on_default', false);

//是否重启队列任务
set('restart_queue_worker', false);

// Hosts
host('prod')
    ->hostname('*************')
    ->port(8022)
    ->shellCommand('sh -s')
    ->stage('prod')
    ->user('www')
    ->set('branch', 'main')
    ->set('http_user', 'www');

host('test')
    ->hostname('***************')
    ->stage('test')
    ->set('branch', 'test')
    ->user('web')
    ->set('bin/php', '/usr/bin/php8.1')
    ->set('fpm_sockets', '/run/php/php8.1-fpm.sock');

host('dev')
    ->hostname('************')
    ->port(8022)
    ->shellCommand('sh -s')
    ->stage('dev')
    ->set('branch', 'main')
    ->user('www');

/**
 * 检查文件是否相同
 *
 * @param string $file1
 * @param string $file2
 * @return bool
 */
function isFileSame($file1, $file2) {
    return test("cmp --silent $file1 $file2");
}

desc('Prepare laravel enviroument');
task('deploy:prepare_laravel', function() {
    cd('{{release_path}}');

    run('rm -f .env');

    $stage = get('stage');
    run("cp .env.{$stage} .env");

    run('{{bin/php}} artisan optimize');

    run('{{bin/php}} artisan storage:link');

    run('{{bin/php}} artisan config:clear');

    //询问是否执行数据库迁移，默认不执行
    if (askConfirmation('Do database migrate?', get('migrate_on_default'))) {
        run('{{bin/php}} artisan migrate --force');
    }
});

desc('Installing vendors');
task('deploy:vendors', function () {
    //在前一个版本拥有相同的 composer 包时询问是否拷贝而不是重新安装
    //首先拷贝前一个版本的 vendor 目录，这样有新包时安装会更快
    if (has('previous_release') && test('[ -d {{previous_release}}/vendor ]')) {
        writeln('Copy previous vendor.');
        run('cp -R {{previous_release}}/vendor {{release_path}}/vendor');
    }

    //还没有上一个版本，或当 composer 包发生变化时更新包
    if (!has('previous_release') || !isFileSame('{{previous_release}}/composer.lock', '{{release_path}}/composer.lock')) {
        if (!commandExist('unzip')) {
            writeln('<comment>To speed up composer installation setup "unzip" command with PHP zip extension https://goo.gl/sxzFcD</comment>');
        }
        writeln('Composer install.');
        run('cd {{release_path}} && {{bin/php}} {{bin/composer}} {{composer_options}}');
    }
});

/**
 * Clear opcache cache
 */
desc('Clearing OPcode cache');
task('opcache:clear', function () {
    $deployPath = get('deploy_path');
    $fpmSockets = get('fpm_sockets');

    cd($deployPath);
    $hasCachetool = run("if [ -e $deployPath/cachetool.phar ]; then echo 'true'; fi");

    if ('true' !== $hasCachetool) {
        writeln("Downloading cachetool.phar...");
        run("curl -sO http://tools.it.chunhuahealth.com/cachetool.phar");
    }

    run("{{bin/php}} cachetool.phar opcache:reset --fcgi={$fpmSockets}");
});

desc('Upload Certs');
task('deploy:certs', function() {
    upload('storage/cert', '{{release_path}}/storage', [
        'options' => ['-L'] //-L 确保会复制软链指向的文件
    ]);
});

desc('Restart Queue Worker');
task('queue:restart', function() {
    $stage = get('stage');
    cd('{{release_path}}');
    if ($stage == 'prod') {
        //正式环境会被 s6-overlay 重新启动，所以这里只需停止即可
        run('{{bin/php}} artisan queue:daemon stop');
    } else {
        run('{{bin/php}} artisan queue:daemon restart');
    }
});

desc('Ask Restart Queue Worker');
task('queue:restart-ask', function() {
    if (askConfirmation('Restart queue worker?', get('restart_queue_worker'))) {
        invoke('queue:restart');
    }
});

//Server Source Deployer
desc('Deploy project');
task('deploy', [
    'deploy:info',
    'deploy:prepare',
    'deploy:lock',
    'deploy:release',
    'deploy:update_code',
    'deploy:shared',
    'deploy:writable',
    'deploy:vendors',
    'deploy:prepare_laravel',
    'deploy:clear_paths',
    'deploy:symlink',
    'deploy:unlock',
    'opcache:clear',
    'cleanup',
    'success'
]);

after('deploy', 'queue:restart-ask');

// [Optional] If deploy fails automatically unlock.
after('deploy:failed', 'deploy:unlock');
